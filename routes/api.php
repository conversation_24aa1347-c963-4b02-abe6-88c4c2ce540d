<?php

use App\Http\Controllers\Api\BookingController;
use App\Http\Controllers\Api\PaymentController;
use App\Http\Controllers\Api\SearchController;
use App\Http\Controllers\NewUserWalletController;
use App\Http\Middleware\ValidateFingerprint;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Carbon;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// Route::middleware('auth:api')->get('/user', function (Request $request) {
//     return $request->user();
// });
Route::middleware('api')->group(function () {

    Route::get('/server-time', function () {
    return response()->json([
        'server_time' => Carbon::now()->toDateTimeString(),
        'timezone' => config('app.timezone'),
    ]);
});
    Route::group(['middleware' => ['locale']], function () {
        Route::prefix('v1')->group(function () {
            //Email Availabilty
            Route::post('check/email/exist', [App\Http\Controllers\Api\LoginController::class, 'CheckEmailValidity']);

            //Phone Number Availabilty
            Route::post('check/phone/number/exist', [App\Http\Controllers\Api\LoginController::class, 'CheckPhoneNumberValidity']);

            // Send Phone Otp
            Route::post('sendOtp', [App\Http\Controllers\Api\LoginController::class, 'createPhoneOtpV2']);

            Route::post('sendOtpNew', [App\Http\Controllers\Api\LoginController::class, 'sendOtpNew'])
                ->middleware('throttle:otp');

            // Verify Phone Otp for New Login
            Route::post('phone/otp/verify', [App\Http\Controllers\Api\LoginController::class, 'verifyPhoneOtp']);
                // ->middleware('throttle:otp');
            Route::get('initialData', [App\Http\Controllers\Api\HomeController::class, 'initialData']);
            Route::get('search', [App\Http\Controllers\Api\SearchController::class, 'index']);

            // Sign up New Form
            Route::post('signup', [App\Http\Controllers\Api\LoginController::class, 'SignUp']);

            //automation testing api
            Route::delete('/delete/user/bookings/{phone}', [App\Http\Controllers\Api\UserController::class,'deleteReservations']);
            Route::delete('/delete/user/properties/{phone}', [App\Http\Controllers\Api\UserController::class,'deleteProperties']);
            Route::delete('/delete/user/{phone}', [App\Http\Controllers\Api\UserController::class,'permanentlyDeleteUser']);

            Route::get('home', [App\Http\Controllers\Api\HomeController::class, 'index']);
            Route::post('userregister', [App\Http\Controllers\Api\LoginController::class, 'register']);
            Route::post('login', [App\Http\Controllers\Api\LoginController::class, 'login']);
            Route::post('mobile-login', [App\Http\Controllers\Api\LoginController::class, 'mobileLogin']);
            Route::post('social-login', [App\Http\Controllers\Api\LoginController::class, 'socialLogin']);
            Route::post('create/otp/{again?}', [App\Http\Controllers\Api\LoginController::class, 'createOtp'])
                ->middleware('throttle:otp');
            Route::post('verify/otp', [App\Http\Controllers\Api\LoginController::class, 'verifyOtp'])
                ->middleware('throttle:otp');
            Route::get('google/redirect', [App\Http\Controllers\Api\LoginController::class, 'googleRedirect']);
            Route::get('googleAuthenticate', [App\Http\Controllers\Api\LoginController::class, 'handleGoogleCallback']);
            Route::get('settingApi', [App\Http\Controllers\Api\LoginController::class, 'translation']);
            Route::match(array('GET', 'POST'), 'property/get-price', [App\Http\Controllers\Api\PropertyController::class, 'getPrice']);
            Route::post('coupon/check', [App\Http\Controllers\Api\PaymentController::class, 'couponCheck']);
            Route::get('get-price', [App\Http\Controllers\Api\HomeController::class, 'getPrice']);

            Route::match(array('GET', 'POST'), 'properties/get-price', [App\Http\Controllers\Api\PropertyController::class, 'getPropertyPrice']);

            Route::post('search/result', [App\Http\Controllers\SearchController::class, 'searchResultV2']);
            Route::match(array('GET', 'POST'), 'properties/{slug}', [App\Http\Controllers\Api\PropertyController::class, 'single']);
            Route::post('property/view', [App\Http\Controllers\Api\PropertyController::class, 'updatePropertyView']);
            

            Route::post('myfatoorah/payment', [App\Http\Controllers\Api\PaymentController::class, 'myfatoorahWebhook']);
            Route::get('staahBooking/{bookingId}', [App\Http\Controllers\Api\PaymentController::class, 'staahBooking']);

            Route::post('hyperpay/payment', [App\Http\Controllers\Api\PaymentController::class, 'HyperPayWebhook']);
            Route::post('hyperbill/payment', [App\Http\Controllers\Api\PaymentController::class, 'HyperBillWebhook']);

            Route::get('myfatoorah/payment/read', [App\Http\Controllers\Api\PaymentController::class, 'paymentStatusRead']);

            Route::post('tabby-webhook', [App\Http\Controllers\TabbyController::class, 'tabbyWebHook']);

            // Receipt
            Route::get('booking/receipt', [App\Http\Controllers\Api\HomeController::class, 'receipt']);


            Route::post('/search/locations', [SearchController::class, 'searchLocations']);
            Route::post('/sync/locations', [SearchController::class, 'syncLocations']);


        });

        Route::prefix('v2')->group(function () {
            Route::get('{type}/properties/{property}/reviews', [App\Http\Controllers\Api\v2\ReviewController::class, 'list']);
            Route::post('{type}/bookings/{booking}/review', [App\Http\Controllers\Api\v2\ReviewController::class, 'store']);
            Route::post('search/result', [App\Http\Controllers\SearchController::class, 'searchResultV2']);
            Route::post('social-login', [App\Http\Controllers\Api\LoginController::class, 'socialLogin']);
            Route::match(array('GET', 'POST'), 'properties/{slug}', [App\Http\Controllers\Api\PropertyController::class, 'single']);
            Route::get('initialData', [App\Http\Controllers\Api\HomeController::class, 'initialData']);
            Route::get('cityBasedDistricts', [App\Http\Controllers\Api\HomeController::class, 'cityBasedDistricts']);
            Route::post('sendOtp', [App\Http\Controllers\Api\LoginController::class, 'createPhoneOtpV2']);
            Route::post('sendOtp2', [App\Http\Controllers\Api\LoginController::class, 'createPhoneOtpV3']);


            //My Fatoorah webhook New
            Route::post('myfatoorah/payment', [App\Http\Controllers\Api\PaymentController::class, 'OnlyfatoorahWebhook']);
        });

        Route::prefix('v3')->group(function () { // NO AUTH V3
            Route::post('search/result', [App\Http\Controllers\SearchController::class, 'searchResultV2']);
            Route::get('cityBasedDistricts', [App\Http\Controllers\Api\HomeController::class, 'cityBasedDistrictsV3']);
            Route::get('initialData', [App\Http\Controllers\Api\HomeController::class, 'initialData']);
        });

        Route::prefix('v4')->group(function () { // NO AUTH V3
            Route::post('search/result', [App\Http\Controllers\SearchController::class, 'searchResultV2']);
            Route::get('cityBasedDistricts', [App\Http\Controllers\Api\HomeController::class, 'cityBasedDistrictsV4']);
        });

        Route::prefix('v5')->group(function () { // NO AUTH V3
            // Route::post('search/result', [App\Http\Controllers\Api\SearchController::class, 'searchResultV5']);
            Route::post('search/result', [App\Http\Controllers\SearchController::class, 'searchResultV2']);
        });

        Route::prefix('v6')->group(function () { // NO AUTH V3
            // Route::post('search/result', [App\Http\Controllers\Api\SearchController::class, 'searchResultV5']);
            Route::post('search/result', [App\Http\Controllers\SearchController::class, 'apiSearchResultV4']);
        });
    });
    // Auth::routes();
    Route::group(['middleware' => ['auth:api', 'json.response', 'locale']], function () {
        Route::prefix('v1')->group(function () {


            Route::get('properties/{property}/score', [App\Http\Controllers\HostController::class, 'getScore']);
            Route::get('performance-report-old/{propertyId}', [App\Http\Controllers\HostController::class, 'performanceReportOld']);
            Route::get('performance-report/{propertyId}', [App\Http\Controllers\HostController::class, 'performanceReport']);

            // Verify Email Otp for New Login
            Route::post('email/otp/verify', [App\Http\Controllers\Api\LoginController::class, 'emailVerifyOtp'])
                ->middleware('throttle:otp');
            // Resend Email Otp
            Route::get('resend/email/resend', [App\Http\Controllers\Api\LoginController::class, 'resendEmailOtp'])
                ->middleware('throttle:otp');
            // Email Update
            Route::post('email/update', [App\Http\Controllers\Api\LoginController::class, 'updateEmail']);

            // Wishlist Create
            Route::post('create/wishlist', [App\Http\Controllers\Api\WishlistController::class, 'createWishlistName']);
            // Add or Remove Property in Existing Wishlist
            Route::post('add/property/wishlist', [App\Http\Controllers\Api\WishlistController::class, 'addPropertyInExistWishlistName']);
            // Add or Remove Property in Existing Wishlist
            Route::get('delete/wishlist/{id}', [App\Http\Controllers\Api\WishlistController::class, 'deleteWishlistByName']);
            // Get Wishlist Group
            Route::get('all/group/wishlist', [App\Http\Controllers\Api\WishlistController::class, 'getAllWishlistGroups']);
            // Get Wishlist Group
            Route::get('all/wishlist/{id}/properties', [App\Http\Controllers\Api\WishlistController::class, 'getAllWishlistProperties']);
            // Collaborator
            Route::get('collaborator', [App\Http\Controllers\Api\WishlistController::class, 'updateCollaborate']);
            // Update WIshlist By Name
            Route::post('update/wishlist', [App\Http\Controllers\Api\WishlistController::class, 'updateWishlistName']);
            // Remove Single Wishlist
            Route::post('toggle/wishlist', [App\Http\Controllers\Api\WishlistController::class, 'toggleWishlist']);
            // leave in wishlist
            Route::get('leave-wishlist', [App\Http\Controllers\Api\WishlistController::class, 'leaveWishlist']);
            // collaborate in wishlist
            Route::get('collaborators/{code}', [App\Http\Controllers\Api\WishlistController::class, 'collaborateUser']);

            // Upload license
            Route::post('upload-license', [App\Http\Controllers\Api\HomeController::class, 'uploadLicense']);

            // Account Review Page
            Route::get('host/reviews', [App\Http\Controllers\Api\HomeController::class, 'HostReview']);
            //Rating routes
            Route::post('guestrating', [App\Http\Controllers\Api\PropertyController::class, 'RatingByGuest']);
            Route::post('hostrating', [App\Http\Controllers\Api\PropertyController::class, 'RatingByHost']);
            Route::post('searchReviews', [App\Http\Controllers\Api\PropertyController::class, 'searchReviews'])->withoutMiddleware('auth:api');

            //Import Calendar
            Route::post('icalender-import', [App\Http\Controllers\Admin\CalendarController::class, 'icalendarImport']);
            Route::get('icalender-export/{id}', [App\Http\Controllers\Admin\CalendarController::class, 'icalendarExport']);

            //Sync Calendar
            Route::get('icalendar/synchronization/{id}', [App\Http\Controllers\Admin\CalendarController::class, 'icalendarSynchronization']);

            Route::get('calendar-urls/{id}', [App\Http\Controllers\CalendarController::class, 'index']);
            Route::post('calendar-urls-update/{id}', [App\Http\Controllers\CalendarController::class, 'update']);
            Route::post('calendar-urls-remove/{id}', [App\Http\Controllers\CalendarController::class, 'remove']);

            Route::post('store-cancellationpolicy', [App\Http\Controllers\Api\BookingController::class, 'CancellationPolicy']);
            Route::post('store-guestrequirement', [App\Http\Controllers\Api\BookingController::class, 'Store_GuestRequirement']);

            Route::get('/user/customerservice/messages', [App\Http\Controllers\Api\InboxController::class, 'user_support_messages']);
            // Route::post('/user/customerservice/messages',[App\Http\Controllers\Api\InboxController::class, 'send_service_message']);
            Route::post('/user/customerservice/messages', [App\Http\Controllers\Api\InboxController::class, 'send_support_message']);
            Route::post('user/card/add', [App\Http\Controllers\Api\PaymentController::class, 'addCard']);

            // WALLET CREDIT
            // Route::post('credit', [App\Http\Controllers\NewUserWalletController::class, 'credit']);

            Route::post('credit', function (Request $request) {
                // Get the current app version from settings
                $currentAppVersion = getSetting('app_version');

                // Determine which method to call based on the app version
                if (version_compare($currentAppVersion, '2.0.0', '<')) {
                    return app(NewUserWalletController::class)->credit($request);
                } else {
                    return app(NewUserWalletController::class)->creditForHyperpay($request);
                }
            });



            Route::get('payment/getway', [App\Http\Controllers\NewUserWalletController::class, 'GetPaymentGetway']);
            Route::get('walletCallback', [App\Http\Controllers\NewUserWalletController::class, 'walletCallback']);

            //Get Card
            Route::get('user/card/get', [App\Http\Controllers\Api\PaymentController::class, 'getCards']);

            //userCard Delete
            Route::get('delete/card/{id}', [App\Http\Controllers\Api\UserController::class, 'cardDelete']);
            //Make Default User Card
            Route::get('make/default/card/{id}', [App\Http\Controllers\Api\UserController::class, 'setDefaultCard']);
            //SET FCM TOKEN
            Route::post('set-fcm-token', [App\Http\Controllers\Api\UserController::class, 'setFcmToken']);
            Route::post('send-fcm-notification', [App\Http\Controllers\Api\UserController::class, 'sendNotificationrToUser']);

            //SET LANGUAGE FOR MOBILE
            Route::post('set-language', [App\Http\Controllers\Api\UserController::class, 'setLanguageForMobile']);


            //inbox of User Dashboard
            Route::get('inbox', [App\Http\Controllers\Api\InboxController::class, 'index']);
            //Toggle on Your Listing Page
            Route::post('check_list', [App\Http\Controllers\Api\PropertyController::class, 'checklisting']);


            //Reservation (Guest Upcoming Booking)
            Route::get('guest/coming/reservation', [App\Http\Controllers\Api\HomeController::class, 'guestComingReservation']);
            //Reservation (Guest Booking History)
            Route::get('guest/history/reservation', [App\Http\Controllers\Api\HomeController::class, 'guestHistoryReservation']);

            //Reservation (Guest Booking Cancelled)
            Route::get('guest/cancelled/reservation', [App\Http\Controllers\Api\HomeController::class, 'guestCancelledReservation']);
            //Reservation (Host Booking Expired)
            Route::get('guest/expired/reservation', [App\Http\Controllers\Api\HomeController::class, 'guestExpiredReservation']);
            //Reservation (Guest Booking Current)
            Route::get('guest/current/reservation', [App\Http\Controllers\Api\HomeController::class, 'guestCurrentReservation']);
            //Reservation (Host Booking Current)
            Route::get('host/current/reservation', [App\Http\Controllers\Api\HomeController::class, 'hostCurrentReservation']);
            //Reservation (Host Booking Expired)
            Route::get('host/expired/reservation', [App\Http\Controllers\Api\HomeController::class, 'hostExpiredReservation']);

            //Reservation (Host Booking Cancelled)
            Route::get('host/cancelled/reservation', [App\Http\Controllers\Api\HomeController::class, 'hostCancelledReservation']);


            //Manual Message
            Route::get('manualmsg', [App\Http\Controllers\Api\InboxController::class, 'MuanualMessage']);
            Route::get('profile', [App\Http\Controllers\Api\UserController::class, 'profileView']);

            Route::get('dashboard', [App\Http\Controllers\Api\UserController::class, 'dashboard']);
            Route::get('user/favourite', [App\Http\Controllers\Api\PropertyController::class, 'userBookmark']);
            Route::match(['get', 'post'], 'my-bookings', [App\Http\Controllers\Api\BookingController::class, 'myBookings']);
            Route::get('booking_history', [App\Http\Controllers\Api\HomeController::class, 'booking_History']);
            // Route::match(array('GET', 'POST'), 'users/profile', [App\Http\Controllers\Api\UserController::class, 'profile']);
            Route::match(array('GET', 'POST'), 'users/profile', [App\Http\Controllers\Api\UserController::class, 'profileSave']);
            Route::match(array('GET', 'POST'), 'users/profile/media', [App\Http\Controllers\Api\UserController::class, 'media']);
            Route::match(['get', 'post'], 'users/security', [App\Http\Controllers\Api\UserController::class, 'security']);

            //Reviews
            Route::match(array('GET', 'POST'), 'users/reviews', [App\Http\Controllers\Api\UserController::class, 'reviews']);
            Route::match(array('GET', 'POST'), 'users/reviews_by_you', [App\Http\Controllers\Api\UserController::class, 'reviewsByYou']);

            Route::get('get/reviews', [App\Http\Controllers\Api\UserController::class, 'getAllReviews']);

            // User verification
            Route::get('users/edit-verification', [App\Http\Controllers\Api\UserController::class, 'verification']);
            Route::get('users/confirm_email/{code?}', [App\Http\Controllers\Api\UserController::class, 'confirmEmail']);
            Route::get('users/new_email_confirm', [App\Http\Controllers\Api\UserController::class, 'newConfirmEmail']);

            //user Properties
            Route::match(array('GET', 'POST'), 'properties', [App\Http\Controllers\Api\PropertyController::class, 'userProperties']);

            //Create Property
            Route::get('create', [App\Http\Controllers\Api\PropertyController::class, 'create']);
            Route::post('store', [App\Http\Controllers\Api\PropertyController::class, 'store']);
            // Route::match(array('GET', 'POST'), 'listing/{id}/{step}', [App\Http\Controllers\Api\PropertyController::class, 'listing'])->where(['id' => '[0-9]+', 'page' => 'basics|description|location|amenities|photos|pricing|calendar|details|booking']);

            // --create property in new web
            Route::match(array('GET', 'POST'), 'property/addplace', [App\Http\Controllers\Api\PropertyController::class, 'AddPlace']);
            Route::match(array('GET', 'POST'), 'listing/{id}/{step}', [App\Http\Controllers\Api\PropertyController::class, 'AddPropertyWithSteps']);

            // Set Pictures Orders
            Route::post('change-photo-order/{id}', [App\Http\Controllers\Api\PropertyController::class, 'changePhotoOrder']);


            Route::post('editlisting/updatecover', [App\Http\Controllers\Api\PropertyController::class, 'updateCoverPhoto']);


            Route::post('/delete_photo', [App\Http\Controllers\Api\PropertyController::class, 'DeletePhoto']);



            Route::match(['get', 'post'], 'window/{id}', [App\Http\Controllers\Api\InboxController::class, 'window']);
            Route::post('messaging/booking/', [App\Http\Controllers\Api\InboxController::class, 'message']);
            Route::post('messaging/reply/', [App\Http\Controllers\Api\InboxController::class, 'messageReply']);

            //Add-Edit-Bookmark
            Route::post('add-edit-book-mark', [App\Http\Controllers\Api\PropertyController::class, 'addEditBookMark']);


            //Contact Us
            Route::post('emails/contact_us', [App\Http\Controllers\Api\EmailController::class, 'contactUs']);

            //PROPERTY RESERVATION
            // Route::post('property/reserve', [App\Http\Controllers\Api\PaymentController::class, 'propertyReservation']);
            // Route::post('property/reserve', [App\Http\Controllers\Api\PaymentController::class, 'propertyReservationForHyperpay']);

            Route::post('property/reserve', function (Request $request) {
                // Get the current app version from the settings table
                $currentAppVersion = getSetting('app_version');

                // Determine which method to call based on the app version
                if (version_compare($currentAppVersion, '2.0.0', '<')) {
                    // Call the method for Moyasar
                    return app(PaymentController::class)->propertyReservation($request);
                } else {
                    // Call the method for Hyperpay
                    return app(PaymentController::class)->propertyReservationForHyperpay($request);
                }
            });
            route::post('reserveProperty', [App\Http\Controllers\Api\PaymentController::class, 'reserveProperty']);
            //All User Transaction
            Route::get('usertransactions', [App\Http\Controllers\Api\PaymentController::class, 'userTransaction']);
            //Booking on Accpetg/Decline
            Route::get('booking/{id}', [App\Http\Controllers\Api\BookingController::class, 'index'])->where('id', '[0-9]+');

            //Accept
            Route::post('booking/accept/{id}', [App\Http\Controllers\Api\BookingController::class, 'accept']);

            //Decline
            Route::post('booking/decline/{id}', [App\Http\Controllers\Api\BookingController::class, 'decline']);

            //Cancel
            Route::get('booking/cancel/{id}', [App\Http\Controllers\Api\BookingController::class, 'cancel']);

            //Booking Payment (From Client)
            // Route::post('makepayment', [App\Http\Controllers\Api\BookingController::class, 'makePayment']);
            // Route::post('makepayment', [App\Http\Controllers\Api\BookingController::class, 'makePaymentForHyperpay']);

            Route::post('makepayment', function (Request $request) {
                // Get the current app version from settings
                $currentAppVersion = getSetting('app_version');

                // Determine which method to call based on the app version
                if (version_compare($currentAppVersion, '2.0.0', '<')) {
                    // Call the method for Moyasar
                    return app(BookingController::class)->makePayment($request);
                } else {
                    // Call the method for Hyperpay
                    return app(BookingController::class)->makePaymentForHyperpay($request);
                }
            });


            //Set Calender Price
            Route::post('setcalendarprice', [App\Http\Controllers\Api\CalendarController::class, 'calenderPriceSet']);

            //get calender of property
            Route::get('bookingcalender/{id}', [App\Http\Controllers\Api\CalendarController::class, 'calender']);

            //Upload Documents
            Route::post('documents/upload', [App\Http\Controllers\Api\UserController::class, 'documentStore']);
            Route::get('get/documents', [App\Http\Controllers\Api\UserController::class, 'getDocument']);

            //Notification
            Route::get('notifications', [App\Http\Controllers\Api\HomeController::class, 'notifications']);


            //Logout
            Route::get('mobile-logout', [App\Http\Controllers\Api\LoginController::class, 'mobileLogout']);
            Route::get('logout', [App\Http\Controllers\Api\LoginController::class, 'logout']);

            //get Promo Code
            Route::get('get/promocode', [App\Http\Controllers\Api\PromoCodeController::class, 'getPromoCodes']);
            //Promo Code Routes
            Route::post('promocode/insert', [App\Http\Controllers\Api\PromoCodeController::class, 'store']);
            Route::get('promo/generate', [App\Http\Controllers\Api\PromoCodeController::class, 'generateCode']);
            Route::post('promocode/update/{id}', [App\Http\Controllers\Api\PromoCodeController::class, 'update']);
            Route::get('promocode/delete/{id}', [App\Http\Controllers\Api\PromoCodeController::class, 'delete']);

            //Ticket Routes
            Route::post('ticket/create', [App\Http\Controllers\Api\TicketController::class, 'create_ticket']);
            Route::get('ticket/list', [App\Http\Controllers\Api\TicketController::class, 'ticket_list']);
            Route::match(array('GET', 'POST'), 'ticket/details/{ticketid}', [App\Http\Controllers\Api\TicketController::class, 'ticket_detail']);


            //Promo Code On Properties
            Route::post('promocode/properties', [App\Http\Controllers\Api\PromoCodeController::class, 'promoCodeProperties']);
            Route::post('promocode/property/update/{id}', [App\Http\Controllers\Api\PromoCodeController::class, 'updatePromoCodeProperties']);
            Route::get('promocode/property/delete/{id}', [App\Http\Controllers\Api\PromoCodeController::class, 'deletePromoCodeProperties']);

            Route::post('bookings/{booking}/remove/coupon', [App\Http\Controllers\BookingController::class, 'removePromo']);

            //User Data Combined
            Route::get('user-data', [App\Http\Controllers\Api\HomeController::class, 'userData']);
            Route::get('host-data', [App\Http\Controllers\Api\HomeController::class, 'hostData']);
            Route::post('host-agreement', [App\Http\Controllers\Api\HomeController::class, 'hostAgreement']);

            Route::post('elm/verify', [App\Http\Controllers\Api\ElmDocController::class, 'verify']);

            //  Bank Account Create
            Route::post('create/bank_account', [App\Http\Controllers\Api\BankController::class, 'createBankAccount']);

            Route::get('bookings/{code}/generate/qr', [App\Http\Controllers\TestController::class, 'generateQr']);

            // Account Manager
            Route::get('account/manager/list', [App\Http\Controllers\Api\HomeController::class, 'listAccountManager']);
            Route::post('create/account/manager/', [App\Http\Controllers\Api\HomeController::class, 'createAccountManager']);
            Route::post('edit/account/manager/{id}', [App\Http\Controllers\Api\HomeController::class, 'editAccountManager']);
            Route::get('account/manager/delete/{id}', [App\Http\Controllers\Api\HomeController::class, 'deleteAccountManager']);
            Route::any('account/delete', [App\Http\Controllers\AccountDeleteRequestController::class, 'deleteAccountApi']);

            // Host Screens Api
            Route::post('checking-out', [App\Http\Controllers\Api\HostReservationController::class, 'hostCheckingOut']);
            Route::post('ongoing-bookings', [App\Http\Controllers\Api\HostReservationController::class, 'hostOngoing']);
            Route::post('ariving-soon', [App\Http\Controllers\Api\HostReservationController::class, 'hostArivingBooking']);
            Route::post('pending-reviews', [App\Http\Controllers\Api\HostReservationController::class, 'hostPendingReviews']);
            Route::post('upcoming-bookings', [App\Http\Controllers\Api\HostReservationController::class, 'hostUpcomingBooking']);
            Route::post('history-bookings', [App\Http\Controllers\Api\HostReservationController::class, 'hostHistoryBooking']);
            Route::post('expired-bookings', [App\Http\Controllers\Api\HostReservationController::class, 'hostExpiredBooking']);
            Route::post('pending-bookings', [App\Http\Controllers\Api\HostReservationController::class, 'hostPendingBooking']);
            Route::post('cancelled-bookings', [App\Http\Controllers\Api\HostReservationController::class, 'hostCancelledBooking']);
            Route::post('all_reservation', [App\Http\Controllers\Api\HostReservationController::class, 'allReservation']);
            Route::get('booking/{code}', [App\Http\Controllers\Api\BookingController::class, 'bookingDetail']);
            Route::get('chat/booking/{code}', [App\Http\Controllers\Api\BookingController::class, 'chatBookingDetail']);
            Route::get('host-property/{slug}', [App\Http\Controllers\Api\HostReservationController::class, 'singleProperty']);
            Route::get('host/all/properties', [App\Http\Controllers\Api\HostReservationController::class, 'hostProperties']);
            // Route::post('host/all-listing', [App\Http\Controllers\Api\HostReservationController::class, 'hostList']);

            Route::match(['get', 'post'], 'host/all-listing', [App\Http\Controllers\Api\HostReservationController::class, 'hostList']);
            Route::post('host/request-management', [App\Http\Controllers\Api\HostReservationController::class, 'requestManagement']);

            Route::get('properties/{property}/{type}/{user}/reservation-detail', [App\Http\Controllers\Api\v3\PropertyChatController::class, 'getReservationDetails']);
            Route::get('properties/{id}/calendar', [App\Http\Controllers\Api\PropertyController::class, 'getPricesByProperty']);
            Route::post('properties/{id}/calendar/save', [App\Http\Controllers\Api\PropertyController::class, 'setCalendarProp']);
            Route::get('properties/{type}/{entity_type}/{entity_id}/details', [App\Http\Controllers\Api\v3\PropertyChatController::class, 'getReservationDetails'])
                ->where(['type' => 'host|guest', 'entity_type' => 'bookings|property-inquiries']);
            // update Property
            Route::post('update/property/{id}', [App\Http\Controllers\Api\HostReservationController::class, 'updateProperty']);
            Route::get('delete/cohost', [App\Http\Controllers\HostListingController::class, 'deleteCoHost']);

            // Edit Profile Routes
            Route::post('managehost/personal_info', [App\Http\Controllers\HostProfileController::class, 'personal_info']);
            Route::post('managehost/global_preferences', [App\Http\Controllers\HostProfileController::class, 'global_preferences']);
        });


        Route::prefix('v2')->group(function () {
            // User Data Api Version 2
            Route::get('user-data', [App\Http\Controllers\Api\HomeController::class, 'userData']);

            // Host Data Version 2
            Route::get('host-data', [App\Http\Controllers\Api\HomeController::class, 'hostData']);

            Route::get('generateReferalLink/{code}', [App\Http\Controllers\Api\HostReferalCodeController::class, 'generateReferalLink']);

            Route::get('user/favourite', [App\Http\Controllers\Api\PropertyController::class, 'userBookmark']);
            //inbox of User Dashboard
            Route::get('inbox', [App\Http\Controllers\Api\InboxController::class, 'index']);
            //Chat Window
            Route::match(['get', 'post'], 'window/{id}', [App\Http\Controllers\Api\InboxController::class, 'window']);
            Route::match(array('GET', 'POST'), 'properties', [App\Http\Controllers\Api\PropertyController::class, 'userProperties']);
            Route::get('complete-properties', [App\Http\Controllers\Api\PropertyController::class, 'completeProperties']);
            Route::get('incomplete-properties', [App\Http\Controllers\Api\PropertyController::class, 'incompleteProperties']);

            //Notification
            Route::get('notifications', [App\Http\Controllers\Api\HomeController::class, 'notifications']);

            //get calender of property
            Route::get('bookingcalender/{id}', [App\Http\Controllers\Api\CalendarController::class, 'calender']);


            Route::match(['get', 'post'], 'my-bookings', [App\Http\Controllers\Api\BookingController::class, 'myBookings']);

            Route::match(array('GET', 'POST'), 'listing/{id}/{step}', [App\Http\Controllers\Api\PropertyController::class, 'editPropertyWithAdminApprovalRequired'])
                ->where(['id' => '[0-9]+', 'step' => 'description|photos|title']);
            Route::post('coupon/check', [App\Http\Controllers\Api\PaymentController::class, 'couponCheck']);

            // Property Chat Modul
            Route::get('{type}/properties/chats', [App\Http\Controllers\Api\PropertyChatController::class, 'propertyChatHeads']);
            Route::get('{type}/properties/chats/{chat}', [App\Http\Controllers\Api\PropertyChatController::class, 'propertyChatHeadById']);
            Route::post('{type}/properties/chats/{chat}/messages', [App\Http\Controllers\Api\PropertyChatController::class, 'sendPropertyChatMessage']);
            Route::get('{type}/properties/chats/{chat}/messages', [App\Http\Controllers\Api\PropertyChatController::class, 'propertyChatsByChatHead']);
            Route::post('properties/{property}/inquiry', [App\Http\Controllers\Api\PropertyChatController::class, 'sendPropertyInquiry']);
            Route::get('{type}/properties/chats/{chat}/details', [App\Http\Controllers\Api\PropertyChatController::class, 'getChatHeadDetails']);

            // Property Review Module
            Route::post('{type}/bookings/{booking}/review', [App\Http\Controllers\Api\v2\ReviewController::class, 'store']);


            // Host Screens
            Route::post('upcoming-bookings', [App\Http\Controllers\Api\HostReservationController::class, 'hostUpcomingBooking']);

            // Guest Enhanced Screen

            //Reservation (Guest Upcoming Booking)
            Route::get('guest/coming/reservation', [App\Http\Controllers\Api\HomeController::class, 'guestComingReservation']);
            //Reservation (Guest Booking History)
            Route::get('guest/history/reservation', [App\Http\Controllers\Api\HomeController::class, 'guestHistoryReservation']);

            Route::get('guest/expired/reservation', [App\Http\Controllers\Api\HomeController::class, 'guestExpiredReservation']);

            //Reservation (Guest Booking Cancelled)
            Route::get('guest/cancelled/reservation', [App\Http\Controllers\Api\HomeController::class, 'guestCancelledReservation']);
            Route::get('guest/current/reservation', [App\Http\Controllers\Api\HomeController::class, 'guestCurrentReservation']);


            // Account Review Page version 3 for host reviews
            Route::get('host/reviews', [App\Http\Controllers\Api\HomeController::class, 'HostReview']);

            // Upload License
            Route::post('upload-license/{id}', [App\Http\Controllers\PropertyController::class, 'uploadLicense']);
        });

        Route::group(['prefix' => 'v3'], function () {
            // Property Chat Modul
            Route::get('{type}/properties/chats', [App\Http\Controllers\Api\v3\PropertyChatController::class, 'propertyChatHeads']);
            Route::get('{type}/properties/chats/{chat}', [App\Http\Controllers\Api\v3\PropertyChatController::class, 'propertyChatHeadById']);
            Route::post('{type}/properties/chats/{chat}/messages', [App\Http\Controllers\Api\v3\PropertyChatController::class, 'sendPropertyChatMessage']);
            Route::get('{type}/properties/chats/{chat}/messages', [App\Http\Controllers\Api\v3\PropertyChatController::class, 'propertyChatsByChatHead'])->name('v3.api.paginated.property.chats');
            Route::post('properties/{property}/inquiry', [App\Http\Controllers\Api\v3\PropertyChatController::class, 'sendPropertyInquiry']);
            Route::get('{type}/properties/chats/{chat}/details', [App\Http\Controllers\Api\v3\PropertyChatController::class, 'getChatHeadDetails']);

            Route::match(array('GET', 'POST'), 'listing/{id}/{step}', [App\Http\Controllers\Api\PropertyController::class, 'editPropertyWithAdminApprovalRequired'])
                ->where(['id' => '[0-9]+', 'step' => 'description|photos|title']);
            
            Route::post('upload-license/{id}', [App\Http\Controllers\PropertyController::class, 'uploadLicenseV3']);
        });
    });

    // THIRD PARTY PROVIDER
    Route::prefix('v1')->group(function () {
        Route::match(array('GET', 'POST'), 'webhook/properties-details/{code}', [App\Http\Controllers\Api\PropertyController::class, 'singleWebhook']);
        Route::post('webhook/login', [App\Http\Controllers\Api\LoginController::class, 'loginWebhook']);
        Route::post('webhook/property/reserve', [App\Http\Controllers\Api\PaymentController::class, 'propertyReservationHook'])->withoutMiddleware('auth:api');
    });

    if (strtolower(config('app.env')) == 'local') {
        Route::get('user/{id}/get/token', function ($userId) {
            $user = \App\Models\User::findOrFail($userId);
            return response()->json($user->createToken('API Token')->accessToken);
        });
    }
});
Route::post('tabby-webhook', [App\Http\Controllers\TabbyController::class, 'tabbyWebHook']);

Route::post('/unifonicwebhook', [App\Http\Controllers\TestController::class, 'unifonicwebhook']);

Route::get('/test/pusher', [App\Http\Controllers\TestController::class, 'puhserTest']);

Route::post('bookings/payout/hook', [App\Http\Controllers\Admin\PayoutsController::class, 'checkHook']);
Route::post('silkhaus/calendar/webhook', [App\Http\Controllers\Api\PaymentController::class, 'SilkhausWebHook']);
Route::get('silkhaus/import/calender/{id}/{status}', [App\Http\Controllers\Api\PaymentController::class, 'CheckAvailability']);
Route::get('silkhaus/accesstoken', [App\Http\Controllers\Api\PaymentController::class, 'SilkhausAccessToken']);
Route::get('mabbat/accesstoken', [App\Http\Controllers\Api\PaymentController::class, 'MabbatAccessToken']);
Route::get('kease/webhook', [App\Http\Controllers\KeaseWebhookController::class, 'keaseWebHook']);

