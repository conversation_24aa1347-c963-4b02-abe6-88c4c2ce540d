<?php

use App\Models\User;
use Illuminate\Support\Facades\Route;

Route::group(['middleware' => ['locale']], function () {
    if(str_contains(config('app.url'), '.test')){
        Route::get('login-as-user', function(){
            $user = User::query()->inRandomOrder()->first();
            auth()->login($user);
            return redirect()->route('home');
        })->name('login.as.user');
    }

    // Property single page
    Route::get('{lang?}/properties/{slug}/v0', [App\Http\Controllers\V0\PropertyController::class, 'single'])->name('property.v0.single')
        ->where('lang', '^(en|ar)?$');

    // V0 API endpoints
    Route::post('v0/property/price', [App\Http\Controllers\V0\PropertyController::class, 'calculatePrice'])->name('property.v0.price');
    Route::post('v0/wishlist/toggle', [App\Http\Controllers\V0\PropertyController::class, 'toggleWishlist'])->name('wishlist.v0.toggle');
    Route::get('v0/property/reviews', [App\Http\Controllers\V0\PropertyController::class, 'loadReviews'])->name('property.v0.reviews');
    Route::get('v0/property/reviews/load', [App\Http\Controllers\V0\PropertyController::class, 'loadReviews'])->name('property.v0.reviews.load');
    Route::get('v0/property/similar', [App\Http\Controllers\V0\PropertyController::class, 'loadSimilarProperties'])->name('property.v0.similar');
    Route::post('v0/contact/host', [App\Http\Controllers\V0\PropertyController::class, 'contactHost'])->name('contact.v0.host');
    Route::post('v0/contact/track', [App\Http\Controllers\V0\PropertyController::class, 'trackContact'])->name('contact.v0.track');
    Route::post('v0/booking/track', [App\Http\Controllers\V0\PropertyController::class, 'trackBooking'])->name('booking.v0.track');
    Route::post('v0/property/view', [App\Http\Controllers\V0\PropertyController::class, 'updatePropertyView'])->name('property.v0.view');
    Route::post('v0/coupon/check', [App\Http\Controllers\V0\PropertyController::class, 'checkCoupon'])->name('coupon.v0.check');
    Route::post('v0/session/set', [App\Http\Controllers\V0\PropertyController::class, 'setSessionValue'])->name('session.v0.set');

    // Apple Pay routes
    Route::post('v0/apple-pay/validate', [App\Http\Controllers\V0\PropertyController::class, 'validateApplePay'])->name('apple.pay.v0.validate');
    Route::post('v0/apple-pay/process', [App\Http\Controllers\V0\PropertyController::class, 'processApplePay'])->name('apple.pay.v0.process');

    // Tabby routes
    Route::post('v0/tabby/create-session', [App\Http\Controllers\V0\PropertyController::class, 'createTabbySession'])->name('tabby.v0.create.session');
});
