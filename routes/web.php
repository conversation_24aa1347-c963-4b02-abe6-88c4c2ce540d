<?php

use App\Http\Controllers\Admin\CalendarController;
use App\Http\Controllers\TestController;
use App\Http\Middleware\ValidateFingerprint;
use App\Http\Services\CampaignService;
use App\Http\Services\HyperBillService;
use App\Http\Services\WalletService;
use App\Jobs\CashbackExpiryJob;
use App\Jobs\DistributeCampaignCredits;
use App\Jobs\LicenseVerificationWarningNotification;
use App\Jobs\OptimizePropertyImages;
use App\Models\Bookings;
use App\Models\Campaign;
use App\Models\NewUserWallet;
use App\Models\PhotosTemp;
use App\Models\Properties;
use App\Providers\FCMService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Session;
use App\Http\Controllers\RiyadhAppartmentApartmentController;
/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

if (app()->environment('local')) {
    require_once 'test.php';
}

require_once 'admin.php';

Route::get('/command', function () {
    Artisan::call('optimize:clear');

    return response()->json([
        'message' => 'Optimization command executed successfully!',
        'output' => Artisan::output()
    ]);
});

Route::get('process/payment', [App\Http\Controllers\PaymentController::class, 'ProcessPayment'])->name('ProcessPayment');

Route::post('view/property', [App\Http\Controllers\PropertyController::class, 'viewProperty'])->name('view.property.ajax');

Route::get('mobile/payment', [App\Http\Controllers\PaymentController::class, 'MobileProcessPayment'])->name('MobileProcessPayment');
Route::get('check', function (Request $request) {
    $b = Bookings::first();
    $data = $request->data;
    // return $data;
    $data = (new App\Http\Services\HyperpaySdkService())->checkout(2, 2, $b, route('result'));
    return view('check2', compact('data'));
})->name('checksdk');


Route::get('HyperpaySdkService', function (Request $request) {
    // return 123;
    $b = Bookings::first();
    // return $b;
    // $data = $request->data;
    // $data = (new App\Http\Services\HyperpaySdkService())->checkout(11, $b->total_with_discount ?: $b->total, $b, route('result'));
    $data = (new App\Http\Services\HyperpaySdkService())->checkout(11, 1, $b, route('result'));
    // return $data;
    return view('check2', compact('data'));
})->name('HyperpaySdkService');


Route::get('capturePayment', function (Request $request) {
    // return 123;
    $b = Bookings::where('id', $request->id)->first();
    // return $b;
    // $data = $request->data;
    // $data = (new App\Http\Services\HyperpaySdkService())->checkout(11, $b->total_with_discount ?: $b->total, $b, route('result'));
    $data = (new App\Http\Services\HyperpaySdkService())->capturePayment($b->transaction_id, $b->total_with_discount ?: $b->total);
    return $data;
    // return view('check2', compact('data'));
})->name('capturePayment');






Route::get('updateUserWallet', function (Request $request) {
    $userId = $request->input('user_id');
    $previousBookingTotal = $request->input('previous_booking_total');
    $newBookingTotal = $request->input('new_booking_total');

    $userWallet = NewUserWallet::where('user_id', $userId)->first();
    $amountAddedToUserWallet = 0;
    if ($previousBookingTotal > $newBookingTotal) {
        $amountAddedToUserWallet = $previousBookingTotal - $newBookingTotal;
        $userWallet->balance += $amountAddedToUserWallet;
    } elseif ($newBookingTotal > $previousBookingTotal) {
        $amountToMinusFromUserWallet = $newBookingTotal - $previousBookingTotal;
        $userWallet->balance -= $amountToMinusFromUserWallet;
    } else {
    }

    return response()->json(['status' => 'success', 'message' => 'Wallet updated successfully', 'data' => $userWallet], 200);
})->name('updateUserWallet');

Route::get('result', function (Request $request) {
    if (!!$request->id && !!$request->resourcePath) {
        return (new App\Http\Services\HyperpaySdkService())->getDetails($request->id);
    }
})->name('result');
Route::get('/insert_keys', [App\Http\Controllers\Admin\LanguageController::class, 'AddKeywordsInDbFromFile']);


Route::get('tabby-webhook', [App\Http\Controllers\TabbyController::class, 'tabbyWebHook'])->name('tabby_webhook');

Route::get('/session-key-remove', function () {
    // Specify the key you want to forget
    $key = 'address';

    // Use the forget method to remove the specified key from the session
    Session::forget($key);
});


Route::get('update-session-location', [App\Http\Controllers\TestController::class, 'setLatLongInSession'])->name('setLatLong');

Route::get('booking/{id}/payment/status', [App\Http\Controllers\Admin\BookingsController::class, 'getPaymentStatus'])->name('booking.payment.status');

// translte descriptions
Route::get('translate', [App\Http\Controllers\PropertyController::class, 'translate'])->name('trans.englishnArabic');

Route::get('/hostPerformanceScore', [App\Http\Controllers\TestController::class, 'hostPerformanceScore']);
Route::get('/guestReviewScore', [App\Http\Controllers\TestController::class, 'guestReviewScore']);
Route::get('/listingQualityScore', [App\Http\Controllers\TestController::class, 'listingQualityScore']);
// Route::get('/tawuniya/cron', [App\Http\Controllers\TestController::class, 'tawuniyaCron']);
Route::get('/hostpay/{bid}', [App\Http\Controllers\TestController::class, 'payableToHost']);
// Route::get('/createWalletForExistingUsers', [App\Http\Controllers\TestController::class, 'createWalletForExistingUsers']);

Route::get('set-all/review/scores', [App\Http\Controllers\TestController::class, 'setUpReviewScore']);


Route::get('cronjob-6RmF&Z15s7cV', [App\Http\Controllers\HomeController::class, 'cronjob_send_notification']);
Route::get('walletWithdrawal', [App\Http\Controllers\UserTestController::class, 'walletWithdrawal']);


Route::get('destoarabic', [App\Http\Controllers\HomeController::class, 'des_to_arabic']);
Route::get('getcitynstate', [App\Http\Controllers\HomeController::class, 'get_cities_and_states']);

Route::post('set_session', [App\Http\Controllers\HomeController::class, 'setSession']);

Route::group(['prefix' => 'admin', 'middleware' => ['guest:admin', 'locale']], function () {

    Route::get('/run-updatename-seeder', function () {
        if (auth('admin')->user()->id == 1) {
            Artisan::call('db:seed', [
                '--class' => 'UpdatenameArSeeder',
            ]);

            return 'Seeder executed successfully!';
        }
        return "you are not authorized user";
    });
    Route::get('/', function () {
        return Redirect::to('admin/dashboard');
    });
    // Setting Url On Admin
    // Route::get('settings/all', [App\Http\Controllers\Admin\SpaceTypeController::class, 'all_settings']);
    // Route::get('settings', [App\Http\Controllers\Admin\LogController::class, 'index'])->name('admin.log');

    // Route::get('logs', [App\Http\Controllers\Admin\LogController::class, 'index'])->name('admin.log');
    Route::get('platform-logs', [App\Http\Controllers\Admin\PlatformLogController::class, 'index'])->name('admin.platform.log');

    Route::get('populate/keys', [App\Http\Controllers\Admin\LanguageController::class, 'populatekeysFromMessages'])->name('admin.populateKey');
    Route::match(['GET', 'POST'], 'create/translation/key', [App\Http\Controllers\Admin\LanguageController::class, 'createTransKeyword'])->name('admin.create.translation_key');

    Route::get('set_session', [App\Http\Controllers\Admin\LanguageController::class, 'setSession'])->name('admin.setSession');
    //PromoCode
    Route::get('dropdownPopulate', [App\Http\Controllers\Admin\PromoController::class, 'dropdownPopulate'])->name('populateDropdown');
    Route::get('promocodes', [App\Http\Controllers\Admin\PromoController::class, 'index'])->middleware(['permission:manage_bookings']);
    Route::match(['GET', 'POST'], 'promocodes/add', [App\Http\Controllers\Admin\PromoController::class, 'promoCode'])->middleware(['permission:manage_bookings'])->name('addPromoCode');
    Route::match(['GET', 'POST'], 'edit/promocode/{id}', [App\Http\Controllers\Admin\PromoController::class, 'updatePromoCode'])->middleware(['permission:manage_bookings'])->name('updatePromoCode');
    Route::get('promocode/delete/{id}', [App\Http\Controllers\Admin\PromoController::class, 'deletePromoCode'])->middleware(['permission:manage_bookings']);
    Route::get('promo/generate', [App\Http\Controllers\PromoController::class, 'generateCode'])->name('admin.promo.generate');

    //Tickets
    Route::match(['get', 'post'], 'ticket/add', [App\Http\Controllers\Admin\TicketController::class, 'create_ticket'])->name('admin.add.ticket');
    Route::get('tickets', [App\Http\Controllers\Admin\TicketController::class, 'index'])->name('admin.list.ticket');
    Route::match(['GET', 'POST'], 'ticket-detail/{id}', [App\Http\Controllers\Admin\TicketController::class, 'ticket_detail'])->name('admin.ticket.edit');
    Route::get('ticket-closed/{id}', [App\Http\Controllers\Admin\TicketController::class, 'ticket_closed'])->name('admin.ticket.closed');
    Route::match(['GET', 'POST'], 'ticket-discussion/{id}', [App\Http\Controllers\Admin\TicketController::class, 'ticket_discussion'])->name('admin.ticket.discussion');
    Route::get('ticket/populate', [App\Http\Controllers\Admin\TicketController::class, 'ticketPopulate'])->name('addTicketDropdown');

    // Property Chat Module
    Route::get('properties/chats', [App\Http\Controllers\Admin\PropertyChatController::class, 'propertyChatHeads'])->name('admin.property.chat.heads');
    Route::get('properties/chats/{chat}/message', [App\Http\Controllers\Admin\PropertyChatController::class, 'propertyChatMessages'])->name('admin.property.chat.messages');
    Route::get('properties/chats/delete/message/{id}', [App\Http\Controllers\Admin\PropertyChatController::class, 'propertyChatDeleteMessages'])->name('admin.property.chat.delete.messages');

    Route::get('properties/{property}/logs', [App\Http\Controllers\Admin\PropertiesController::class, 'logs'])->name('admin.property.logs');
    Route::get('properties/{property}/logs/{log}', [App\Http\Controllers\Admin\PropertiesController::class, 'log'])->name('admin.property.logs.show');
    Route::get('properties/{property}/admins', [App\Http\Controllers\Admin\PropertiesController::class, 'admins'])->name('admin.property.admins');
    Route::post('properties/{property}/admins/{admin}/toggle', [App\Http\Controllers\Admin\PropertiesController::class, 'toggleAdmin'])->name('admin.property.admins.toggle');

    // Country Module
    Route::resource('countries', App\Http\Controllers\Admin\CountryController::class, ['as' => 'admin']);
    // City Module
    Route::resource('cities', App\Http\Controllers\Admin\CityController::class, ['as' => 'admin']);

    Route::resource('property-management-requests', App\Http\Controllers\Admin\PropertyManagementRequestController::class, ['as' => 'admin']);
    // District Module
    Route::resource('districts', App\Http\Controllers\Admin\DistrictController::class, ['as' => 'admin']);
    //Agent Tickets
    Route::get('agent/ticket', [App\Http\Controllers\Admin\AgentTicketController::class, 'agentTicket'])->name('admin.agent.tickets');
    Route::match(['GET', 'POST'], 'agent/ticket-discussion/{id}', [App\Http\Controllers\Admin\AgentTicketController::class, 'agentTicketDiscussion'])->name('admin.agent.ticket.discussion');

    // Booking Report Module
    Route::group(['prefix' => 'booking-report'], function () {
        Route::get('/', [App\Http\Controllers\Admin\BookingReportController::class, 'index'])->name('admin.booking-report.index');
        Route::get('report_pdf', [App\Http\Controllers\Admin\BookingReportController::class, 'bookingReportPdf']);
        Route::get('report_csv', [App\Http\Controllers\Admin\BookingReportController::class, 'bookingReportCSV']);
        Route::get('guest_host_search/{type}', [App\Http\Controllers\Admin\BookingReportController::class, 'searchGuestOrHost'])->middleware(['permission:manage_bookings'])->name('admin.booking-report.searchGuestOrHost');
        Route::get('search_property_code', [App\Http\Controllers\Admin\BookingReportController::class, 'searchPropertyCode'])->middleware(['permission:manage_bookings'])->name('admin.booking-report.searchPropertyCode');
        Route::get('detail/{id}', [App\Http\Controllers\Admin\BookingReportController::class, 'searchGuestOrHost'])->middleware(['permission:manage_bookings']);
        Route::get('fetchBookingStatus/{id}', [App\Http\Controllers\Admin\BookingReportController::class, 'fetchBookingStatus'])->name('fetchBookingStatus');
    });

    // Delete Requests
    Route::group(['prefix' => 'delete-requests'], function () {
        Route::get('/', [App\Http\Controllers\AccountDeleteRequestController::class, 'index'])->name('admin.delete-requests.index');
        Route::get('/{action}/{id}', [App\Http\Controllers\AccountDeleteRequestController::class, 'approveOrDelete'])->name('admin.delete-requests.action');
    });

    // Campaign Pages
    Route::group(['prefix' => 'campaign-pages'], function () {
        Route::get('/', [App\Http\Controllers\Admin\CampaignController::class, 'index'])->name('admin.campaign-pages.index');
        Route::get('/create', [App\Http\Controllers\Admin\CampaignController::class, 'create'])->name('admin.campaign-pages.create');
        Route::post('/store', [App\Http\Controllers\Admin\CampaignController::class, 'store'])->name('admin.campaign-pages.store');
        Route::get('/edit/{id}', [App\Http\Controllers\Admin\CampaignController::class, 'edit'])->name('admin.campaign-pages.edit');
        Route::post('/update', [App\Http\Controllers\Admin\CampaignController::class, 'update'])->name('admin.campaign-pages.update');
        Route::get('/delete/{id}', [App\Http\Controllers\Admin\CampaignController::class, 'delete'])->name('admin.campaign-pages.delete');
    });

    // New User Wallet Module
    Route::group(['prefix' => 'wallets'], function () {
        Route::get('/', [App\Http\Controllers\Admin\NewUserWalletController::class, 'index'])->name('admin.wallets.index');
        Route::get('/edit/{id}', [App\Http\Controllers\Admin\NewUserWalletController::class, 'edit'])->name('admin.wallets.edit');
        Route::post('/update', [App\Http\Controllers\Admin\NewUserWalletController::class, 'update'])->name('admin.wallets.update');
        Route::get('/fetchUsers', [App\Http\Controllers\Admin\NewUserWalletController::class, 'fetchUsers'])->name('admin.wallets.fetchUsers');
        Route::get('/logs', [App\Http\Controllers\Admin\NewUserWalletController::class, 'logs'])->name('admin.wallets.logs');
    });

    // Transactions Module
    Route::group(['prefix' => 'transactions'], function () {
        Route::get('/', [App\Http\Controllers\Admin\TransactionController::class, 'index'])->name('admin.transactions.index');
    });

    // Campaign Pages
    Route::group(['prefix' => 'scoring'], function () {
        Route::get('/', [App\Http\Controllers\Admin\ScoringController::class, 'index'])->name('admin.scoring.index');
    });

    //Property Discounts
    Route::get('prop-discounts', [App\Http\Controllers\Admin\PropertiesController::class, 'getDiscounts'])->middleware(['permission:manage_bookings']);
    Route::match(['GET', 'POST'], 'prop-discount/add', [App\Http\Controllers\Admin\PropertiesController::class, 'addDiscount'])->middleware(['permission:manage_bookings'])->name('addDiscount');

    //Translations Key Words
    Route::match(['GET', 'POST'], 'translation/keywords/add', [App\Http\Controllers\Admin\LanguageController::class, 'AddTransKeyWords'])->middleware(['permission:translation'])->name('addtranskeywords');

    Route::get('host_properties', [App\Http\Controllers\Admin\BookingsController::class, 'propertiesExceptHost'])->name('propertiesExceptHost');

    Route::match(['get', 'post'], 'property/book/{id?}', [App\Http\Controllers\Admin\PaymentController::class, 'createBooking']);

    Route::post('ajax-calender/{id}', [App\Http\Controllers\Admin\CalendarController::class, 'calenderJson']);

    Route::post('ajax-calender-price/{id}', [App\Http\Controllers\Admin\CalendarController::class, 'calenderPriceSet']);
    Route::match(array('GET', 'POST'), 'profile', [App\Http\Controllers\Admin\AdminController::class, 'profile']);
    Route::get('logout', [App\Http\Controllers\Admin\AdminController::class, 'logout']);
    Route::get('dashboard', [App\Http\Controllers\Admin\DashboardController::class, 'index'])->name('admin.dashboard');
    Route::get('customers', [App\Http\Controllers\Admin\CustomerController::class, 'index'])->middleware(['permission:customers']);
    Route::get('customers/customer_search', [App\Http\Controllers\Admin\CustomerController::class, 'searchCustomer'])->middleware(['permission:customers']);
    Route::post('add-ajax-customer', [App\Http\Controllers\Admin\CustomerController::class, 'add'])->middleware(['permission:add_customer']);
    Route::post('add-ajax-customer-booking', [App\Http\Controllers\Admin\CustomerController::class, 'add'])->middleware(['permission:add_customer']);
    Route::post('notify-user', [App\Http\Controllers\Admin\CustomerController::class, 'notifyUser'])->name('notifyuser');
    Route::match(array('GET', 'POST'), 'add-customer', [App\Http\Controllers\Admin\CustomerController::class, 'add'])->middleware(['permission:add_customer'])->name('admin.add-customers');
    Route::get('account-manager/{id}', [App\Http\Controllers\Admin\CustomerController::class, 'hostAccountManager'])->name('admin.host.account_manager')->middleware(['permission:customers']);
    Route::match(['get', 'post'], 'edit/account-manager/{id}', [App\Http\Controllers\Admin\CustomerController::class, 'updateAccountManager'])->name('admin.update.account_manager')->middleware(['permission:customers']);


    Route::group(['middleware' => 'permission:edit_customer'], function () {

        Route::match(array('GET', 'POST'), 'edit-customer/{id}', [App\Http\Controllers\Admin\CustomerController::class, 'update']);
        Route::match(array('GET', 'POST'), 'block-customer/{id}', [App\Http\Controllers\Admin\CustomerController::class, 'blockUser']);
        Route::match(array('GET', 'POST'), 'verify-customer/{id}', [App\Http\Controllers\Admin\CustomerController::class, 'verify_customer']);
        Route::get('customer/properties/{id}', [App\Http\Controllers\Admin\CustomerController::class, 'customerProperties']);
        Route::get('customer/bookings/{id}', [App\Http\Controllers\Admin\CustomerController::class, 'customerBookings']);
        Route::get('customer/reservations/{id}', [App\Http\Controllers\Admin\CustomerController::class, 'customerReservations']);
        Route::post('customer/bookings/property_search', [App\Http\Controllers\Admin\CustomerController::class, 'searchProperty']);
        Route::get('customer/payouts/{id}', [App\Http\Controllers\Admin\CustomerController::class, 'customerPayouts']);
        Route::get('customer/payment-methods/{id}', [App\Http\Controllers\Admin\CustomerController::class, 'paymentMethods']);
        Route::match(array('GET', 'POST'), 'customer/bank-account/{id}', [App\Http\Controllers\Admin\CustomerController::class, 'bankAccountDetail'])->name("customer.bankAccount");

        Route::get('customer/wallet/{id}', [App\Http\Controllers\Admin\CustomerController::class, 'customerWallet']);

        Route::get('customer/elm/{id}', [App\Http\Controllers\Admin\CustomerController::class, 'customerElm']);
        Route::post('customer/elm/{id}', [App\Http\Controllers\Admin\CustomerController::class, 'customerElm'])->name("customer.elm.store");
        Route::delete('customer/elm/{id}', [App\Http\Controllers\Admin\CustomerController::class, 'customerElm'])->name("customer.elm.destroy");

        Route::get('customer/properties/{id}/property_list_csv', [App\Http\Controllers\Admin\PropertiesController::class, 'propertyCsv']);
        Route::get('customer/properties/{id}/property_list_pdf', [App\Http\Controllers\Admin\PropertiesController::class, 'propertyPdf']);

        Route::get('customer/bookings/{id}/booking_list_csv', [App\Http\Controllers\Admin\BookingsController::class, 'bookingCsv']);
        Route::get('customer/bookings/{id}/booking_list_pdf', [App\Http\Controllers\Admin\BookingsController::class, 'bookingPdf']);

        Route::get('customer/payouts/{id}/payouts_list_pdf', [App\Http\Controllers\Admin\PayoutsController::class, 'payoutsPdf']);
        Route::get('customer/payouts/{id}/payouts_list_csv', [App\Http\Controllers\Admin\PayoutsController::class, 'payoutsCsv']);

        Route::get('customer/customer_list_csv', [App\Http\Controllers\Admin\CustomerController::class, 'customerCsv']);
        Route::get('customer/customer_list_pdf', [App\Http\Controllers\Admin\CustomerController::class, 'customerPdf']);
    });

    Route::group(['middleware' => 'permission:manage_messages'], function () {
        Route::get('messages', [App\Http\Controllers\Admin\AdminController::class, 'customerMessage']);
        Route::match(array('GET', 'POST'), 'delete-message/{id}', [App\Http\Controllers\Admin\AdminController::class, 'deleteMessage']);
        Route::match(array('GET', 'POST'), 'send-message-email/{id}', [App\Http\Controllers\Admin\AdminController::class, 'sendEmail']);
        Route::match(array('GET', 'POST'), 'upload_image', [App\Http\Controllers\Admin\AdminController::class, 'uploadImage'])->name('upload');
        Route::get('messaging/host/{id}', [App\Http\Controllers\Admin\AdminController::class, 'hostMessage']);
        Route::get('properties/chat/{id}', [App\Http\Controllers\Admin\AdminController::class, 'propertyMessage']);

        Route::post('reply/{id}', [App\Http\Controllers\Admin\AdminController::class, 'reply']);
    });

    Route::match(array('GET', 'POST'), 'properties', [App\Http\Controllers\Admin\PropertiesController::class, 'index']);
    Route::post('properties/update-badge', [App\Http\Controllers\Admin\PropertiesController::class, 'updatePropertyBadge'])->name('admin.property.badge.update');
    Route::get('property/detail/{id}', [App\Http\Controllers\Admin\PropertiesController::class, 'getPropertyDetails'])->name('admin.property.detail');
    Route::get('properties/property-step', [App\Http\Controllers\Admin\PropertiesController::class, 'property_with_property_step'])->name('admin.properties.propertystep')->middleware(['permission:edit_properties']);

    // Route::get('approval-changes-list', [App\Http\Controllers\Admin\PropertiesController::class, 'index']);

    // Import Property Priorities
    // Route::get('properties/manual-sorting', [App\Http\Controllers\Admin\PropertiesController::class, 'importPropertyPrioritiesView'])->name('admin.properties.proirities.import');
    // Route::post('properties/manual-sorting', [App\Http\Controllers\Admin\PropertiesController::class, 'importPropertyPriorities'])->name('admin.properties.proirities.import');

    Route::get('approval-changes-list', [App\Http\Controllers\Admin\PropertiesController::class, 'index']);
    Route::match(array('GET', 'POST'), 'changes/{id}/{page}', [App\Http\Controllers\Admin\PropertiesController::class, 'seechanges'])->where(['id' => '[0-9]+', 'page' => 'photos|description']);


    Route::get('status-change-property/{id}/{visibility}', [App\Http\Controllers\Admin\PropertiesController::class, 'statusProperty']);
    Route::post('status-change-property/unlist', [App\Http\Controllers\Admin\PropertiesController::class, 'unlist'])->name('admin.property.unlist');

    Route::get('property_calender/{id}', [App\Http\Controllers\Admin\PropertiesController::class, 'propertyCalender']);
    Route::match(array('GET', 'POST'), 'property_booking/{id}', [App\Http\Controllers\Admin\PropertiesController::class, 'propertyBooking'])->name('property_booking');
    Route::match(array('GET', 'POST'), 'add-properties', [App\Http\Controllers\Admin\PropertiesController::class, 'add'])->middleware(['permission:add_properties']);

    Route::get('properties/property_list_csv', [App\Http\Controllers\Admin\PropertiesController::class, 'propertyCsv']);
    Route::get('properties/property_list_pdf', [App\Http\Controllers\Admin\PropertiesController::class, 'propertyPdf']);

    Route::group(['middleware' => 'permission:edit_properties'], function () {
        Route::match(array('GET', 'POST'), 'listing/{id}/photo_message', [App\Http\Controllers\Admin\PropertiesController::class, 'photoMessage']);
        Route::match(array('GET', 'POST'), 'listing/{id}/photo_delete', [App\Http\Controllers\Admin\PropertiesController::class, 'photoDelete']);
        Route::match(array('POST'), 'listing/photo_rotate', [App\Http\Controllers\Admin\PropertiesController::class, 'photoRotate']);
        Route::match(array('POST'), 'listing/{id}/update_status', [App\Http\Controllers\Admin\PropertiesController::class, 'update_status']);
        Route::match(array('POST'), 'listing/photo/make_default_photo', [App\Http\Controllers\Admin\PropertiesController::class, 'makeDefaultPhoto']);
        Route::match(array('POST'), 'listing/photo/change_photo_serial', [App\Http\Controllers\Admin\PropertiesController::class, 'changeSerial'])->name('changeSerial');
        Route::match(array('GET', 'POST'), 'listing/{id}/{step}', [App\Http\Controllers\Admin\PropertiesController::class, 'listing'])->where([
            'id' => '[0-9]+',
            'page'
            => 'basics|description|location|amenities|photos|pricing|calendar|details|booking|nightsandtime'
        ]);
        Route::post('/delete_photo', [App\Http\Controllers\Admin\PropertiesController::class, 'DeletePhoto'])->name('admin.delete_photo');

        Route::get('/update/property/{property_id}/permit-by-darent/{permit}', [App\Http\Controllers\Admin\PropertiesController::class, 'updatePermit'])->name('admin.property.update.permit_by_darent');
        Route::post('/update/property/permit-by-darent', [App\Http\Controllers\Admin\PropertiesController::class, 'updatePermitAjax'])->name('admin.update.permit_by_darent');
    });

    // Route::post('ajax-calender/{id}', 'CalendarController@calenderJson');
    // Route::post('ajax-calender-price/{id}', 'CalendarController@calenderPriceSet');
    //iCalender routes for admin
    Route::post('ajax-icalender-import/{id}', [App\Http\Controllers\Admin\CalendarController::class, 'icalendarImport']);
    // Route::get('ajax-icalender-export/{id}', [App\Http\Controllers\Admin\CalendarController::class, 'icalendarExport'])->name('icalendarExport');

    Route::get('/icalimport/edit/{id}', [CalendarController::class, 'edit'])->name('icalimport.edit');
    Route::post('/icalimport/update/{id}', [CalendarController::class, 'update'])->name('icalimport.update');
    Route::get('/icalimport/remove/{id}', [CalendarController::class, 'remove'])->name('icalimport.remove');


    Route::get('icalendar/synchronization/{id}', [App\Http\Controllers\Admin\CalendarController::class, 'icalendarSynchronization']);
    //iCalender routes end
    Route::match(array('GET', 'POST'), 'edit_property/{id}', [App\Http\Controllers\Admin\PropertiesController::class, 'update'])->middleware(['permission:edit_properties']);
    Route::get('delete-property/{id}', [App\Http\Controllers\Admin\PropertiesController::class, 'delete'])->middleware(['permission:delete_property']);
    Route::post('reject-property', [App\Http\Controllers\Admin\PropertiesController::class, 'rejectProperty'])->middleware(['permission:delete_property']);


    Route::post('/check-certificate', [App\Http\Controllers\Admin\BookingsController::class, 'checkCertificate'])->name('admin.checkCertificate');


    Route::get('bookings', [App\Http\Controllers\Admin\BookingsController::class, 'index'])->middleware(['permission:manage_bookings'])->name('admin.bookings');
    Route::get('tawuniya', [App\Http\Controllers\Admin\BookingsController::class, 'tawuniya_display'])->middleware(['permission:manage_bookings'])->name('admin.tawuniya');
    Route::get('tawuniya/create-qoute/{booking_id}', [App\Http\Controllers\Admin\BookingsController::class, 'againCreateQoutation'])->middleware(['permission:manage_bookings'])->name('admin.tawuniya.againcreateqoutation');
    Route::get('tawuniya/activate-certificate/{booking_id}', [App\Http\Controllers\Admin\BookingsController::class, 'activateCertificate'])->middleware(['permission:manage_bookings'])->name('admin.tawuniya.activatecertificate');
    Route::match(array('GET', 'POST'), 'add-bookings', [App\Http\Controllers\Admin\BookingsController::class, 'add'])->name('addBooking');
    Route::get('add-bookings/search/user', [App\Http\Controllers\Admin\BookingsController::class, 'searchUser'])->name('booking.search.user');
    Route::get('bookings/property_search', [App\Http\Controllers\Admin\BookingsController::class, 'searchProperty'])->middleware(['permission:manage_bookings']);
    Route::get('shift_booking/{booking_id}', [App\Http\Controllers\Admin\BookingsController::class, 'shiftBooking'])->middleware(['permission:manage_bookings']);
    Route::post('shift_booking/apply-promo-code', [App\Http\Controllers\Admin\BookingsController::class, 'applyShiftBookingPromo'])->name('admin.apply.promo-code')->middleware(['permission:manage_bookings']);
    Route::post('shift_booking/price', [App\Http\Controllers\Admin\BookingsController::class, 'shiftBookingGetPrice'])->name('admin.shift_booking.get_price')->middleware(['permission:manage_bookings']);
    Route::post('bookings/shift', [App\Http\Controllers\Admin\BookingsController::class, 'shiftBookingToProperty'])->name('ShiftBookingToProperty')->middleware(['permission:manage_bookings']);


    Route::get('bookings/customer_search', [App\Http\Controllers\Admin\BookingsController::class, 'searchCustomer'])->middleware(['permission:manage_bookings']);
    Route::get('bookings/invoices_phone', [App\Http\Controllers\Admin\BookingsController::class, 'invoicesPhone'])->middleware(['permission:manage_bookings']);
    Route::get('bookings/allBookings', [App\Http\Controllers\Admin\BookingsController::class, 'allBookings'])->middleware(['permission:manage_bookings']);
    Route::get('bookings/invoices_email', [App\Http\Controllers\Admin\BookingsController::class, 'invoicesEmail'])->middleware(['permission:manage_bookings']);
    Route::get('bookings/invoices_creator', [App\Http\Controllers\Admin\BookingsController::class, 'invoicesCreator'])->middleware(['permission:manage_bookings']);
    Route::get('customers/bookings/data', [App\Http\Controllers\Admin\BookingsController::class, 'bookingWiseCustomerData'])->name('bookings.customerData')->middleware(['permission:manage_bookings']);

    Route::match(['get', 'post'], 'payments/book/{id?}', [App\Http\Controllers\Admin\PaymentController::class, 'createBooking']);

    //booking details
    Route::get('bookings/detail/{id}', [App\Http\Controllers\Admin\BookingsController::class, 'details'])->middleware(['permission:manage_bookings']);
    Route::get('bookings/{id}/audits', [App\Http\Controllers\Admin\BookingsController::class, 'audits'])->middleware(['permission:manage_bookings']);
    Route::get('bookings/delete/{id}', [App\Http\Controllers\Admin\BookingsController::class, 'delete'])->middleware(['permission:manage_bookings']);
    Route::get('bookings/cancel/{id}', [App\Http\Controllers\Admin\BookingsController::class, 'cancel'])->middleware(['permission:manage_bookings']);
    Route::get('bookings/cashback/{id}', [App\Http\Controllers\Admin\BookingsController::class, 'cashback'])->middleware(['permission:manage_bookings']);
    Route::get('bookings/insurance/{id}', [App\Http\Controllers\Admin\BookingsController::class, 'insurance'])->middleware(['permission:manage_bookings']);
    Route::get('edit/booking/{id}', [App\Http\Controllers\Admin\BookingsController::class, 'editBookings'])->middleware(['permission:manage_bookings']);
    Route::post('booking_extend', [App\Http\Controllers\Admin\BookingsController::class, 'extendedPrice'])->middleware(['permission:manage_bookings'])->name('bookingExtend');
    Route::post('bookings/update-forced-total', [App\Http\Controllers\Admin\BookingsController::class, 'updateForcedTotal'])->middleware(['permission:manage_bookings'])->name('updateForcedTotal');
    Route::post('extend_booking_confirm', [App\Http\Controllers\Admin\BookingsController::class, 'extendBookingConfirm'])->middleware(['permission:manage_bookings'])->name('extendBookingConfirm');
    Route::post('extend-booking-confirm/update', [App\Http\Controllers\Admin\BookingsController::class, 'extendBookingDetailsConfirm'])->middleware(['permission:manage_bookings'])->name('admin.update.booking.details');
    Route::get('bookings/edit/{req}/{id}', [App\Http\Controllers\Admin\BookingsController::class, 'updateBookingStatus'])->middleware(['permission:manage_bookings']);
    Route::post('bookings/update-status', [App\Http\Controllers\Admin\BookingsController::class, 'foreUpdateBookingStatus'])->middleware(['permission:manage_bookings'])->name('admin.booking.status.force');
    Route::post('verify/refund', [App\Http\Controllers\Admin\BookingsController::class, 'verifyRefund'])->name('verifyRefund')->middleware(['permission:manage_bookings']);

    /* ------------------------------------------------------MOYASAR INVOICE--------------------------------------------------------------------------------- */
    Route::get('bookings/invoices', [App\Http\Controllers\Admin\BookingsController::class, 'invoices'])->middleware(['permission:manage_bookings'])->name('admin.invoices.index');
    Route::any('bookings/invoices/create', [App\Http\Controllers\Admin\BookingsController::class, 'generateInvoice'])->middleware(['permission:manage_bookings'])->name('admin.invoices.generate');
    /* ------------------------------------------------------MOYASAR INVOICE--------------------------------------------------------------------------------- */

    Route::get('bookings/insurance', [App\Http\Controllers\Admin\BookingsController::class, 'tawuniyaInsurance'])->middleware(['permission:manage_bookings'])->name('admin.insurance.index');

    Route::post('bookings/pay', [App\Http\Controllers\Admin\BookingsController::class, 'pay'])->middleware(['permission:manage_bookings']);

    Route::get('booking/need_pay_account/{id}/{type}', [App\Http\Controllers\Admin\BookingsController::class, 'needPayAccount']);

    Route::get('booking/booking_list_csv', [App\Http\Controllers\Admin\BookingsController::class, 'bookingCsv']);
    Route::get('bookings/booking_list_pdf', [App\Http\Controllers\Admin\BookingsController::class, 'bookingPdf']);

    Route::get('payouts', [App\Http\Controllers\Admin\PayoutsController::class, 'index'])->name('payouts.index')->middleware(['permission:view_payouts']);
    Route::match(array('GET', 'POST'), 'payouts/edit/{id}', [App\Http\Controllers\Admin\PayoutsController::class, 'edit']);

    Route::get('payouts/details/{id}', [App\Http\Controllers\Admin\PayoutsController::class, 'details']);
    Route::post('bookings/payment/{id}/make/payouts', [App\Http\Controllers\Admin\PayoutsController::class, 'makePayout'])->name('admin.make.payout');

    Route::get('payouts/payouts_list_pdf', [App\Http\Controllers\Admin\PayoutsController::class, 'payoutsPdf']);
    Route::get('payouts/payouts_list_csv', [App\Http\Controllers\Admin\PayoutsController::class, 'payoutsCsv']);

    Route::group(['middleware' => 'permission:manage_reviews'], function () {
        Route::get('reviews', [App\Http\Controllers\Admin\ReviewsController::class, 'index'])->name('admin.review_list');
        Route::get('edit_review/{id}', [App\Http\Controllers\Admin\ReviewsController::class, 'edit'])->name("admin.edit-review");;
        Route::put('update-review/{id}', [App\Http\Controllers\Admin\ReviewsController::class, 'update'])
            ->name("admin.update-review");

        Route::match(array('GET', 'POST'), 'addReviewAdmin', [App\Http\Controllers\Admin\ReviewsController::class, 'fakeReview'])->name('addReviewAdmin');
        Route::get('reviews/review_search', [App\Http\Controllers\Admin\ReviewsController::class, 'searchReview']);
        Route::get('reviews/review_list_csv', [App\Http\Controllers\Admin\ReviewsController::class, 'reviewCsv']);
        Route::get('reviews/review_list_pdf', [App\Http\Controllers\Admin\ReviewsController::class, 'reviewPdf']);
    });

    // Route::get('reports', [App\Http\Controllers\Admin\ReportsController::class,'index'])->middleware(['permission:manage_reports']);

    // For Reporting
    Route::group(['middleware' => 'permission:view_reports'], function () {
        Route::get('sales-report', [App\Http\Controllers\Admin\ReportsController::class, 'salesReports']);
        Route::get('sales-analysis', [App\Http\Controllers\Admin\ReportsController::class, 'salesAnalysis']);
        Route::get('reports/property-search', [App\Http\Controllers\Admin\ReportsController::class, 'searchProperty']);
        Route::get('overview-stats', [App\Http\Controllers\Admin\ReportsController::class, 'overviewStats']);
    });

    Route::group(['middleware' => 'permission:manage_amenities'], function () {
        Route::get('amenities', [App\Http\Controllers\Admin\AmenitiesController::class, 'index']);
        Route::match(array('GET', 'POST'), 'add-amenities', [App\Http\Controllers\Admin\AmenitiesController::class, 'add']);
        Route::match(array('GET', 'POST'), 'edit-amenities/{id}', [App\Http\Controllers\Admin\AmenitiesController::class, 'update']);
        Route::get('delete-amenities/{id}', [App\Http\Controllers\Admin\AmenitiesController::class, 'delete']);
    });

    Route::group(['middleware' => 'permission:manage_pages'], function () {
        Route::get('pages', [App\Http\Controllers\Admin\PagesController::class, 'index']);
        Route::get('contact-listing', [App\Http\Controllers\Admin\PagesController::class, 'contact_us_listing']);
        Route::get('status-change-contact/{id}/{statusdata}', [App\Http\Controllers\Admin\PagesController::class, 'statusContact']);
        Route::match(array('GET', 'POST'), 'add-page', [App\Http\Controllers\Admin\PagesController::class, 'add']);
        Route::match(array('GET', 'POST'), 'edit-page/{id}', [App\Http\Controllers\Admin\PagesController::class, 'update']);
        Route::get('delete-page/{id}', [App\Http\Controllers\Admin\PagesController::class, 'delete']);
    });


    Route::group(['middleware' => 'permission:manage_admin'], function () {
        Route::get('admin-users', [App\Http\Controllers\Admin\AdminController::class, 'index'])->name('adminindex');
        Route::match(array('GET', 'POST'), 'add-admin', [App\Http\Controllers\Admin\AdminController::class, 'add']);
        Route::match(array('GET', 'POST'), 'edit-admin/{id}', [App\Http\Controllers\Admin\AdminController::class, 'update']);
        Route::match(array('GET', 'POST'), 'delete-admin/{id}', [App\Http\Controllers\Admin\AdminController::class, 'delete']);
    });


    Route::group(['middleware' => 'permission:manage_metas'], function () {
        Route::get('settings/metas', [App\Http\Controllers\Admin\MetasController::class, 'index'])->name('admin.metaList');
        Route::match(array('GET', 'POST'), 'settings/create_meta', [App\Http\Controllers\Admin\MetasController::class, 'add'])->name('admin.createMeta');
        Route::match(array('GET', 'POST'), 'settings/edit_meta/{id}', [App\Http\Controllers\Admin\MetasController::class, 'update']);
    });

    Route::group(['middleware' => 'permission:general_setting'], function () {
        Route::match(array('GET', 'POST'), 'settings', [App\Http\Controllers\Admin\SettingsController::class, 'general'])->middleware(['permission:general_setting']);
        Route::match(array('GET', 'POST'), 'settings/preferences', [App\Http\Controllers\Admin\SettingsController::class, 'preferences'])->middleware(['permission:preference']);
        Route::post('settings/delete_logo', [App\Http\Controllers\Admin\SettingsController::class, 'deleteLogo']);
        Route::post('settings/delete_favicon', [App\Http\Controllers\Admin\SettingsController::class, 'deleteFavIcon']);
        Route::match(array('GET', 'POST'), 'settings/fees', [App\Http\Controllers\Admin\SettingsController::class, 'fees'])->middleware(['permission:manage_fees']);

        Route::group(['middleware' => 'permission:manage_banners'], function () {
            Route::get('settings/banners', [App\Http\Controllers\Admin\BannersController::class, 'index']);
            Route::match(array('GET', 'POST'), 'settings/banners/add-banners', [App\Http\Controllers\Admin\BannersController::class, 'add']);
            Route::match(array('GET', 'POST'), 'settings/banners/edit-banners/{id}', [App\Http\Controllers\Admin\BannersController::class, 'update']);
            Route::get('settings/banners/delete-banners/{id}', [App\Http\Controllers\Admin\BannersController::class, 'delete']);
        });

        Route::group(['middleware' => 'permission:starting_cities_settings'], function () {
            Route::get('settings/starting-cities', [App\Http\Controllers\Admin\StartingCitiesController::class, 'index']);
            Route::match(array('GET', 'POST'), 'settings/add-starting-cities', [App\Http\Controllers\Admin\StartingCitiesController::class, 'add']);
            Route::match(array('GET', 'POST'), 'settings/edit-starting-cities/{id}', [App\Http\Controllers\Admin\StartingCitiesController::class, 'update']);
            Route::get('settings/delete-starting-cities/{id}', [App\Http\Controllers\Admin\StartingCitiesController::class, 'delete']);
        });

        Route::group(['middleware' => 'permission:manage_property_type'], function () {
            Route::get('settings/property-type', [App\Http\Controllers\Admin\PropertyTypeController::class, 'index']);
            Route::match(array('GET', 'POST'), 'settings/add-property-type', [App\Http\Controllers\Admin\PropertyTypeController::class, 'add']);
            Route::match(array('GET', 'POST'), 'settings/edit-property-type/{id}', [App\Http\Controllers\Admin\PropertyTypeController::class, 'update']);
            Route::get('settings/delete-property-type/{id}', [App\Http\Controllers\Admin\PropertyTypeController::class, 'delete']);
        });

        Route::group(['middleware' => 'permission:space_type_setting'], function () {
            Route::get('settings/space-type', [App\Http\Controllers\Admin\SpaceTypeController::class, 'index']);
            Route::match(array('GET', 'POST'), 'settings/add-space-type', [App\Http\Controllers\Admin\SpaceTypeController::class, 'add']);
            Route::match(array('GET', 'POST'), 'settings/edit-space-type/{id}', [App\Http\Controllers\Admin\SpaceTypeController::class, 'update']);
            Route::get('settings/delete-space-type/{id}', [App\Http\Controllers\Admin\SpaceTypeController::class, 'delete']);
        });

        Route::group(['middleware' => 'permission:manage_bed_type'], function () {
            Route::get('settings/bed-type', [App\Http\Controllers\Admin\BedTypeController::class, 'index']);
            Route::match(array('GET', 'POST'), 'settings/add-bed-type', [App\Http\Controllers\Admin\BedTypeController::class, 'add']);
            Route::match(array('GET', 'POST'), 'settings/add-bed-type/{id}', [App\Http\Controllers\Admin\BedTypeController::class, 'update']);
            Route::get('settings/delete-bed-type/{id}', [App\Http\Controllers\Admin\BedTypeController::class, 'delete']);
        });

        Route::group(['middleware' => 'permission:manage_currency'], function () {
            Route::get('settings/currency', [App\Http\Controllers\Admin\CurrencyController::class, 'index']);
            Route::match(array('GET', 'POST'), 'settings/currency', [App\Http\Controllers\Admin\CurrencyController::class, 'add']);
            Route::match(array('GET', 'POST'), 'settings/currency/{id}', [App\Http\Controllers\Admin\CurrencyController::class, 'update'])->name('edit_currency');
            Route::get('settings/delete-currency/{id}', [App\Http\Controllers\Admin\CurrencyController::class, 'delete']);
        });

        Route::group(['middleware' => 'permission:manage_country'], function () {
            Route::get('settings/country', [App\Http\Controllers\Admin\CountryController::class, 'index']);
            Route::match(array('GET', 'POST'), 'settings/add-country', [App\Http\Controllers\Admin\CountryController::class, 'add']);
            Route::match(array('GET', 'POST'), 'settings/add-country/{id}', [App\Http\Controllers\Admin\CountryController::class, 'update']);
            Route::get('settings/delete-country/{id}', [App\Http\Controllers\Admin\CountryController::class, 'delete']);
        });

        Route::group(['middleware' => 'permission:manage_amenities_type'], function () {
            Route::get('settings/amenities-type', [App\Http\Controllers\Admin\AmenitiesTypeController::class, 'index']);
            Route::match(array('GET', 'POST'), 'settings/add-amenities-type', [App\Http\Controllers\Admin\AmenitiesTypeController::class, 'add']);
            Route::match(array('GET', 'POST'), 'settings/edit-amenities-type/{id}', [App\Http\Controllers\Admin\AmenitiesTypeController::class, 'update']);
            Route::get('settings/delete-amenities-type/{id}', [App\Http\Controllers\Admin\AmenitiesTypeController::class, 'delete']);
        });

        Route::match(array('GET', 'POST'), 'settings/email', [App\Http\Controllers\Admin\AmenitiesTypeController::class, 'email'])->middleware(['permission:email_settings']);

        Route::group(['middleware' => 'permission:manage_language'], function () {
            Route::get('settings/language', [App\Http\Controllers\Admin\LanguageController::class, 'index']);
            Route::match(array('GET', 'POST'), 'settings/add-language', [App\Http\Controllers\Admin\LanguageController::class, 'add']);
            Route::match(array('GET', 'POST'), 'settings/add-language/{id}', [App\Http\Controllers\Admin\LanguageController::class, 'update']);
            Route::get('settings/delete-language/{id}', [App\Http\Controllers\Admin\LanguageController::class, 'delete']);
        });

        Route::match(array('GET', 'POST'), 'settings/fees', [App\Http\Controllers\Admin\SettingsController::class, 'fees'])->middleware(['permission:manage_fees']);

        Route::match(array('GET', 'POST'), 'settings/api-informations', [App\Http\Controllers\Admin\SettingsController::class, 'apiInformations'])->middleware(['permission:api_informations']);
        Route::match(array('GET', 'POST'), 'settings/payment-methods', [App\Http\Controllers\Admin\SettingsController::class, 'paymentMethods'])->middleware(['permission:payment_settings']);
        // Route::match(array('GET', 'POST'), 'settings/bank/add', [App\Http\Controllers\Admin\BankController::class, 'addBank'])->middleware(['permission:payment_settings']);
        // Route::match(array('GET', 'POST'), 'settings/bank/edit/{bank}', [App\Http\Controllers\Admin\BankController::class, 'editBank'])->middleware(['permission:payment_settings']);
        // Route::get('settings/bank/{bank}', [App\Http\Controllers\Admin\BankController::class, 'show'])->middleware(['permission:payment_settings']);
        // Route::get('settings/bank/delete/{bank}', [App\Http\Controllers\Admin\BankController::class, 'deleteBank'])->middleware(['permission:payment_settings']);

        Route::match(array('GET', 'POST'), 'settings/social-links', [App\Http\Controllers\Admin\SettingsController::class, 'socialLinks'])->middleware(['permission:social_links']);
        Route::match(array('GET', 'POST'), 'settings/social-logins', [App\Http\Controllers\Admin\SettingsController::class, 'socialLogin'])->middleware(['permission:social_logins']);

        Route::group(['middleware' => 'permission:manage_roles'], function () {
            Route::get('settings/roles', [App\Http\Controllers\Admin\RolesController::class, 'index']);
            Route::match(array('GET', 'POST'), 'settings/add-role', [App\Http\Controllers\Admin\RolesController::class, 'add']);
            Route::match(array('GET', 'POST'), 'settings/add-role/{id}', [App\Http\Controllers\Admin\RolesController::class, 'update']);
            Route::match(array('GET', 'POST'), 'settings/edit-role/{id}', [App\Http\Controllers\Admin\RolesController::class, 'update']);
            Route::get('settings/roles/{id}', [App\Http\Controllers\Admin\RolesController::class, 'delete']);
        });

        Route::group(['middleware' => 'permission:database_backup'], function () {
            Route::get('settings/backup', [App\Http\Controllers\Admin\BackupController::class, 'index']);
            Route::get('backup/save', [App\Http\Controllers\Admin\BackupController::class, 'add']);
            Route::get('backup/download/{id}', [App\Http\Controllers\Admin\BackupController::class, 'download']);
        });

        Route::group(['middleware' => 'permission:manage_email_template'], function () {
            Route::get('email-template/{id}', [App\Http\Controllers\Admin\EmailTemplateController::class, 'index']);
            Route::get('email-template/{id}', [App\Http\Controllers\Admin\EmailTemplateController::class, 'update']);
        });

        Route::group(['middleware' => 'permission:manage_testimonial'], function () {
            Route::get('testimonials', [App\Http\Controllers\Admin\TestimonialController::class, 'index']);
            Route::match(array('GET', 'POST'), 'add-testimonials', [App\Http\Controllers\Admin\TestimonialController::class, 'add']);
            Route::match(array('GET', 'POST'), 'edit-testimonials/{id}', [App\Http\Controllers\Admin\TestimonialController::class, 'update']);
            Route::get('delete-testimonials/{id}', [App\Http\Controllers\Admin\TestimonialController::class, 'delete']);

            // User Device Routes
            Route::get('user/device', [App\Http\Controllers\Admin\UserDeviceController::class, 'index'])->name('userdevice');

            //Customer Refund
            Route::get('customer/refunds', [App\Http\Controllers\Admin\CustomerRefundController::class, 'CustomerRefund'])->name('refund.CustomerRefund');
            Route::match(array('GET', 'POST'), 'add-refund', [App\Http\Controllers\Admin\CustomerRefundController::class, 'add'])->name('admin.add-refunds');

            // Refunds Routes
            Route::get('refunds', [App\Http\Controllers\Admin\RefundController::class, 'index'])->name('refund.listing');
            Route::match(array('GET', 'POST'), 'security-refund', [App\Http\Controllers\Admin\RefundController::class, 'securityrefund'])->name('securityrefund');
            Route::get('myfatoorah-status/', [App\Http\Controllers\Admin\RefundController::class, 'mf_refund_status'])->name('mf_refund_status');
            Route::match(array('GET', 'POST'), 'edit-refunds', [App\Http\Controllers\Admin\RefundController::class, 'upload_receipt'])->name('uploadreceipt');

            //Customer Support Routes
            Route::get('customer/support', [App\Http\Controllers\Admin\SupportController::class, 'customerSupport']);

            Route::match(array('GET', 'POST'), 'customer/support/message', [App\Http\Controllers\Admin\SupportController::class, 'adminsupportReply']);

            // Notification Send to
            Route::match(['GET', 'POST'], 'customer/support/notification/settings', [App\Http\Controllers\Admin\SupportController::class, 'notificationSetting'])->middleware(['permission:translation'])->name('customer.support.notification.settings');
        });

        Route::group(['middleware' => 'permission:messages'], function () {
            // Route::get('chats', [App\Http\Controllers\Admin\ChatController::class, 'index']);
        });
    });

    Route::resource('notifications', App\Http\Controllers\Admin\NotificationController::class, ['as' => 'admin']);

    Route::resource('travel-agents', App\Http\Controllers\Admin\TravelAgentController::class, ['as' => 'admin'])
        ->except(['show', 'delete']);
    Route::get('delete-travel-agents/{id}', [App\Http\Controllers\Admin\TravelAgentController::class, 'destroy']);
});

Route::group(['middleware' => ['locale']], function () {

    Route::get('/get-amenities/{propertyTypeId}', [App\Http\Controllers\PropertyController::class, 'getAmenities'])->name('getRecommendedAmenities');

    Route::get('booking/receipt', [App\Http\Controllers\HomeController::class, 'receipt'])->name('user.receipt');
    Route::get('host/booking/receipt', [App\Http\Controllers\HomeController::class, 'hostReceipt'])->name('host.receipt');

    // Verify Phone Otp for New Login
    Route::post('phone/otp/verify', [App\Http\Controllers\HomeController::class, 'verifyPhoneOtp'])->name('verifyPhoneOtp');
    // ->middleware('throttle:otp');

    // Sign up New Form
    Route::post('signup', [App\Http\Controllers\HomeController::class, 'SignUp'])->name('signup');

    //wishlistGroup by code
    Route::get('{lang?}/wishlists/{code}', [App\Http\Controllers\WishlistController::class, 'getAllWishlistProperties'])->name('wishlists.properties')
        ->where('lang', '^(en|ar)?$');
    //Create Web Otp

    //     Route::post('v1/sendOtp', [App\Http\Controllers\HomeController::class, 'createPhoneOtp'])->name('createOtp');
    //Change Phone OTP
    Route::GET('v1/change/phone/otp', [App\Http\Controllers\HomeController::class, 'changePhone'])->name('changePhone');

    Route::post('v1/sendOtp', [App\Http\Controllers\HomeController::class, 'createPhoneOtp'])
        //->middleware(ValidateFingerprint::class)
        ->name('createOtp');
    // ->middleware('throttle:otp');


    // Route::post('sendOtp', function () {
    //     return 'ali haider test';
    // })->name('createOtp');

    //  front-end added new pages start

    // transactions page
    Route::get('transaction_details', [App\Http\Controllers\HomeController::class, 'transaction_details'])->name('pages.transaction_details');

    //  front-end added new pages end
    Route::get('mobile/profileViewer', [App\Http\Controllers\HomeController::class, 'profileViewer'])->name('mobile.pages.profileViewer');

    // Get Countries
    Route::get('getCountries', [App\Http\Controllers\HomeController::class, 'getCountries'])->name('getCountries');
    Route::get('clearCache', [App\Http\Controllers\HomeController::class, 'clearCache'])->name('clearCache');
    Route::get('getDistricts/{id}', [App\Http\Controllers\HomeController::class, 'getDistricts'])->name('getDistricts');
    Route::post('getCampaignProperties', [App\Http\Controllers\Admin\CampaignController::class, 'getCampaignProperties'])->name('campaign.getProperties');
    Route::post('fetchAmenitiesBaseonIds', [App\Http\Controllers\HomeController::class, 'fetchAmenitiesBaseonIds'])->name('fetchAmenitiesBaseonIds');
    Route::get('getReservations/{type}', [App\Http\Controllers\HomeController::class, 'getReservations'])->name('getReservations');
    Route::get('generateReferalCode/{code}', [App\Http\Controllers\HostReferalCodeController::class, 'generateReferalCode'])->name('generateReferalCode');
    Route::get('generateReferalLink/{code}', [App\Http\Controllers\HostReferalCodeController::class, 'generateReferalLink'])->name('generateReferalLink');
    Route::get('/getCampaginProperties/{campaign}', [App\Http\Controllers\CampaignController::class, 'getCampaginProperties'])->name('getCampaginProperties');
    Route::get('/getBooking/{id}', [App\Http\Controllers\BookingController::class, 'getBooking'])->name('getBooking');
    // Route::get('/debug-sentry', function () { // SENTRY TEST ROUTE
    //     throw new Exception('My first Sentry error!');
    // });

    Route::prefix('v1')->group(function () {
        // Property Review Module
        Route::get('{type}/properties/{property}/review', [App\Http\Controllers\Api\v2\ReviewController::class, 'list']);
        Route::get('{type}/properties/review', [App\Http\Controllers\Api\v2\ReviewController::class, 'list']);
    });

    Route::get('/recommended-home-properties', [App\Http\Controllers\HomeController::class, 'recommendedHomeProperties'])->name('recommended.home.properties');
    Route::get('{lang?}/', [App\Http\Controllers\HomeController::class, 'index'])->name('home')
        ->where('lang', '^(en|ar)?$');
    Route::get('become-host/', [App\Http\Controllers\PropertyController::class, 'becomeHost'])->name('becomehost');
    Route::get('{lang?}/properties/{slug}', [App\Http\Controllers\PropertyController::class, 'single'])->name('property.single')
        ->where('lang', '^(en|ar)?$');

    Route::get('{lang?}/search', [App\Http\Controllers\SearchController::class, 'index'])->name('search')
        ->where('lang', '^(en|ar)?$');
    Route::get('{lang?}/lp/riyadh-appartment-apartment', [App\Http\Controllers\SearchController::class, 'lpRiyadh'])->name('lp.riyadh')
        ->where('lang', '^(en|ar)?$');

    Route::post('search/result', [App\Http\Controllers\SearchController::class, 'searchResult'])->name('search.result');
    // Route::post('search/resultV2', [App\Http\Controllers\SearchController::class, 'searchResultV4'])->name('search.resultV2');
    Route::post('search/resultV2', [App\Http\Controllers\SearchController::class, 'searchResultV3'])->name('search.resultV2');

    // V6 Optimized Search Routes
    Route::get('{lang?}/v6/search', [App\Http\Controllers\V6\SearchController::class, 'index'])->name('v6.search')
        ->where('lang', '^(en|ar)?$');
    Route::post('v6/search/result', [App\Http\Controllers\V6\SearchController::class, 'searchResult'])->name('v6.search.result');

    Route::get('getPropertyType/{property_type_id}', [App\Http\Controllers\SearchController::class, 'getPropertyType'])->name('get.property.type');
    Route::get('getSpaceType/{space_type_id}', [App\Http\Controllers\SearchController::class, 'getSpaceType'])->name('get.space.type');
    Route::get('getSpaceTypesByPropertyType/{property_type_id}', [App\Http\Controllers\SearchController::class, 'getSpaceTypesByPropertyType'])->name('get.space.types.by.property.type');

    Route::match(array('GET', 'POST'), 'property/get-price', [App\Http\Controllers\Api\PropertyController::class, 'getPrice'])->name('property.price');
    Route::match(array('GET', 'POST'), 'property/v2/get-price', [App\Http\Controllers\Api\PropertyController::class, 'getPriceV2'])->name('property.v2.price');
    Route::get('set-slug/', [App\Http\Controllers\PropertyController::class, 'set_slug']);
    Route::post('/checkUser/check', [App\Http\Controllers\Api\LoginController::class, 'check'])->name('checkUser.check');

    Route::post('register', [App\Http\Controllers\Api\LoginController::class, 'register'])->name('user.register');
    Route::post('login', [App\Http\Controllers\Api\LoginController::class, 'login'])->name('user.login');
    Route::post('create/otp/{again?}', [App\Http\Controllers\Api\LoginController::class, 'createOtp'])->name('user.create.token')->middleware('throttle:otp');
    Route::post('verify/otp', [App\Http\Controllers\Api\LoginController::class, 'verifyOtp'])->name('user.verify.token')->middleware('throttle:otp');
    Route::get('google/redirect', [App\Http\Controllers\Api\LoginController::class, 'googleRedirect'])->name('user.google.redirect');
    Route::get('googleAuthenticate', [App\Http\Controllers\Api\LoginController::class, 'handleGoogleCallback'])->name('user.login.google');
    Route::post('logout', [App\Http\Controllers\Api\LoginController::class, 'logout'])->name('user.logout');


    Route::get('{lang?}/privacy-policy', [App\Http\Controllers\HomeController::class, 'privacy_policy'])->name('privacyPolicy')
        ->where('lang', '^(en|ar)?$');
    Route::get('{lang?}/term_condition', [App\Http\Controllers\HomeController::class, 'term_condition'])->name('termCondition')
        ->where('lang', '^(en|ar)?$');
    Route::get('{lang?}/insurance', [App\Http\Controllers\HomeController::class, 'insurance'])->name('insurance')
        ->where('lang', '^(en|ar)?$');
    Route::get('{lang?}/insurance_policy', [App\Http\Controllers\HomeController::class, 'insurance_policy'])->name('insurance_policy')
        ->where('lang', '^(en|ar)?$');
    Route::get('{lang?}/ilmyaqeen', [App\Http\Controllers\HomeController::class, 'ilm_yaqeen'])->name('ilm_yaqeen')
        ->where('lang', '^(en|ar)?$');
    Route::get('account_delete', [App\Http\Controllers\HomeController::class, 'account_delete'])->name('accountDelete')
        ->where('lang', '^(en|ar)?$');
    // faq guest
    // Route::get('{lang?}/faqGuest', [App\Http\Controllers\HomeController::class, 'faq_guest'])->name('faq_guest')
    //     ->where('lang', '^(en|ar)?$');


    Route::get('{lang?}/property_card', [App\Http\Controllers\HomeController::class, 'showPropertyCard'])->name('property_card')->where('lang', '^(en|ar)?$');

    Route::get('{lang?}/faqs', [App\Http\Controllers\HomeController::class, 'faq_guest'])->name('faq_guest')
        ->where('lang', '^(en|ar)?$');
    // faq host
    Route::get('{lang?}/faqHost', [App\Http\Controllers\HomeController::class, 'faq_host'])->name('faq_host')
        ->where('lang', '^(en|ar)?$');

    Route::get('{lang?}/about', [App\Http\Controllers\HomeController::class, 'about'])->name('about')
        ->where('lang', '^(en|ar)?$');
    Route::get('{lang?}/announcement', [App\Http\Controllers\HomeController::class, 'announcement'])->name('announcement')
        ->where('lang', '^(en|ar)?$');
    // Route::get('{lang?}/blog', [App\Http\Controllers\HomeController::class, 'announcement'])->name('announcement')
    //     ->where('lang', '^(en|ar)?$');

    Route::get('{lang?}/landing-page', [App\Http\Controllers\HomeController::class, 'landingPage'])->name('pages.landing-page')
        ->where('lang', '^(en|ar)?$');

    Route::post('/update/landing-page/visits',[App\Http\Controllers\HomeController::class, 'trackLandingVisit'])->name('update.visits');

    Route::get('{lang?}/checkout', [App\Http\Controllers\HomeController::class, 'checkoutPage'])->name('property.checkout')
        ->where('lang', '^(en|ar)?$');

    // Route::get('contact_host', [App\Http\Controllers\HomeController::class, 'contact_host'])->name('contactHost');


    //List YOur Property/Become A Host
    Route::get('starthosting', [App\Http\Controllers\PropertyController::class, 'StartHosting'])->name('starthosting');

    Route::get('property/stepOne', [App\Http\Controllers\PropertyController::class, 'stepOne'])->name('stepOne');
    Route::get('property/stepTwo', [App\Http\Controllers\PropertyController::class, 'stepTwo'])->name('stepTwo');
    Route::get('property/stepThree', [App\Http\Controllers\PropertyController::class, 'stepThree'])->name('stepThree');
    Route::get('property/confirmLocation', [App\Http\Controllers\PropertyController::class, 'confirmLocation'])->name('confirmLocation');
    Route::get('property/numberofRoom', [App\Http\Controllers\PropertyController::class, 'numberofRoom'])->name('numberofRoom');
    Route::get('property/photosUploading', [App\Http\Controllers\PropertyController::class, 'photosUploading'])->name('photosUploading');
    Route::get('property/reviewListing', [App\Http\Controllers\PropertyController::class, 'reviewListing'])->name('reviewListing');
    Route::get('property/setCover', [App\Http\Controllers\PropertyController::class, 'setCover'])->name('setCover');

    Route::get('{lang?}/property/darStays', [App\Http\Controllers\PropertyController::class, 'propertyLandingPage'])->name('propertyLandingPage')
     ->where('lang', '^(en|ar)?$');
    // Route::get('property/hostAgreement', [App\Http\Controllers\PropertyController::class, 'hostAgreement'])->name('hostAgreement');

    Route::get('{lang?}/property/hostAgreement', [App\Http\Controllers\PropertyController::class, 'hostAgreement'])->name('hostAgreement')
        ->where('lang', '^(en|ar)?$');
    Route::post('host-agreement', [App\Http\Controllers\Api\HomeController::class, 'hostAgreement'])->name('user.terms.agree');

    Route::get('listing/addBankAccount', [App\Http\Controllers\PropertyController::class, 'addBankAccount'])->name('addBankAccount');
    Route::get('listing/importCalendar', [App\Http\Controllers\PropertyController::class, 'importCalendar'])->name('importCalendar');
    Route::get('listing/permit', [App\Http\Controllers\PropertyController::class, 'permit'])->name('permit');

    Route::post('coupon/check', [App\Http\Controllers\PaymentController::class, 'couponCheck'])->name('couponCheck');

    Route::group(['middleware' => ['guest:users']], function () {
        // Tabby Routes
        Route::POST('checkout/tabby', [App\Http\Controllers\TabbyController::class, 'initiateTabby'])->name('tabbyPaymentInitiate');
        Route::get('success', [App\Http\Controllers\TabbyController::class, 'tabbySuccess'])->name('tabby_success');
        Route::get('cancel', [App\Http\Controllers\TabbyController::class, 'tabbyCancel'])->name('tabby_cancel');
        Route::get('failure', [App\Http\Controllers\TabbyController::class, 'tabbyFailure'])->name('tabby_failure');
        Route::get('installment/{price?}', [App\Http\Controllers\TabbyController::class, 'installmentWebView'])->name('tabby_instl_webview');

        // Property Chat Module
        Route::get('{type}/properties/chats', [App\Http\Controllers\PropertyChatController::class, 'propertyChats'])
            ->name('properties.chat.view');
        Route::prefix('v1')->group(function () {
            // Calendar Module
            Route::get('properties/{id}/calendar', [App\Http\Controllers\Api\PropertyController::class, 'getPricesByProperty']);
            Route::post('properties/{id}/calendar/save', [App\Http\Controllers\Api\PropertyController::class, 'setCalendarProp']);
            Route::get('properties/{property}/calendar/bookings/{booking}', [App\Http\Controllers\Api\PropertyController::class, 'getCalendarBookingInfo']);
            Route::get('properties/{type}/{entity_type}/{entity_id}/details', [App\Http\Controllers\Api\v3\PropertyChatController::class, 'getReservationDetails'])
                ->where(['type' => 'host|guest', 'entity_type' => 'bookings|property-inquiries']);
            Route::post('elm/verify', [App\Http\Controllers\Api\ElmDocController::class, 'verify'])->name('user.elm.verify');

            // Route::get('icalendar/synchronization/{id}', [App\Http\Controllers\Admin\CalendarController::class, 'icalendarSynchronization']);
            Route::get('host/icalendar/synchronization/{id}', [App\Http\Controllers\Admin\CalendarController::class, 'icalendarSynchronization']);
            Route::get('calendar-urls/{id}', [App\Http\Controllers\CalendarController::class, 'index'])->name('calendar-urls');
            Route::post('calendar-urls-update/{id?}', [App\Http\Controllers\CalendarController::class, 'update'])->name('calendar-urls-update');
            Route::post('calendar-urls-remove/{id}', [App\Http\Controllers\CalendarController::class, 'remove'])->name('calendar-urls-remove');

            // Property Chat Module
            Route::get('{type}/properties/chats', [App\Http\Controllers\Api\v3\PropertyChatController::class, 'propertyChatHeads']);
            Route::get('{type}/properties/chats/{chat}', [App\Http\Controllers\Api\v3\PropertyChatController::class, 'propertyChatHeadById']);
            Route::post('{type}/properties/chats/{chat}/messages', [App\Http\Controllers\Api\v3\PropertyChatController::class, 'sendPropertyChatMessage']);
            Route::get('{type}/properties/chats/{chat}/messages', [App\Http\Controllers\Api\v3\PropertyChatController::class, 'propertyChatsByChatHead'])
                ->name('paginated.property.chats');
            Route::post('properties/{property}/inquiry', [App\Http\Controllers\Api\v3\PropertyChatController::class, 'sendPropertyInquiry']);
            Route::post('{type}/properties/chats/{chat}', [App\Http\Controllers\Api\v3\PropertyChatController::class, 'sendPropertyChatMessage']);
            Route::get('{type}/properties/chats/{chat}/details', [App\Http\Controllers\Api\v3\PropertyChatController::class, 'getChatHeadDetails']);

            // Property Review Module
            Route::post('{type}/bookings/{booking}/review', [App\Http\Controllers\Api\v2\ReviewController::class, 'store']);

            Route::post('/booking/accept/{id}', [App\Http\Controllers\BookingController::class, 'acceptBooking']);

            Route::post('/booking/decline/{id}', [App\Http\Controllers\BookingController::class, 'decline'])->name('declineBooking');

            Route::get('complete-properties', [App\Http\Controllers\Api\PropertyController::class, 'completeProperties']);

            Route::post('bookings/{booking}/remove/coupon', [App\Http\Controllers\BookingController::class, 'removePromo']);
        });

        // collaborate in wishlist
        Route::get('collaborators/{code}', [App\Http\Controllers\WishlistController::class, 'collaborateUser']);

        // leave in wishlist
        Route::get('leave-wishlist', [App\Http\Controllers\WishlistController::class, 'leaveWishlist'])->name('leaveWishlistGroup');

        //set Host Mode
        Route::POST('set/hostmode', [App\Http\Controllers\HomeController::class, 'switchToHost'])->name('switchToHost');

        //clearhostsession
        Route::post('/clear-switch-to-host-session', [App\Http\Controllers\HomeController::class, 'clearSwitchToHostSession'])->name('clearSwitchToHostSession');

        // Verify Email Otp for New Login
        Route::post('email/otp/verify', [App\Http\Controllers\HomeController::class, 'emailVerifyOtp'])->name('verifyemailotp')
            ->middleware('throttle:otp');
        // Resend Email Otp
        Route::get('resend/email/resend', [App\Http\Controllers\HomeController::class, 'resendEmailOtp'])->name('resend.emailOtp')
            ->middleware('throttle:otp');
        // Change Email Otp
        Route::get('change/email/resend', [App\Http\Controllers\HomeController::class, 'changeEmail'])->name('changeemail')
            ->middleware('throttle:otp');
        // Change Email Otp
        Route::get('change/email/otp/verify', [App\Http\Controllers\HomeController::class, 'verifyEmailChangeOtp'])->name('verifyEmailChangeOtp')
            ->middleware('throttle:otp');

        Route::get('change/phone/otp/verify', [App\Http\Controllers\HomeController::class, 'verifyPhoneChangeOtp'])->name('verifyPhoneChangeOtp');

        // New Wishlist like airbnb
        Route::post('create/wishlist', [App\Http\Controllers\WishlistController::class, 'createWishlistName'])->name('createWishlist');
        Route::post('update/wishlist', [App\Http\Controllers\WishlistController::class, 'updateWishlistName'])->name('updateWishlist');
        Route::post('delete/wishlist', [App\Http\Controllers\WishlistController::class, 'deleteWishlistByName'])->name('deleteWishlist');
        Route::post('add/remove/wishlist', [App\Http\Controllers\WishlistController::class, 'addRemoveWishlistProperty'])->name('addRemoveWishlist');
        Route::post('toggle/wishlist', [App\Http\Controllers\WishlistController::class, 'toggleWishlist'])->name('toggleWishlist');
        Route::get('{lang?}/wishlists', [App\Http\Controllers\WishlistController::class, 'getAllWishlistGroups'])->name('wishlists.listing')
            ->where('lang', '^(en|ar)?$');
        Route::get('collaborator', [App\Http\Controllers\WishlistController::class, 'updateCollaborate'])->name('collaborate');

        // bank page page
        Route::match(array('GET', 'POST'), 'user/bankAccount', [App\Http\Controllers\HomeController::class, 'bankAccount'])->name('bankAccount');

        // customer support
        Route::get('customer_services', [App\Http\Controllers\InboxController::class, 'customer_services'])->name('customerServices');
        Route::post('customer_service/message', [App\Http\Controllers\InboxController::class, 'storeCustomerSupportMessage'])->name('sendcustomermessage');

        // Delete On Hold Booking Dates
        Route::post('onhold/booking', [App\Http\Controllers\BookingController::class, 'deleteHoldBookingDates'])->name('deleteHoldBookingDates');

        //import calendar on website
        Route::post('ajax-icalender-import/{id}', [App\Http\Controllers\Admin\CalendarController::class, 'icalendarImport']);
        Route::post('ajax-icalender-sync/{id}', [App\Http\Controllers\CalendarController::class, 'icalendarSynchronization'])->name('ajax.icalendar.synchronization');

        // transactions page
        Route::get('transaction_details', [App\Http\Controllers\HomeController::class, 'transaction_details'])->name('pages.transaction_details');


        Route::post('save-timezone', [App\Http\Controllers\HomeController::class, 'setTimeZone']);

        Route::get('wishlist', [App\Http\Controllers\HomeController::class, 'wishList'])->name('user.wishlist');

        // Route::match(['get', 'post'], 'payments/book/{id?}', [App\Http\Controllers\PaymentController::class, 'index']);
        Route::Post('summary', [App\Http\Controllers\PaymentController::class, 'postSummary'])->name('payment.summary');
        Route::get('{lang?}/summary/{token}', [App\Http\Controllers\PaymentController::class, 'getSummary'])->name('payment.summary.getUrl')->where('lang', '^(en|ar)?$');;

        Route::match(['get', 'post'], 'payments/book/{id?}', [App\Http\Controllers\PaymentController::class, 'propertyReservation']);
        // Route::match(['get', 'post'], 'payments/book/{id?}', [App\Http\Controllers\PaymentController::class, 'webReservation']);
        Route::get('apple-pay', [App\Http\Controllers\PaymentController::class, 'paymentSuccess'])->name('payment.success');
        Route::get('wallet-deposit', [App\Http\Controllers\PaymentController::class, 'walletdepositSuccess'])->name('wallet.deposit.success');


        Route::match(['get', 'post'], 'payments/create_booking', [App\Http\Controllers\PaymentController::class, 'createBooking'])->name('create_booking');

        Route::get('myfatoorah/payment/execute/{bookingid}', [App\Http\Controllers\PaymentController::class, 'myFatoorahPaymentExecute'])->name('myFatoorahPaymentExecute');
        Route::get('payment/cancel', [App\Http\Controllers\PaymentController::class, 'cancel'])->name('paymentCancel');

        //  tickets pages
        Route::match(['get', 'post'], '{lang?}/ticket/create', [App\Http\Controllers\TicketController::class, 'create_ticket'])->name('tickets.create_ticket')
            ->where('lang', '^(en|ar)?$');
        // Route::post('ticket/create', [App\Http\Controllers\TicketController::class, 'create_ticket'])->name('tickets.create_ticket');
        Route::get('{lang?}/ticket/list', [App\Http\Controllers\TicketController::class, 'ticket_list'])->name('tickets.ticket_list')
            ->where('lang', '^(en|ar)?$');
        Route::match(['GET', 'POST'], 'ticket-detail/{id}', [App\Http\Controllers\TicketController::class, 'ticket_detail'])->name('tickets.edit');
        Route::get('booking/requested', [App\Http\Controllers\BookingController::class, 'requested'])->name('requested');
        Route::get('trips/guest_cancel/{id}', [App\Http\Controllers\TripsController::class, 'guestCancel'])->name('cancelbyguest');
        Route::post('booking/accept/{id}', [App\Http\Controllers\BookingController::class, 'accept'])->name('accept');

        Route::post('booking/decline/{id}', [App\Http\Controllers\BookingController::class, 'decline'])->name('decline');
        Route::post('makePayment', [App\Http\Controllers\BookingController::class, 'makePayment'])->name('makePayment');
        Route::post('bookings/apple/pay', [App\Http\Controllers\BookingController::class, 'applePage'])->name('applePage');

        //Cancellation  Policy Route POST
        Route::get('/cancellation_policy', [App\Http\Controllers\HomeController::class, 'Cancellation_Policy'])->name("cancellation_policy");
        Route::post('store-cancellationpolicy', [App\Http\Controllers\BookingController::class, 'CancellationPolicy'])->name('store_cancellationpolicy');
        Route::get('/guest_requirements', [App\Http\Controllers\HomeController::class, 'GuestRequirements'])->name("guestrequirements");
        Route::post('store-guestrequirement', [App\Http\Controllers\BookingController::class, 'Store_GuestRequirement'])->name('store_guestrequirement');
        Route::get('/receiptPDF', [App\Http\Controllers\HomeController::class, 'ReceiptPDF']);

        // setting policy added by frontend
        Route::get('/policySetting', [App\Http\Controllers\HomeController::class, 'policySetting'])->name("policySetting");

        Route::get('/guestBookings', [App\Http\Controllers\HomeController::class, 'guestBookings'])->name("guestBookings");

        Route::match(array('GET', 'POST'), 'property/addplace/{id?}', [App\Http\Controllers\PropertyController::class, 'addPlace'])->name('addplace');
        Route::match(array('GET', 'POST'), 'property/preferred_method', [App\Http\Controllers\PropertyController::class, 'preferred_method'])->name('preferred_method');
        Route::match(array('GET', 'POST'), 'property/listing_iqama', [App\Http\Controllers\PropertyController::class, 'listing_iqama'])->name('listing_iqama');

        // Add Property by host
        Route::match(array('GET', 'POST'), 'listing/{id}/{step}', [App\Http\Controllers\PropertyController::class, 'AddPropertyWithSteps'])
            ->name("listingwithsteps");
        Route::post('getimages/{propertyId}', [App\Http\Controllers\PropertyController::class, 'getimages'])->name('property.images');
        // ->where(['id' => '[0-9]+', 'step' => 'basics|description|location|amenities|photos|pricing|calendar|details|booking']);
        // Admin approval for images

        Route::get('/approveImages', [App\Http\Controllers\Admin\PropertiesController::class, 'approvedImages'])->name("approvedImages");
        Route::get('/approveTitle', [App\Http\Controllers\Admin\PropertiesController::class, 'approvedTitlenDescription']);

        // Edit Properties
        Route::match(array('GET', 'POST'), 'editlisting/{id}/{step}', [App\Http\Controllers\PropertyController::class, 'edit_listing'])
            ->name('property.edit')
            ->where(['id' => '[0-9]+', 'step' => 'propertyType|photos|basics|amenities|location|nightsandtime|price|addBankAccount|calendar|question|license']);


        // Route::match(array('GET', 'POST'), 'listing/{id}/{step}', [App\Http\Controllers\PropertyController::class, 'importCalendar']) ->name("importCalendar");


        Route::match(array('POST'), 'editlisting/updatecover', [App\Http\Controllers\PropertyController::class, 'updateCoverPhoto'])->name('updateCover');

        // 'basics|description|location|amenities|photos|pricing|calendar|details|booking'

        // Route::get('translate', [App\Http\Controllers\PropertyController::class, 'translate'])->name('trans.englishnArabic');
        // ->where(['id' => '[0-9]+','page' => 'basics|description|location|amenities|photos|pricing|calendar|details|booking']);
        Route::post('/delete_photo', [App\Http\Controllers\PropertyController::class, 'DeletePhoto'])->name('delete_photo');
        Route::get('your_listing', [App\Http\Controllers\PropertyController::class, 'yourlisting'])->name("yourlisting");

        Route::get('/booking_history', [App\Http\Controllers\HomeController::class, 'bookingHistory'])->name('user.booking_history');
        Route::get('load-more', [App\Http\Controllers\PropertyController::class, 'loadMore'])->name('loadMore');
        Route::get('load-more/listing', [App\Http\Controllers\PropertyController::class, 'moreListing'])->name('moreListing');
        Route::get('load-more/reservation', [App\Http\Controllers\BookingController::class, 'moreReservation'])->name('moreReservation');
        Route::get('load-more/host/reservation', [App\Http\Controllers\BookingController::class, 'hostMoreReservation'])->name('host.moreReservation');
        Route::get('more-booking-request', [App\Http\Controllers\PropertyController::class, 'moreBookingRequest'])->name('moreBookingRequest');
        Route::get('more-host-upcoming-booking', [App\Http\Controllers\PropertyController::class, 'moreHostUpcomingBooking'])->name('morehostupcomingbooking');
        Route::get('more-host-expired-booking', [App\Http\Controllers\PropertyController::class, 'moreHostExpiredBooking'])->name('moreexpiredbooking');
        Route::get('more-host-ongoing-booking', [App\Http\Controllers\PropertyController::class, 'moreHostOngoingBooking'])->name('morehostongoingbooking');
        Route::get('more-host-cancelled-booking', [App\Http\Controllers\PropertyController::class, 'moreHostCancelledBooking'])->name('morehostcancelledbooking');
        Route::get('more-host-history-booking', [App\Http\Controllers\PropertyController::class, 'moreHostHistoryBooking'])->name('morehosthistorybooking');
        // Route::get('/booking_payment/{id?}', [App\Http\Controllers\HomeController::class, 'bookingHistory'])->name('user.booking_history');

        //PROMO
        Route::get('promo', [App\Http\Controllers\PromoController::class, 'promocode'])->name('pages.promoCode');
        Route::post('promoCodeForm', [App\Http\Controllers\PromoController::class, 'promoCodeForm'])->name('pages.promocodeform');
        Route::get('promotions', [App\Http\Controllers\PromoController::class, 'promotions'])->name('pages.promotions');
        Route::get('promo/edit/{id}', [App\Http\Controllers\PromoController::class, 'promoEdit'])->name('promo.edit');
        Route::get('promo/generate', [App\Http\Controllers\PromoController::class, 'generateCode'])->name('promo.generate');
        Route::get('deletepromo', [App\Http\Controllers\PromoController::class, 'deletePromo'])->name('delete.promotion');


        Route::post('check_list', [App\Http\Controllers\PropertyController::class, 'checklisting'])->name("checklisting");

        // WALLET ROUTES
        Route::get('{lang?}/wallet', [App\Http\Controllers\UserWalletController::class, 'wallet'])->name('user.wallet')->middleware(['userPermission:properties'])
            ->where('lang', '^(en|ar)?$');
        Route::get('{lang?}/all_transaction', [App\Http\Controllers\UserWalletController::class, 'all_transaction'])->name('payment.all_transaction');
        Route::get('load-more-transactions', [App\Http\Controllers\UserWalletController::class, 'loadMoreTransactions'])->name('load.more.transactions');
        Route::post('credit', [App\Http\Controllers\NewUserWalletController::class, 'credit'])->name('new.user.wallet.credit');
        Route::post('credit', [App\Http\Controllers\NewUserWalletController::class, 'creditForHyperpay'])->name('new.user.wallet.credit');
        Route::get('walletCallback', [App\Http\Controllers\NewUserWalletController::class, 'walletCallback'])->name('new.user.wallet.callback');
        Route::post('make-defualt-card', [App\Http\Controllers\UserWalletController::class, 'makeDefualtCard'])->name('card.makeDefault');
        Route::post('delete-card', [App\Http\Controllers\UserWalletController::class, 'deleteCard'])->name('card.delete');

        Route::post('user/card/add', [App\Http\Controllers\PaymentController::class, 'addCard'])->name('user.addcard');

        // Messaging
        Route::get('manualmsg', [App\Http\Controllers\InboxController::class, 'MuanualMessage'])->name('sendmsg');

        Route::match(['get', 'post'], 'inbox', [App\Http\Controllers\InboxController::class, 'index'])->name('inbox');

        Route::match(['get', 'post'], 'window/{id}', [App\Http\Controllers\InboxController::class, 'window'])->name('user.window');
        Route::post('messaging/booking/', [App\Http\Controllers\InboxController::class, 'message']);
        Route::post('messaging/reply/', [App\Http\Controllers\InboxController::class, 'messageReply']);
        // Messaging End

        Route::post('add-edit-book-mark', [App\Http\Controllers\PropertyController::class, 'addEditBookMark']);

        Route::get('incoming/booking/request', [App\Http\Controllers\BookingController::class, 'bookingRequests'])->name('user.my-bookings');
        Route::get('host/incoming/booking', [App\Http\Controllers\BookingController::class, 'hostUpcomingBooking'])->name('host.upcoming-bookings');
        Route::get('host/ongoing/booking', [App\Http\Controllers\BookingController::class, 'hostOngoingBooking'])->name('host.ongoing-bookings');
        Route::get('host/cancelled/booking', [App\Http\Controllers\BookingController::class, 'hostCancelledBooking'])->name('host.cancelled-bookings');
        Route::get('host/history/booking', [App\Http\Controllers\BookingController::class, 'hostHistoryBooking'])->name('host.history-bookings');
        Route::get('host/expired/booking', [App\Http\Controllers\BookingController::class, 'hostExpiredBooking'])->name('host.expired-bookings');
        // Route::get('booking-history', [App\Http\Controllers\HomeController::class, 'bookingHistory'])->name('user.booking-history');
        Route::get('booking/{id}', [App\Http\Controllers\BookingController::class, 'index'])->where('id', '[0-9]+');
        // personal info
        Route::get('personalinfo', [App\Http\Controllers\HomeController::class, 'personalinfo'])->name('user.personalinfo');
        Route::get('profile', [App\Http\Controllers\Api\UserController::class, 'profileView'])->name('user.profile.view');
        Route::get('login-security', [App\Http\Controllers\Api\UserController::class, 'loginSecurity'])->name('user.login-security');
        Route::get('my-request', [App\Http\Controllers\Api\UserController::class, 'myRequest'])->name('pages.my-request');
        // userrole
        Route::match(['get', 'post'], 'create/account/manager/', [App\Http\Controllers\HomeController::class, 'createAccountManager'])->name('user.create_account_manager');
        Route::get('api/create/account/manager', [App\Http\Controllers\Api\UserController::class, 'profileView'])->name('create.account_manager');
        Route::get('account/manager/list', [App\Http\Controllers\HomeController::class, 'listAccountManager'])->name('user.account_manager.list');
        Route::any('account/delete', [App\Http\Controllers\AccountDeleteRequestController::class, 'deleteAccount'])->name('user.delete_account');
        Route::match(['get', 'post'], 'edit/account/manager/{id}', [App\Http\Controllers\HomeController::class, 'editAccountManager'])->name('user.edit_account_manager');
        Route::get('account/manager/delete/{id}', [App\Http\Controllers\HomeController::class, 'deleteAccountManager'])->name('user.account_manager.delete');
        // reservation
        Route::get('reservation', [App\Http\Controllers\HomeController::class, 'reservation'])->name('reservation');

        // host bookings mobile tabs
        Route::get('host/bookings', [App\Http\Controllers\BookingController::class, 'hostBookings'])->name('mobile.hostbookings');

        // new inbox
        Route::get('new_inbox', [App\Http\Controllers\HomeController::class, 'new_inbox'])->name('inbox.new_inbox');
        Route::get('new_guest_inbox', [App\Http\Controllers\HomeController::class, 'new_guest_inbox'])->name('inbox.new_guest_inbox');

        Route::get('properties/{slug}/contact_host', [App\Http\Controllers\PropertyController::class, 'contact_host'])->name('property.contact_host');
        Route::get('managehost/properties/{code}/calendar', [App\Http\Controllers\PropertyController::class, 'propertyCalendar'])->name('managehost.properties.calendar');


        Route::get('properties/bookings/{booking}/apple', [App\Http\Controllers\BookingController::class, 'appleView'])->name('bookings.apple.view');

        // notification view
        Route::get('notifications', [App\Http\Controllers\HomeController::class, 'notifications'])->name('user.notifications');

        //for ajax
        Route::get('getnotifications', [App\Http\Controllers\HomeController::class, 'getNotifications'])->name('getnotifications');

        Route::post('notifications/read', [App\Http\Controllers\HomeController::class, 'markNotifications'])->name('user.notifications.read');

        Route::get('pages/account', [App\Http\Controllers\HomeController::class, 'accounts'])->name('mobile.pages.account');
        Route::get('pages/view_profile', [App\Http\Controllers\HomeController::class, 'view_profile'])->name('mobile.pages.view_profile');


        Route::post('booking/{booking}/paymentApple', [App\Http\Controllers\PaymentController::class, 'paymentApple'])->name('paymentApple');


        // Host Dashboard Routes
        //HOST LISTING API
        Route::get('managehost/api/host_listings', [App\Http\Controllers\PropertyController::class, 'hostListingApi'])->name('managehost.api');
        // instruction page
        Route::get('managehost/api/calendar-listings', [App\Http\Controllers\PropertyController::class, 'hostListingApi'])->name('managehost.calendar-listings');


        Route::get('managehost/day', [App\Http\Controllers\PropertyController::class, 'day'])->name('managehost.day');
        Route::get('managehost/allreservation', [App\Http\Controllers\PropertyController::class, 'reservationData'])->name('managehost1.day');
        Route::get('managehost/all_reservation', [App\Http\Controllers\HomeController::class, 'all_reservation'])->name('managehost.all_reservation');

        Route::get('managehost/allreservation/export', [App\Http\Controllers\PropertyController::class, 'reservationDataExport']);
        Route::get('managehost/allreservation/export/invoices', [App\Http\Controllers\PropertyController::class, 'generateAndDownloadZip'])->name('exportCalendarLink');
        Route::get('managehost/host_listings', [App\Http\Controllers\PropertyController::class, 'host_listings'])->name('managehost.host_listings');

        //managment request
        Route::post('property-management-requests/store', [App\Http\Controllers\Admin\PropertyManagementRequestController::class, 'store'])->name('host.request.management');

        Route::get('managehost/incoming_mail', [App\Http\Controllers\HomeController::class, 'incoming_mail'])->name('managehost.incoming_mail');
        // Route::get('managehost/host_listings',                [App\Http\Controllers\HomeController::class,       'host_listings'])->name('managehost.host_listings');

        Route::get('managehost/host-listings-details/{property_code}', [App\Http\Controllers\HostListingController::class, 'host_listings_details'])->name('managehost.host_listings_details');
        Route::post('managehost/host-listings-details/{property_code}', [App\Http\Controllers\HostListingController::class, 'updateDetails'])->name('managehost.updateDetails');
        Route::patch('managehost/custom-amenities/{property_code}', [App\Http\Controllers\HostListingController::class, 'updateCustomAmenities'])->name('managehost.updateCustomAmenities');
        Route::get('managehost/photos/{property_code}', [App\Http\Controllers\HostListingController::class, 'photos'])->name('managehost.photos');
        Route::get('delete/cohost', [App\Http\Controllers\HostListingController::class, 'deleteCoHost'])->name('delete.cohost');


        Route::put('managehost/storeListingDetails/{property_code}', [App\Http\Controllers\HostListingController::class, 'storeListingDetails'])->name('managehost.storeListingDetails');
        // Route::get('managehost/host_listings_details',        [App\Http\Controllers\HomeController::class,       'host_listings_details'])->name('managehost.host_listings_details');
        Route::get('managehost/host_transaction_history', [App\Http\Controllers\HomeController::class, 'host_transaction_history'])->name('managehost.host_transaction_history');
        Route::get('managehost/host_profile', [App\Http\Controllers\HostProfileController::class, 'host_profile'])->name('managehost.host_profile');
        Route::get('managehost/accountManage', [App\Http\Controllers\HomeController::class, 'accountManage'])->name('managehost.accountManage');
        Route::match(['GET', 'POST'], 'managehost/personal_info', [App\Http\Controllers\HostProfileController::class, 'personal_info'])->name('managehost.personal_info');
        // Route::get('managehost/login_and_security',           [App\Http\Controllers\HostProfileController::class,       'login_and_security'])->name('managehost.login_and_security');
        Route::get('managehost/payment_and_payouts_main', [App\Http\Controllers\HostProfileController::class, 'payment_and_payouts_main'])->name('managehost.payment_and_payouts_main');
        Route::get('managehost/host_notifications', [App\Http\Controllers\HomeController::class, 'host_notifications'])->name('managehost.host_notifications');
        Route::get('managehost/privacy_and_sharing', [App\Http\Controllers\HomeController::class, 'privacy_and_sharing'])->name('managehost.privacy_and_sharing');
        Route::match(['GET', 'POST'], 'managehost/global_preferences', [App\Http\Controllers\HostProfileController::class, 'global_preferences'])->name('managehost.global_preferences');
        Route::get('managehost/host_settings', [App\Http\Controllers\HomeController::class, 'host_settings'])->name('managehost.host_settings');
        Route::get('managehost/payments_and_payouts', [App\Http\Controllers\HomeController::class, 'payments_and_payouts'])->name('managehost.payments_and_payouts');
        Route::get('managehost/host_translation', [App\Http\Controllers\HomeController::class, 'host_translation'])->name('managehost.host_translation');
        Route::get('managehost/payouts', [App\Http\Controllers\HomeController::class, 'payouts'])->name('managehost.payouts');
        Route::get('managehost/transaction_history', [App\Http\Controllers\HomeController::class, 'transaction_history'])->name('managehost.transaction_history');
        Route::get('managehost/taxes', [App\Http\Controllers\HomeController::class, 'taxes'])->name('managehost.taxes');
        Route::get('managehost/privacy_and_sharing_inner', [App\Http\Controllers\HomeController::class, 'privacy_and_sharing_inner'])->name('managehost.privacy_and_sharing_inner');
        Route::get('managehost/payment_methods', [App\Http\Controllers\HomeController::class, 'payment_methods'])->name('managehost.payment_methods');
        Route::get('managehost/your_payments', [App\Http\Controllers\HomeController::class, 'your_payments'])->name('managehost.your_payments');
        Route::get('managehost/payment_return', [App\Http\Controllers\HomeController::class, 'payment_return'])->name('managehost.payment_return');

        // Route::get('managehost/photos',                       [App\Http\Controllers\HomeController::class,       'photos'])->name('managehost.photos');

        Route::get('managehost/host_calendar', [App\Http\Controllers\HomeController::class, 'host_calendar'])->name('managehost.host_calendar');
        Route::get('managehost/incoming_mail', [App\Http\Controllers\HomeController::class, 'incoming_mail'])->name('managehost.incoming_mail');
        // Route::get('managehost/host_listings', [App\Http\Controllers\HomeController::class, 'host_listings'])->name('managehost.host_listings');
        // Route::get('managehost/host_listings_details', [App\Http\Controllers\HomeController::class, 'host_listings_details'])->name('managehost.host_listings_details');
        Route::get('managehost/host_transaction_history', [App\Http\Controllers\HomeController::class, 'host_transaction_history'])->name('managehost.host_transaction_history');
        Route::get('managehost/transaction_history_create', [App\Http\Controllers\HomeController::class, 'transaction_history_create'])->name('managehost.transaction_history_create');
        Route::get('managehost/accountManage', [App\Http\Controllers\HostProfileController::class, 'accountManage'])->name('managehost.accountManage');
        // Route::get('managehost/personal_info', [App\Http\Controllers\HostProfileController::class, 'personal_info'])->name('managehost.personal_info');
        Route::get('managehost/login_and_security', [App\Http\Controllers\HostProfileController::class, 'login_and_security'])->name('managehost.login_and_security');
        Route::get('managehost/payment_and_payouts', [App\Http\Controllers\HomeController::class, 'payment_and_payouts'])->name('managehost.payment_and_payouts');
        Route::get('managehost/host_notifications', [App\Http\Controllers\HomeController::class, 'host_notifications'])->name('managehost.host_notifications');
        Route::get('managehost/privacy_and_sharing', [App\Http\Controllers\HomeController::class, 'privacy_and_sharing'])->name('managehost.privacy_and_sharing');
        // Route::get('managehost/global_preferences', [App\Http\Controllers\HomeController::class, 'global_preferences'])->name('managehost.global_preferences');
        Route::get('mobile/managehost/host_listings_mobile', [App\Http\Controllers\HomeController::class, 'host_listings_mobile'])->name('managehost.host_listings_mobile');


        Route::get('managehost/bookings/{booking}/reviews/create', [App\Http\Controllers\Api\v2\ReviewController::class, 'create'])->name('managehost.guest.review.create');

        // Co Host
        Route::get('managehost/coHost/{code}', [App\Http\Controllers\HomeController::class, 'coHost'])->name('managehost.coHost');
        Route::post('managehost/coHost/{code}', [App\Http\Controllers\HostListingController::class, 'coHostSave'])->name('managehost.coHostSave');
        // primary host
        Route::get('managehost/primaryHost', [App\Http\Controllers\HomeController::class, 'primaryHost'])->name('managehost.primaryHost');
        // co host user
        Route::get('managehost/coHostUser', [App\Http\Controllers\HomeController::class, 'coHostUser'])->name('managehost.coHostUser');


        // new reservation pages
        Route::get('{lang?}/guest/reservation', [App\Http\Controllers\ReservationController::class, 'guest_reservation'])->name('guest_reservation')
            ->where('lang', '^(en|ar)?$');
        Route::get('load-more/guest/reservation', [App\Http\Controllers\ReservationController::class, 'moreGuestReservation'])->name('moreGuestReservation');
        Route::get('{lang?}/guest/reservation/{code}', [App\Http\Controllers\ReservationController::class, 'guest_reservation_detail'])->name('guest_reservation_detail')
            ->where('lang', '^(en|ar)?$');
        // Customer Supports chat Api
        Route::resource("customer-support-chat", "App\Http\Controllers\CustomerSupportController")->only(['index', 'store']);
        Route::get("unread-customer-support-counter", [App\Http\Controllers\CustomerSupportController::class, 'unreadCounter'])->name('customer-support-chat.counter');
    });


    Route::get('bookings/{code}/generate/qr', [App\Http\Controllers\TestController::class, 'generateQr'])->name('generate.receipt.qr');
    Route::get('resetserial', [App\Http\Controllers\TestController::class, 'reset_photos_serial']);
    // payment
    Route::get('payment', [App\Http\Controllers\HomeController::class, 'payment'])->name('payment');
    Route::get('transactions', [App\Http\Controllers\HomeController::class, 'transactions'])->name('transactions');


    //get profile data
    Route::get('getprofiledata', [App\Http\Controllers\PropertyController::class, 'getprofiledata'])->name('getprofiledata');


    // wishlist
    Route::get('new_wishlist', [App\Http\Controllers\HomeController::class, 'new_wishlist'])->name('wishlist.new_wishlist');


    //Account Review Page
    Route::get('user/reviews', [App\Http\Controllers\HomeController::class, 'UserReview'])->name('userreview');
    Route::post('guestrating', [App\Http\Controllers\PropertyController::class, 'RatingByGuest'])->name('rating');
    Route::post('hostrating', [App\Http\Controllers\PropertyController::class, 'RatingByHost'])->name('hostrating');

    //Account Review Page End

    // booking calender
    Route::get('booking_calender/{id}', [App\Http\Controllers\PropertyController::class, 'calender'])->name('booking_calender');

    Route::post('ajax-calender/{id}', [App\Http\Controllers\CalendarController::class, 'calenderJson']);
    Route::post('guestrating', [App\Http\Controllers\PropertyController::class, 'RatingByGuest'])->name('rating');

    Route::post('ajax-calender-price/{id}', [App\Http\Controllers\CalendarController::class, 'calenderPriceSet']);

    Route::get('icalendar/synchronization/{id}', [App\Http\Controllers\CalendarController::class, 'icalendarSynchronization']);
    Route::get('contactadmin', [App\Http\Controllers\HomeController::class, 'contactadmin'])->name('contact.admin');

    // saria apartments
    Route::get('sariaapartments', [App\Http\Controllers\HomeController::class, 'sariaapartments'])->name('sariaapartments');

    Route::get('transaction_failed', [App\Http\Controllers\HomeController::class, 'transaction_failed'])->name('pages.transaction_failed');

    Route::get('/{lang}/{campaign}', [App\Http\Controllers\CampaignController::class, 'campaign'])->name('home.campaign')->where('lang', '^(en|ar)?$');

    //Callback response
    Route::get('admin/payment/callback/{status}/{type?}', [App\Http\Controllers\Admin\PaymentController::class, 'paymentInvoiceCallback'])->name('paymentInvoiceCallback');
});

Route::get('managehost/instruction', [App\Http\Controllers\HomeController::class, 'instruction'])->name('managehost.instruction');

Route::group(['prefix' => 'admin', 'namespace' => 'Admin', 'middleware' => 'no_auth:admin'], function () {


    Route::get('login', [App\Http\Controllers\Admin\AdminController::class, 'login']);
});

Route::post('duplicate-phone-number-check', [App\Http\Controllers\Api\UserController::class, 'duplicatePhoneNumberCheck']);
Route::post('duplicate-phone-number-check-for-existing-customer', [App\Http\Controllers\Api\UserController::class, 'duplicatePhoneNumberCheckForExistingCustomer']);

// booking calender
Route::get('modals', [App\Http\Controllers\HomeController::class, 'modals'])->name('modals');

Route::post('admin/authenticate', [App\Http\Controllers\Admin\AdminController::class, 'authenticate']);

Route::post('paginated/bookings/{type}', [App\Http\Controllers\HomeController::class, 'paginatedBookings'])->name('paginated.bookings');

// notification
Route::get('notification', [App\Http\Controllers\HomeController::class, 'notification'])->name('notification');

// my booking
// Route::group(['middleware' => ['locale']], function () {

// });

Route::post('profile', [App\Http\Controllers\Api\UserController::class, 'profileSave'])->name('user.profile.save');

Route::get('reviews/{type}', [App\Http\Controllers\Api\UserController::class, 'reviewView'])->name('user.reviews.view')
    ->where(['type' => 'about-you|by-you']);


Route::post('documents', [App\Http\Controllers\Api\UserController::class, 'documents'])->name('user.documents');
Route::post('documents/upoad', [App\Http\Controllers\Api\UserController::class, 'documentStore'])->name('user.documents.store');
Route::post('documents/{document}', [App\Http\Controllers\Api\UserController::class, 'documentDelete'])->name('user.documents.delete');
// Route::get('reservation', [App\Http\Controllers\HomeController::class, 'reservation'])->name('reservation');

// personal info
Route::get('myrequest', [App\Http\Controllers\HomeController::class, 'myrequest'])->name('user.myrequest');

// Payment Detail //
// Route::get('payment_detail', [App\Http\Controllers\HomeController::class, 'payment_detail'])->name('pages.payment_detail');


// email form

Route::get('booking', [App\Http\Controllers\HomeController::class, 'booking'])->name('emails.booking');

//Callback response
// Route::get('payment/callback/{status}/{booking_id?}/{type?}', [App\Http\Controllers\PaymentController::class, 'callback'])->name('paymentCallback');
Route::get('payment/callback/{status}', [App\Http\Controllers\PaymentController::class, 'callback'])->name('paymentCallback');
Route::get('payment/callback/{status}/{platform}/{type}', [App\Http\Controllers\PaymentController::class, 'paymentCallbackWallet'])->name('paymentCallbackWallet');

Route::get('payment/initiated', [App\Http\Controllers\PaymentController::class, 'initiated'])->name('payment.initiated');
Route::get('payments/initiated', [App\Http\Controllers\PaymentController::class, 'hyperPayinitiated'])->name('payment.hyperinitiated');

Route::get('invoice/initiated', [App\Http\Controllers\PaymentController::class, 'invoiceInitiated'])->name('invoice.initiated');
// Route::get('booking_extend', [App\Http\Controllers\Admin\BookingsController::class, 'extendedPrice'])->name('bookingExtend');

// User verification
Route::get('users/edit-verification', [App\Http\Controllers\Api\UserController::class, 'verification']);
Route::get('users/confirm_email/{code?}', [App\Http\Controllers\Api\UserController::class, 'confirmEmail']);
Route::get('users/new_email_confirm', [App\Http\Controllers\Api\UserController::class, 'newConfirmEmail']);

Route::post('/set-session-value', [App\Http\Controllers\SessionController::class, 'setSessionValue']);

//for exporting iCalendars
Route::get('icalender/export/{id}', [App\Http\Controllers\Admin\CalendarController::class, 'icalendarExport'])->name('icalExport');


// review fakes
Route::get('new-reviews-fakes', [App\Http\Controllers\HomeController::class, 'newReviews']);
Route::get('geocCode', [App\Http\Controllers\TestController::class, 'geocCode']);

Route::get('captcha-google-v1', [App\Http\Controllers\TestController::class, 'googleV1Captcha']);


// Upload License
Route::post('upload-license/{id}', [App\Http\Controllers\PropertyController::class, 'uploadLicenseV3'])->name('upload.license');
Route::post('clear-license', [App\Http\Controllers\PropertyController::class, 'clearLicense'])->name('clearLicense');

Route::get('setCheckinCheckout/{id}/{start_date}/{end_date}', [App\Http\Controllers\TestController::class, 'setCheckinCheckout']);
Route::get('checkProperty/{id}', [App\Http\Controllers\TestController::class, 'checkProperty']);

if (app()->environment('local')) {
    Route::get('logFile', [App\Http\Controllers\TestController::class, 'logFile']);
    Route::get('clearLogFile', [App\Http\Controllers\TestController::class, 'clearLogFile']);


    Route::get('/fix-orientation', [App\Http\Controllers\TestController::class, 'fixOrientation']);
    Route::post('/fix-orientation', [App\Http\Controllers\TestController::class, 'saveRotatedImage'])->name('fixOrientation');
    Route::get('/imageopt', [App\Http\Controllers\TestController::class, 'imageopt']);
}


Route::get('/unifonicwebhook', [App\Http\Controllers\TestController::class, 'unifonicwebhook']);

// Tamayouz Test
Route::get('/tamayouz', [App\Http\Controllers\TestController::class, 'tamayouz']);

// New Order Mail
Route::get('/new-order-mail', [App\Http\Controllers\TestController::class, 'newOrderTestMail']);


// Hyper Pay Test
Route::get('hyper-pay-bill/{id}', function () {
    $bookinId = request()->id;
    $booking = Bookings::where('id', $bookinId)->first();
    $payAmount = ($booking->total_with_discount == 0) ? $booking->total : $booking->total_with_discount;
    $invoiceUrl = (new HyperBillService())->generateInvoice(
        $payAmount,
        $booking->users->first_name . ' ' . $booking->users->last_name,
        $booking->users->email ?? '<EMAIL>',
        $booking->users->formatted_phone,
        $booking->users->lang,

        $booking->id
    );
    return $invoiceUrl;
});

Route::get('hyperbill-status/{id}', function () {
    $invoice_id = request()->id;
    $invoiceUrl = (new HyperBillService())->GetInvoiceStatus($invoice_id);
    return $invoiceUrl;
});
Route::get('/run-hyperbill-cronjob', function () {
    Artisan::call('cron:hyperbill-check-payment-status');
    return "Hyperbill payment status check executed.";
});
Route::get('/send-notification-on-checkout', function () {
    Artisan::call('send:notification-on-checkout');
    return "Check out Notification sent successfully.";
});
Route::get('/delete-hold-dates', function () {
    Artisan::call('cron:check-hold-bookings');
    return "Booking Dates Deleted Successfully.";
});

Route::get('/run-pay-cronjob', function () {
    Artisan::call('payments:verify-pending');
    return "HypePay payment status check executed.";
});

// Route::get('/check-availability', function () {
//     Artisan::call('mabaat:check-availability');
//     return "Mabaat availability check executed.";
// });

Route::get('/check-availability', function () {
    Artisan::call('mabaat:check-availability');
    return "Mabaat availability check executed.";
});


Route::get('apple-pay-mobile', [App\Http\Controllers\PaymentController::class, 'paymentMobileSuccess'])->name('payment.mobilesuccess');
Route::get('mobile-wallet-deposit', [App\Http\Controllers\PaymentController::class, 'walletMobiledepositSuccess'])->name('mobile.wallet.deposit.success');





// Route::get('/{lang?}/.well-known/apple-developer-merchantid-domain-association{extension?}', function ($lang = null, $extension = '') {
//     // Get the current app version from settings
//     $currentAppVersion = getSetting('web_version');

//     $filePath = '';

//     if (version_compare($currentAppVersion, '2.0.0', '<')) {
//         $filePath = public_path('.well-known/moyasar-apple-developer-merchantid-domain-association');
//     } else {
//         $filePath = public_path('.well-known/hyperpay-apple-developer-merchantid-domain-association.txt');
//     }

//     if (File::exists($filePath)) {
//         return new Response(File::get($filePath), 200, [
//             'Content-Type' => 'text/plain',
//         ]);
//     }

//     return abort(404);
// })->where('lang', 'en|ar')->where('extension', '(\.txt)?');


Route::get('add/banner/prefix', function () {
    $banners = App\Models\Banners::all();
    $prefix = 'images/banners/';
    foreach ($banners as $banner) {
        if (strpos($banner->image, $prefix) !== false) continue;
        App\Models\Banners::where('id', $banner->id)->update(['image' => $prefix . $banner->image]);
    }
    return 'done';
});



if (app()->environment('local')) {


    Route::get('send-license-notification', function () {
        dispatch_now(new LicenseVerificationWarningNotification());
        return 'done';
    });
}

if (app()->environment('local')) {


    // web.php or api.php route file






    Route::get('cancel-kease-res', function (Request $request) {
        $bookings = Bookings::where('id', operator: 53252)->first();
        $data = new App\Http\Services\KEASE\KeaseService();
        $resposne = $data->CancelReservation($bookings->thirdparty_reservation);
        return $resposne;
        // $data = (new App\Http\Services\HyperpaySdkService())->refund($bookings->transaction_id, 10, 'test refund');
        // return $data;
    });

    Route::get('upsertBookedDays', function (Request $request) {
        $data = new App\Http\Services\KEASE\KeaseService();
        $resposne = $data->upsertBookedDays(1965);
        return $resposne;
        // $data = (new App\Http\Services\HyperpaySdkService())->refund($bookings->transaction_id, 10, 'test refund');
        // return $data;
    });



    Route::get('send-big-query-logs-table', function (Request $request) {
        Artisan::call('archive:bigquery-parallel');
        return 'send-big-query-logs-table:sync';
    });

    Route::get('/distribute-campaign-credits', function () {
        DistributeCampaignCredits::dispatch();
        return response()->json(['message' => 'Campaign credit distribution job queued']);
    });

    Route::get('/process-cashback-expiry', function () {
        dispatch(new CashbackExpiryJob());
        return response()->json(['message' => 'Cashback expiry job queued']);
    });
}

Route::get('license-based:update-properties', function (Request $request) {
    Artisan::call('license-based:update-properties');
    return 'send-big-query-logs-table:sync';
});


Route::get('seed-kease-rentals', function (Request $request) {
    // Artisan::call('rentals:sync');
    $page = $request->page ?? 1;
    $data = new App\Http\Services\KEASE\KeaseService();
    $resposne = $data->fetchRentals($page);
    return 'rentals:sync';
});
Route::get('fetchAndStoreRentalRates', function (Request $request) {
    Artisan::call('rental-rates:queue-fetch');
    return "rental-rates:queue-fetch.";
});

// Modified Route
Route::get('optimize-property-images', function (Request $request) {
    try {
        // Get all unique property IDs
        $propertyIds = PhotosTemp::select('property_id')
            ->distinct()
            ->join('properties', 'photos_temp.property_id', '=', 'properties.id')
            ->where('properties.platform_id', 6)
            ->pluck('property_id');

        // Dispatch jobs with delay between each property
        foreach ($propertyIds as $index => $propertyId) {
            OptimizePropertyImages::dispatch($propertyId)
                ->delay(now()->addSeconds($index * 30)); // 30 seconds delay between properties
        }

        return response()->json([
            'success' => true,
            'message' => 'Image optimization jobs queued successfully',
            'total_properties' => count($propertyIds)
        ]);
    } catch (Exception $e) {
        return response()->json([
            'success' => false,
            'error' => $e->getMessage()
        ], 500);
    }
});


Route::get('test-cache', function () {
    // If using Redis
    $cacheKeys = Cache::get('logger_cache_keys', []);

    foreach ($cacheKeys as $key) {
        Cache::forget($key);
    }

    Cache::forget('logger_cache_keys'); // Clear the tracking key itself

    // $this->info('Logger event cache cleared.');
    Log::info('Logger event cache cleared.');
});

// Tracking routes
Route::post('booking/track/started', [App\Http\Controllers\TrackingController::class, 'trackBookingStarted'])->name('booking.track.started');
Route::post('booking/track/start', [App\Http\Controllers\TrackingController::class, 'trackStartBooking'])->name('booking.track.start');

/*
|--------------------------------------------------------------------------
| Search V5 Routes
|--------------------------------------------------------------------------
|
| New search implementation routes for gradual migration
|
*/

// V5 Search Routes
Route::post('search/v5/results', [App\Http\Controllers\SearchV5Controller::class, 'search'])
    ->name('search.v5.results');

Route::get('search/v5/count', [App\Http\Controllers\SearchV5Controller::class, 'count'])
    ->name('search.v5.count');

Route::get('search/v5/filters', [App\Http\Controllers\SearchV5Controller::class, 'availableFilters'])
    ->name('search.v5.filters');

// Feature flag route that can switch between V3 and V5
Route::post('search/results', function (Illuminate\Http\Request $request) {
    $useV5 = false;

    // Check feature flag
    if (config('search.features.v5_enabled')) {
        $useV5 = true;
    }

    // Check for specific users/sessions
    if ($request->session()->get('use_search_v5')) {
        $useV5 = true;
    }

    // Check for percentage rollout
    $userId = auth()->id() ?? $request->ip();
    if (crc32($userId) % 100 < config('search.features.v5_percentage', 0)) {
        $useV5 = true;
    }

    // Force V5 for testing with query parameter
    if ($request->get('force_v5') === 'true' && config('app.debug')) {
        $useV5 = true;
    }

    if ($useV5) {
        return app(\App\Http\Controllers\SearchV5Controller::class)->search($request);
    }

    return app(\App\Http\Controllers\SearchController::class)->searchResultV3($request);
})->name('search.results');

include __DIR__ . '/v0/web.php';