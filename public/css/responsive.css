/* global css */
@font-face {
    font-family: 'dubai-font';
    font-style: normal;
    font-weight: 300;
    font-display: swap;
    src: url('../fonts/dubai-font/Dubai-Light.ttf')
}

@font-face {
    font-family: 'dubai-font';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url('../fonts/dubai-font/Dubai-Regular.ttf')
}

@font-face {
    font-family: 'dubai-font';
    font-style: normal;
    font-weight: 500;
    font-display: swap;
    src: url('../fonts/dubai-font/Dubai-Medium.ttf')
}

@font-face {
    font-family: 'dubai-font';
    font-style: normal;
    font-weight: 600;
    font-display: swap;
    src: url('../fonts/dubai-font/Dubai-Bold.ttf')
}

.cm-sm-width {
    width: 400px !important;
}

.cm-lg-width {
    max-width: 620px !important;
}

.single-modal-content {
    font-size: 17px;
}

.fs-16 {
    font-size: 16px;
}

.fs-17 {
    font-size: 17px;
}

.dgreen-clr {
    color: #7AA826;
}

.color-success {
    color: #23BC4C !important;
}

.ob-br {
    object-fit: cover;
    border-radius: 50%;
}

.ts-back-btn {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.ts-back-btn img {
    margin-right: 10px;
    width: 10px;
}

.ts-back-btn span {
    font-size: 17px;
    color: #181818;
    font-size: 18px;
    font-weight: 500;
}

.df-align-center {
    display: flex;
    align-items: center;
}

.df-justify-between {
    display: flex;
    justify-content: space-between;
}

.success-green-color {
    color: var(--success-green);
}

.warning-red-color {
    color: var(--warning-red);
}

.box-shad-none {
    box-shadow: none !important;
}

.p-button {
    font-family: 'dubai-font' !important;
}

.bd-left-none {
    border-left: none !important;
}

/*border modal css*/
.cm-bd-content {
    border-radius: 15px;
}

.cm-bd-header {
    border-color: #D1D1D1;
    padding: 0 35px;
    height: 65px;
}

.cm-bd-header h5 {
    font-size: 22px;
    color: #000;
}

.cm-bd-header .btn-close {
    border: none;
    font-size: 15px;
}

.cm-bd-body {
    padding: 25px 35px;
}

.cm-bd-footer {
    border-color: #D1D1D1;
    padding: 15px 35px;
    margin: 0;
}

.cm-footer-two {
    justify-content: space-between;
}

.cm-footer-two button {
    max-width: max-content;
}

.modal-footer>* {
    margin: 0;
}

.modal-form-control {
    border-radius: 10px;
    border: 1px solid #8A8A8A;
    height: 55px;
    padding: 10px 20px;
    width: 100%;
    color: #000;
}

.modal-form-control::placeholder {
    font-weight: 500;
    color: #8A8A8A;
}

.logout-btn {
    width: 80%;
    margin-top: 18px;
    font-size: 18px;
}

/*border modal css end*/

/*simple modal css*/
.cm-simple-content {
    border-radius: 10px;
}

.cm-simple-header {
    padding: 35px 30px 20px;
    border: none;
    position: relative;
}

.cm-simple-body {
    padding: 0 30px 40px;
}

.cm-simple-header .btn-close {
    position: absolute;
    right: 50px;
}

/* simple modal css end */
.input-withlable {
    display: flex;
    align-items: center;
    box-shadow: 5.5px 3.5px 16px rgba(149, 157, 165, 0.2);
    border-radius: 16px;
    padding: 0 20px;
    height: 45px;
    white-space: nowrap;
    color: #000000;
    font-weight: 500;
    font-size: 14px;
    margin-bottom: 5px;
}

.input-withlable input {
    border: none;
    outline: none;
}

.input-withlable select {
    border: none;
    outline: none;
}

.input-withlable input:focus {
    outline: none !important;
    border: unset !important;
}

.input-withlable select:focus {
    outline: none !important;
    border: unset !important;
}

.input-withlable select {
    color: #000;
    font-weight: 500;
}

.input-withlable input::placeholder {
    color: #000;
    text-align: center;
}

.dark-grey {
    color: #575757;
}

/* scroll bar */
.bt-grey-scroller ::-webkit-scrollbar {
    width: 8px;
}

.bt-grey-scroller ::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 25px;
}

.bt-grey-scroller ::-webkit-scrollbar-track {
    border-radius: 0;
    background: transparent;
    box-shadow: unset;
}

/* scroll bar end*/





.enable-md-btn.disabled {
    background-color: #8A8A8A;
    cursor: not-allowed;
    pointer-events: none;
}

.enable-md-btn {
    border: none;
}

.cm-bd-footer button {
    height: 44px;
    width: 100%;
    font-size: 18px;
    letter-spacing: .4px;
}

.wishlist-listing {
    padding: 10px 10px 10px 0;
    border-radius: 8px;
    margin-bottom: 10px;
    transition: .3s ease-in-out;
    cursor: pointer;
}

.wishlist-listing:hover {
    background: #EDEDED;
    padding-left: 10px;
}

.wlisting-inner {
    display: flex;
    align-items: center;
}

.wlisting-inner img {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 10px;
    margin-right: 8px;
}

.wlisting-inner h5 {
    flex: 1;
}


/* new designing css  */
.dubai-ff {
    font-family: 'dubai-font';
}

.pg-main-title {
    font-weight: 500;
    margin-bottom: 25px;
}

.no-wishlist-content h4 {
    color: #181818;
    margin-bottom: 18px
}

.no-data p {
    font-size: 18px;
    color: #8A8A8A;
}

.wh-board-height {
    height: 235px;
}

.wishlist-board {
    display: block;
    border-radius: 15px;
    overflow: hidden;
}

.wb-left {
    height: 100%;
    width: 100%;
}

.wb-left img {
    height: 100%;
    width: 100%;
    object-fit: cover;
}

.wb-right {
    width: 100%;
    height: 117.5px;
}

.wb-right img {
    width: 100%;
    height: 100%;
}

.create-wb {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    background-image: url("data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100%25' height='100%25' fill='none' rx='15' ry='15' stroke='%23909090FF' stroke-width='3' stroke-dasharray='6%2c 14' stroke-dashoffset='23' stroke-linecap='square'/%3e%3c/svg%3e");
    border-radius: 15px;
    width: 100%;
}

.create-wb img {
    width: 75px;
}

.create-wb p {
    margin-top: 18px;
    font-size: 27px;
    font-weight: 500;
    color: #909090;
}

.input-mx-content {
    color: #8A8A8A;
    margin-top: 10px;
    display: block;
    font-size: 14px;
}

.wl-right-btn {
    display: flex;
    align-items: center;
}

.wl-left-btn img {
    width: 14px;
}

.wl-right-btn button:nth-child(1) img {
    width: 15px;
}

.wl-right-btn button:nth-child(2) img {
    width: 21px;
    margin-left: 15px;
}

.sl-btn {
    display: flex;
    align-items: center;
    border-radius: 10px;
    border: 1px solid #8A8A8A;
    width: 100%;
    padding: 20px;
    transform: scale(1);
    transition: .3s all;
}

.sl-btn:hover {
    border-color: #000;
}

.sl-btn img {
    width: 40px;
    margin-right: 20px;
}

.sl-btn .sl-content {
    text-align: start;
}

.share-si h4 {
    margin: 25px 0 20px;
}

.share-si ul {
    display: flex;
    align-items: center;
    margin: 0;
}

.share-si ul li {
    margin-right: 30px;
}

.share-si ul li a img {
    width: 48px;
}

.modal-delete-btn {
    font-size: 18px;
    font-weight: 500;
    text-decoration: underline;
}

.dwc-inner {
    position: relative;
}

.dwc-inner label {
    position: absolute;
    top: 11px;
    left: 20px;
    color: #8A8A8A;
}

.dwc-inner input {
    padding-top: 28px;
    height: 75px;
}

.wishlist-listing-sec {
    height: calc(100vh - 72px);
    overflow: hidden;
}

.wishlist-map {
    height: 100%;
}

.wl-property {
    overflow-y: auto;
    overflow-x: hidden;
    height: 92vh;
    padding: 0 25px 0 35px;
}

.wl-property::-webkit-scrollbar {
    display: none;
}

.guest-btn {
    background-color: #D1D1D1;
    border: none;
    color: #fff;
    font-size: 17px;
    height: 47px;
    border-radius: 7px;
}

.guest-btn:hover {
    color: #fff;
}

.cancel-addcard img {
    width: 12px;
    position: absolute;
    left: 30px;
    top: 59%;
    transform: translateY(-50%);
}

.wallet-btn {
    padding: 0 45px;
}

.cd-darent h6 {
    color: #575757;
    font-size: 18px;
    margin: 35px 0 18px;
}

.cd-darent p {
    font-size: 17px;
    color: #575757;
}

.card-num p {
    color: #575757;
}

.property-notify {
    display: flex;
    align-items: self-start;
    border-bottom: 1px solid #E2E2E2;
}

.property-nt-img {
    width: 8%;
    height: 80px;
    margin-right: 20px;
}

.property-nt-img img {
    height: 100%;
    width: 100%;
    border-radius: 10px;
    object-fit: cover;
}

.property-nt-content {
    width: 72%;
}

.pt-nt-userdetail {
    display: flex;
    align-items: center;
}

.mid-content {
    font-size: 18px;
    display: block;
    margin: 0 8px;
    color: #575757;
}

.nt-mini-userimg {
    width: 27px !important;
    margin-right: 8px !important;
}

.pt-nt-date {
    width: 20%;
    display: flex;
    align-items: center;
    height: 80px;
    justify-content: end;
}

.notification-user:last-child {
    border-bottom: 0;
}

.side-inner li a.on-pg i {
    font-weight: 600;
}

.side-inner li a:hover {
    color: #000000;
    font-weight: 500;
}

.side-inner li a:hover i {
    font-weight: 600;
}

.pro-name {
    color: #575757;
}

.change-pro-btn {
    border: 1px solid #575757;
    border-radius: 10px;
    text-decoration: none !important;
}

.ac-dt h5 {
    font-size: 17px;
    margin-bottom: 13px;
}

.ls-icon-main {
    margin: 0 auto;
    border-radius: 50%;
    box-shadow: 2.5px 3.5px 15px rgba(149, 157, 165, 0.2);
    width: 55px;
    height: 53px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.ls-icon {
    width: 31px;
    ;
}

.ls-icon-mobile {
    width: 19px;
}

.circle-check {
    position: absolute;
    bottom: 7px;
    right: -8%;
    transform: translateX(-50%);
    width: 17px;
    background: #fff;
    border-radius: 50%;
}

.nic-btn {
    height: 40px;
    font-size: 14px;
    margin-bottom: 15px;
    font-weight: 500;
    color: #000000;
}

.theme-user-rate {
    margin-left: 15px;
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: baseline;
    line-height: 1;
}

.theme-user-rate img {
    width: 17px;
    margin-right: 6px;
}

.account-tab .nav-pills {
    border-radius: 0 !important;
    color: #000;
    border-bottom: 1px solid #E2E2E2;
    padding-bottom: 25px;
}

.shadow-tabs .nav-pills .nav-link {
    transition: .3s ease-in;
    margin-left: 5px;
}

.shadow-tabs .nav-pills .nav-link:hover {
    background: #fff;
    color: #000;
    font-weight: 500;
    box-shadow: 2.5px 3.5px 15px rgba(149, 157, 165, 0.2);
}

.shadow-tabs .nav-pills .nav-link.active,
.grey-tabs .nav-pills .show>.nav-link {
    background: #fff;
    color: #000;
    font-weight: 500;
    box-shadow: 2.5px 3.5px 15px rgba(149, 157, 165, 0.2);
}

.ac-radio {
    align-items: end !important;
    margin-bottom: 10px;
}

.ac-radio input {
    border: 1px solid #707070 !important;
}

.ac-radio p {
    color: #575757;
}

.acc-inner ul li .ac-dt input,
textarea {
    color: #575757 !important;
}

.list-right-img img {
    width: 90px;
    height: 95px;
    border-radius: 10px;
    object-fit: cover;
}

.listing-layout-one {
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #E2E2E2;
    padding-bottom: 35px;
    margin-bottom: 18px;
}

.ll-one-left {
    width: 60%;
    display: flex;
    align-items: flex-start;
}

.ll-one-left .pr-img img {
    width: 35px !important;
    height: 35px !important;
}

.ll-one-right {
    width: 30%;
    text-align: end;
}

.ll-one-create {
    color: #181818;
}

.ll-one-smallrate {
    color: #000000;
    font-weight: 500;
    margin: 7x 0 !important;
}

.ll-one-smallrate img {
    margin-right: 8px;
    width: 14px;
}

.listing-layout-two {
    border-top: 0 !important;
    border-left: 0 !important;
    border-right: 0 !important;
    border-bottom: 1px solid #E2E2E2;
    border-radius: 0;
}

.ll-tl-img {
    height: 115px;
    width: 160px;
    margin-right: 20px;
}

.ll-tl-img img {
    width: 100% !important;
    height: 100% !important;
}

.ll-two-left .date-mark {
    color: #7AA826;
}

.ll-two-left .date-mark i {
    color: #181818;
}

.ll-two-left .list-descrip {
    font-size: 17px;
    margin: 5px 0 6px;
}

.ll-two-right .mini-profile {
    display: flex;
    align-items: center;
    /* padding: 10px 0 0; */
    justify-content: start;
}

.ll-two-right .mini-profile img {
    width: 50px;
    height: 50px;
}

.ps-btn {
    height: 34px;
    font-size: 16px;
    margin-right: 15px;
    font-weight: 500;
}

.ll-two-left .list-price {
    font-size: 17px;
}


/* filter modal view blad page end */
.filter-padding {
    padding: 20px 35px;
    border-bottom: 1px solid #D1D1D1;
}

.filter-padding .main-title {
    margin-bottom: 20px;
}

.filter-padding .inner-main-title {
    font-size: 18px;
    margin-bottom: 10px;
}

.type-place {
    padding: 0;
    margin: 0;
    border-radius: 10px;
    display: flex;
    align-items: center;
    max-width: max-content;
}

.input-container {
    position: relative;
    height: 68px;
    width: 180px;
}

.input-container .radio-button {
    opacity: 0;
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    margin: 0;
    cursor: pointer;
}

.input-container .radio-tile {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    border: 1px solid #707070;
    transition: transform 300ms ease;
}

.input-container .radio-tile-label {
    text-align: center;
    font-size: 15px;
    font-weight: 400;
    color: #000000;
}

.input-container .radio-button:checked+.radio-tile {
    background-color: var(--theme-primary);
    border: 2px solid var(--theme-primary);
    color: white;
}

.input-container .radio-button:checked+.radio-tile .radio-tile-label {
    color: white;
}

.type-place .input-container:nth-child(1) .radio-tile {
    border-top-left-radius: 10px;
    border-bottom-left-radius: 10px;
    border-right: 0;
}

.type-place .input-container:nth-child(3) .radio-tile {
    border-top-right-radius: 10px;
    border-bottom-right-radius: 10px;
    border-left: 0;
}

.type-place-main p {
    font-size: 14px;
    color: #000000 !important;
    margin: 10px 0 0;
}

.editable-rang {
    border: none;
    outline: none;
}

.editable-rang:focus {
    border: none !important;
    outline: none !important;
    box-shadow: none !important;
}

.price-btn.editable-border {
    border: 1px solid #000000;
}

.recommended-checkbox {
  appearance: none;
  -webkit-appearance: none;
  display: none;
}

.recommended-label {
  border: 1px solid #ddd;
  border-radius: 12px;
  padding: 15px;
  text-align: center;
  cursor: pointer;
  transition: transform 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-weight: 500;
}

.recommended-label img {
  width: 40px;
  height: 40px;
  margin-bottom: 10px;
}
.recommended-content{
    text-align: center;
    margin-top: 5px;
    color: #000000;
}
.recommended-label:hover {
  border-color: #000000;
  background-color: #FFFBF2;
}

.recommended-checkbox:checked + .recommended-label {
  border: 1px solid black;
  outline: 1px solid black;
  background-color: #FFFBF2;
  animation: zoomEffect 0.3s ease;
}

@keyframes zoomEffect {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.95);
  }
  100% {
    transform: scale(1);
  }
}
.type-place-btn-group {
    border: 1px solid #ddd;
    border-radius: 12px;
    overflow: hidden;
    padding: 5px;
}

.type-place-toggle-btn input[type="radio"] {
    display: none;
}

.type-place-toggle-btn label {
    padding: 10px 20px;
    margin: 0;
    cursor: pointer;
    background-color: transparent;
    border: none;
    font-weight: 500;
    width: 100%;
    text-align: center;
    border-radius: 9px;
    border: 2px solid transparent; 
}

.type-place-toggle-btn input[type="radio"]:checked + label {
    background-color: #FFFBF2;
    border-color: #000000;
}

.type-place-toggle-btn label:hover {
    background-color: #FFFBF2;
}

/* search property */
.ls-fltr-feild .form-control {
    border: none;
    border-bottom: 1px solid #c5c5c5;
    border-radius: 0;
    padding: 10px 0;
    color: #000;
}

.ls-fltr-feild .form-control::placeholder {
    color: #000;
}

.ls-fltr-feild .form-control:focus {
    border: none !important;
    border-bottom: 1px solid #000 !important;
    outline: none !important;
}

.district-slt-picker-main .btn-light.dropdown-toggle {
    background-color: transparent;
    border: none;
    border-bottom: 1px solid #c5c5c5;
    border-radius: 0;
    padding: 9px 15px 6px 0;
    height: 40px;
}

.district-slt-picker-main .bs-select-all {
    margin-right: 5px;
}

.bs-actionsbox .btn-group button {
    width: 49%;
}

.district-slt-picker-main .btn-light.dropdown-toggle:hover {
    background: transparent;
    border-color: #c5c5c5;
}

.district-slt-picker-main .btn-light.dropdown-toggle:focus {
    outline: none !important;
    border-color: #000;
}

.district-slt-picker-main .btn-group {
    display: flex;
}

.district-slt-picker-main .district-slt-picker {
    width: 100% !important;
}

.district-slt-picker-main .bootstrap-select>select {
    left: 0 !important;
}

.district-slt-picker-main .dropdown-menu {
    width: 100%;
}

.district-slt-picker-main .dropdown-menu .dropdown-menu.inner .dropdown-item {
    display: flex;
}

.district-slt-picker-main .dropdown-menu .dropdown-menu.inner .dropdown-item .text {
    flex: 0 0 95%;
    max-width: 95%;
    display: -webkit-box !important;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: break-spaces;
}

.district-slt-picker-main .filter-option-inner-inner {
    color: #000;
}

.district-slt-picker-main .dropdown-toggle::after {
    display: inline-block;
    content: "\f078";
    border: none;
    font-weight: 900;
    font-family: 'Font Awesome 5 Free';
    color: #000;
    position: absolute;
    right: 0;
}

/*room and bed radio button*/
.rb-input-container {
    position: relative;
    height: 30px;
    width: 52px;
    margin-right: 11px;
}

.rb-input-container:last-child {
    margin-right: 0;
}

.rb-input-container .rb-radio-button {
    opacity: 0;
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    margin: 0;
    cursor: pointer;
}

.rb-input-container .rb-radio-tile {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    border: 1px solid #707070;
    transition: transform 300ms ease;
    border-radius: 25px;
}

.rb-input-container .rb-radio-tile-label {
    text-align: center;
    font-size: 15px;
    font-weight: 400;
    color: #181818;
}

.rb-input-container .rb-radio-button:checked+.rb-radio-tile {
    background-color: var(--theme-primary);
    border: 1px solid var(--theme-primary);
    color: white;
}

.rb-input-container .rb-radio-button:checked+.rb-radio-tile .rb-radio-tile-label {
    color: white;
}

.thm-check.cust-form-check-input:checked[type=checkbox] {
    /* background-image: url('../icons/theme-check.svg') !important;  Due to global design*/
    background-size: 10px
}

.thm-check.form-check-input {
    border-radius: 5px !important;
    border: 1px solid #707070 !important;
    margin-top: 0 !important;
}

.thm-check.form-check-input:focus {
    border-color: #707070;
}

.thm-check.cust-form-check-input:focus {
    border-color: #707070 !important;
}

.thm-check.cust-form-check-input:checked {
    border-color: #707070 !important;
}

.sm-price-filter {
    padding: 10px 20px;
    z-index: 9999;
    border: none;
}

.sm-price-filter li a {
    padding: 10px 0;
    color: #000000;
    border-bottom: 1px solid #707070;
    font-size: 17px;
}

.sm-price-filter li a:hover {
    background-color: transparent;
}

.sm-price-filter li:last-child a {
    border-bottom: none;
}

.rprt-btn i {
    margin-right: 8px;
}

.rprt-btn span {
    display: block;
    font-weight: 500;
}

.overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgb(0 0 0 / 23%);
    /* Adjust the opacity as needed */
    z-index: 9999;
    /* Make sure the z-index is higher than the dropdown menu */
    overflow: hidden;
}

.dropdown-menu.show+.overlay {
    display: block;
}

/* filter modal view blad page end */



/* profile book modal*/
#md-profile-book .modal-body::-webkit-scrollbar {
    display: none !important;
}

.profile-modal-main {
    background: #ffff;
}

.md-profile-box {
    background-color: #ffff;
    border-radius: 20px;
    padding: 25px 0;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.md-pb-profile {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    height: 100%;
}

.md-pb-pimg {
    position: relative;
}

.md-pb-profile .up {
    width: 90px;
    border-radius: 50%;
    object-fit: cover;
}

.md-pb-pcontent {
    text-align: center;
    margin: 10px 0;
}

.md-pb-pcontent h2 {
    line-height: 1;
}

.md-pb-pcontent p {
    color: #000000;
}

.md-pb-pstatus {
    margin: 0;
    padding: 0 20px 0 0;
}

.md-pb-pstatus hr {
    margin: 0.5rem 0;
}

.md-pb-pstatus li h6 {
    font-size: 22px;
    line-height: 1;
}

.md-pb-pstatus li p {
    line-height: 1.1;
    font-size: 13px;
}

.md-pb-verfy {
    width: 22px;
    bottom: 5px;
    right: -13%;
}

.md-pb-width {
    width: 400px !important;
}

.md-pb-bio ul {
    margin: 30px 0px 0px;
}

.md-pb-bio ul li {
    margin-bottom: 12px;
    display: flex;
    align-items: flex-start;
    color: #222222;
}

.md-pb-bio ul li span {
    display: inline-block;
}

.md-pb-bio ul li .md-pb-bioicon {
    width: 30px;
    margin-right: 7px;
}

.md-pb-bio ul li span i {
    font-size: 22px;
}

.md-pb-bio ul li .md-pb-biocontent {
    font-size: 17px;
    flex: 1;
}

.md-pb-bio p {
    color: #222222;
    font-size: 17px;
}

.md-rb-usreview img {
    width: 42px;
    height: 42px;
    margin-right: 10px;
}

.md-rb-content {
    padding: 20px;
    border: 1px solid #D1D1D1;
    border-radius: 10px;
}

.md-rb-body button {
    border: 1px solid #000000;
    width: 100%;
    padding: 15px 0;
    border-radius: 10px;
    font-size: 18px;
    font-weight: 500;
    margin: 20px 0;
}

.md-rb-rev {
    color: #222222;
}

.md-user-identity ul {
    margin: 0;
}

.md-user-identity ul li {
    display: flex;
    align-items: center;
    color: #222222;
}

.md-user-identity ul li i {
    margin-right: 10px;
    font-size: 23px;
}

.md-user-identity ul li p {
    color: #222222;
    font-size: 18px;
}

.md-user-about ul {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex-flow: wrap;
}

.md-user-about ul li {
    border-radius: 25px;
    color: #222222;
    border: 1px solid #D1D1D1;
    padding: 5px 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 10px 10px 0;
}

.md-user-about ul li i {
    margin-right: 10px;
    font-size: 21px;
}

.md-ul-body .product .product-img {
    height: 150px !important;
}

.mdUlSlider .product .product-detail .title h4 {
    flex: 0 0 55%;
}

.mdUlSlider .product .product-detail .title h4 .product-rate {
    align-items: baseline;
}

.headBtn {
    position: relative;
}

.headBtn .swiper-button-next,
.headBtn .swiper-button-prev {
    border: 1px solid #D1D1D1;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    top: var(--swiper-navigation-top-offset, -18%);
}

.headBtn .swiper-button-prev {
    left: auto;
    right: 40px;
}

.headBtn .swiper-button-next {
    left: auto;
    right: 0px;
}

.headBtn .swiper-button-next:after,
.headBtn .swiper-button-prev:after {
    font-size: 10px;
    color: #000000;
    font-weight: 700;
}


.headBtn .swiper-button-next-pro,
.headBtn .swiper-button-prev-pro {
    border: 1px solid #D1D1D1;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    top: var(--swiper-navigation-top-offset, -9%);
}

.headBtn .swiper-button-prev-pro {
    left: auto;
    right: 40px;
}

.headBtn .swiper-button-next-pro {
    left: auto;
    right: 0px;
}

.headBtn .swiper-button-next-pro:after,
.headBtn .swiper-button-prev-pro:after {
    font-size: 10px;
    color: #000000;
    font-weight: 700;
}

.swiper-button-next-pro,
.swiper-button-prev-pro {
    position: absolute;
    top: var(--swiper-navigation-top-offset, 50%);
    width: calc(var(--swiper-navigation-size)/ 44 * 27);
    height: var(--swiper-navigation-size);
    margin-top: calc(0px - (var(--swiper-navigation-size)/ 2));
    z-index: 10;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--swiper-navigation-color, var(--swiper-theme-color))
}

.swiper-button-next-pro.swiper-button-disabled,
.swiper-button-prev-pro.swiper-button-disabled {
    opacity: .35;
    cursor: auto;
    pointer-events: none
}

.swiper-button-next-pro.swiper-button-hidden,
.swiper-button-prev-pro.swiper-button-hidden {
    opacity: 0;
    cursor: auto;
    pointer-events: none
}

.swiper-navigation-disabled .swiper-button-next-pro,
.swiper-navigation-disabled .swiper-button-prev-pro {
    display: none !important
}

.swiper-button-next-pro svg,
.swiper-button-prev-pro svg {
    width: 100%;
    height: 100%;
    object-fit: contain;
    transform-origin: center
}

.swiper-rtl .swiper-button-next-pro svg,
.swiper-rtl .swiper-button-prev-pro svg {
    transform: rotate(180deg)
}

.swiper-button-prev-pro,
.swiper-rtl .swiper-button-next-pro {
    left: var(--swiper-navigation-sides-offset, 10px);
    right: auto
}

.swiper-button-next-pro,
.swiper-rtl .swiper-button-prev-pro {
    right: var(--swiper-navigation-sides-offset, 10px);
    left: auto
}

.swiper-button-lock {
    display: none
}

.swiper-button-next-pro:after,
.swiper-button-prev-pro:after {
    font-family: swiper-icons;
    font-size: var(--swiper-navigation-size);
    text-transform: none !important;
    letter-spacing: 0;
    font-variant: initial;
    line-height: 1
}

.swiper-button-prev-pro:after,
.swiper-rtl .swiper-button-next-pro:after {
    content: 'prev'
}

.swiper-button-next-pro,
.swiper-rtl .swiper-button-prev-pro {
    right: var(--swiper-navigation-sides-offset, 10px);
    left: auto
}

.swiper-button-next-pro:after,
.swiper-rtl .swiper-button-prev-pro:after {
    content: 'next'
}

.md-rb-head h4 {
    margin-right: 75px;
}

/*profile report modal */
.pr-content ul {
    margin: 35px 0 10px;
}

.pr-content ul li {
    border-bottom: 1px solid #D1D1D1;
    padding-bottom: 20px;
    margin-bottom: 20px;
}

.pr-content ul li:last-child {
    border-bottom: none;
    margin: 0;
    padding: 0;
}

.pr-content .pr-radiobtn {
    justify-content: space-between;
    padding-left: 0;
}

.pr-content .pr-radiobtn .cust-check label {
    margin-left: 0;
}

.pr-content .pr-radiobtn label p {
    color: #000000;
    font-size: 17px;
}

/* profile review modal */
.md-gr-tb {
    padding-bottom: 15px !important;
}

.md-gr-tb .nav-item:first-child .nav-link {
    margin-left: 0 !important;
}

.md-gr-tbContent .reviews {
    border-bottom: 1px solid #D1D1D1;
}

.md-gr-tbContent .reviews:last-child {
    border-bottom: none;
}

.md-gr-tbContent .pr-img img {
    width: 50px;
    height: 50px;
}

.md-rev-tb-img img {
    width: 90px;
    height: 55px;
    border-radius: 10px;
    object-fit: cover;
}

.md-gr-tbContent .pr-mini-detail {
    flex: 1;
}

.md-hr-tbContent .reviews {
    margin-bottom: 19px;
}

.single-review-count {
    display: inline-block;
    font-size: 17px;
    margin-left: 7px;
    position: relative;
}

.single-review-count::before {
    content: "";
    position: absolute;
    top: 48%;
    left: -4px;
    background-color: gray;
    transform: translateY(-50%);
    height: 3px;
    width: 3px;
    border-radius: 50%;
    transition: .3s all;
}

.shadow-tabs .nav-pills .nav-link:hover .single-review-count::before {
    background-color: #000000;
}

.shadow-tabs .nav-pills .nav-link.active .single-review-count::before {
    background-color: #000000;
}

.copiedLink {
    background-color: #EDEDED;
    border: 1px solid #EDEDED;
}

.copiedLink:hover {
    background-color: #EDEDED;
    border: 1px solid #EDEDED;
}

#phoneError {
    width: 100%;
    margin-top: 0.25rem;
    font-size: .875em;
    color: #dc3545;
}

#otpError {
    width: 100%;
    margin-top: 0.25rem;
    font-size: .875em;
    color: #dc3545;
}

/*resevation reviews modal*/
/* .reserv-reviews{
  border-bottom: 1px solid #D1D1D1;
} */
.bg-theme-color {
    background-color: var(--theme-primary);
}

.rev-descrip p {
    border-radius: 5px;
    color: #fff;
    font-size: 14px !important;
}

.reserv-prop-img img {
    height: 130px !important;
}

.reserv-lvl {
    margin: 15px 0;
}

.reserv-input-lvl {
    height: 33px;
    width: 100% !important;
}

.reserv-lvl-content p {
    font-size: 13px;
    font-weight: 500;
    color: #000000;
}

.md-back-btn img {
    width: 13px;
}

.otp-in-text {
    color: #000000 !important;
}

.content-point {
    padding-left: 30px;
    margin-top: 10px;
    list-style: inherit;
}

.second-home {
    display: flex;
    background: #f5c33e;
    height: 600px;
    overflow: hidden;
    position: relative;
    align-items: center;
    background-image: url(../images/sh-banner-en.webp?v=1.0.3);
    background-size: cover;
    background-position: center;
}

.sh-banner-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* .sc-home{
  position: absolute;
  top: 50%;
  width: 50%;
  right: 100px;
  transform: translateY(-50%);
} */
.sc-home {
    position: relative;
    z-index: 99;
}

.back-line {
    font-size: 42px;
    color: #000000;
    font-weight: 500;
    position: relative;
    margin-left: 0;
    max-width: max-content;
    margin-bottom: 0;
    margin-right: auto;
}

.back-line::before {
    content: "";
    position: absolute;
    bottom: 10px;
    right: 0;
    background-color: #fff;
    width: 100%;
    height: 10px;
    z-index: -1;
}

.sc-home p {
    font-size: 25px;
    color: #000000;
    margin-bottom: 30px;
    margin-top: 20px;
}

.downApp p {
    font-size: 36.4px;
    text-align: left;
    color: #000000;
    font-weight: 500;
    margin-bottom: 35px;
}

.downApp-inner {
    position: relative;
    z-index: 99;
}

.downApp .back-line::before {
    bottom: 10px !important;
}

.fea-btn {
    text-align: center;
    padding-right: 20px;
}

.downApp {
    display: flex;
    align-items: center;
    justify-content: start;
}

.sh-dlogo {
    position: absolute;
    top: -135px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
}

.download-app-container .row {
    flex-direction: row-reverse;
}

/*become host*/
.hb-content {
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    height: 100%;
}

.hb-content h2 {
    margin: 19px 0 25px;
}

.hb-content p {
    font-size: 22px;
    text-align: left;
}

.hb-content button {
    width: 40%;
    height: 55px;
    font-size: 22px;
    margin-top: 15px;
    text-transform: capitalize;
}

.bh-map-img img {
    width: 100%;
}

.host-help .hosthelp-slide-main {
    text-align: center;
    width: 80%;
    margin: 0 auto;
}

.hosthelp-slide img {
    height: 580px;
    object-fit: cover;
    margin: 0 auto;
    width: 100%;
}

/*become host slider btn prev next*/
.swiper-button-hh-slide-next,
.swiper-button-hh-slide-prev {
    position: absolute;
    top: var(--swiper-navigation-top-offset, 50%);
    width: 40px;
    height: 40px;
    background: transparent;
    border-radius: 80px;
    box-shadow: 1px 3px 5px 1px #0000002b;
    padding: 5px;
    align-items: center;
    z-index: 10;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px) brightness(210%);
    transform: translateY(-50%);
}

.swiper-button-hh-slide-next.swiper-button-disabled,
.swiper-button-hh-slide-prev.swiper-button-disabled {
    opacity: 0;
    cursor: auto;
    pointer-events: none;
    transition: .3s ease-out;
}

.swiper-button-hh-slide-next.swiper-button-hidden,
.swiper-button-hh-slide-prev.swiper-button-hidden {
    opacity: 0;
    cursor: auto;
    pointer-events: none
}

.swiper-navigation-disabled .swiper-button-hh-slide-next,
.swiper-navigation-disabled .swiper-button-hh-slide-prev {
    display: none !important
}

.about-sec {
    position: relative;
}

.aboutsec-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    width: 100%;
}

.about-content-clr {
    color: #181818;
}

.story-content h1 {
    font-size: 75px;
    color: #fff;
}

.story-content h2 {
    font-size: 45px;
}

.believe-content p {
    font-size: 26px;
}

.about-bg-black {
    background-color: #181818;
    max-width: max-content;
    padding: 15px 20px;
    border-radius: 35px;
    margin: 0 auto;
    color: #fff;
}

.airplane-img {
    position: absolute;
    left: 0;
    width: 28%;
    bottom: -50px;
}

.found-content {
    top: 33%;
}

.found-content h3 {
    margin-top: 40px;
}

.about-sec-mid {
    background-color: #F4F4F4;
    padding: 80px 0px;
}

.as-mid-img {
    position: relative;
}

.as-main-img {
    position: relative;
    z-index: 9999;
    width: 85%;
    height: 350px;
    margin: 0 auto;
}

.as-mid-img .as-main-img-inner {
    width: 100%;
    height: 100%;
    border-radius: 15px;
    object-fit: cover;
}

.traingle-shape {
    position: absolute;
    top: -114px;
    left: 50%;
    transform: translateX(-50%);
}

.as-mid-img .as-pattern-img {
    position: absolute;
    z-index: 99;
    left: -27px;
    top: -22px;
}

.as-mid-img .as-triangle-img {
    position: absolute;
    bottom: 60px;
    right: 10px;
    z-index: 99999;
}

.as-mid-content h4 {
    line-height: 1.4;
    font-weight: 400;
    font-size: 28px;
}

.about-sm-two {
    padding: 80px 0px;
}

.about-smt-image {
    position: relative;
    text-align: center;
}

.baij-img {
    width: 300px;
}

.adven-mid-content h1 {
    font-weight: 700;
    margin-top: 30px;
}

.challeng-content h1 {
    font-size: 65px;
    max-width: max-content;
    position: relative;
    margin: 0 auto 30px;
    font-weight: 600;
}

.challeng-content h1::before {
    content: "";
    position: absolute;
    bottom: 19px;
    left: 0;
    width: 100%;
    height: 10px;
    background-color: var(--theme-primary);
    z-index: -1;
}

.about-sec-last {
    margin: -34px 0 100px;
}

.about-sl-content {
    top: 30%;
}

.about-sl-cw {
    background: #fff;
    border: 1px solid #000000;
    border-radius: 15px;
    padding: 60px 20px;
    position: relative;
}

.about-sl-cw h1 {
    font-weight: 600;
    margin-bottom: 15px;
}

.about-sl-cw h2 {
    margin-bottom: 35px;
}

.about-sl-cw p {
    font-size: 26px;
    margin-bottom: 0;
}

.white-shape-two {
    position: absolute;
    bottom: -13px;
    left: 50%;
    transform: translateX(-50%);
}

.about-sl-profile {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    margin-top: 50px;
}

.about-sl-profile img {
    width: 150px;
    object-fit: cover;
    border-radius: 50%;
    border: 2px solid #fff;
}

.about-sl-profile h3 {
    margin: 10px 0 0;
}

.about-sl-profile p {
    font-size: 24px;
}

.about-sl-sd {
    top: auto;
    bottom: -70px;
    background-color: #181818;
    padding: 8px 20px 8px 8px;
    display: flex;
    align-items: center;
    border-radius: 40px;
    max-width: max-content;
}

.about-sl-sd img {
    margin-right: 20px;
    width: 55px;
}

.about-sl-sd h1 {
    color: #fff;
    margin-bottom: 0;
    font-weight: 600;
}

/*about us page*/
.pay-sec {
    margin: 26px 20px 20px 0;
}

.pay-sec p {
    color: #000000;
}

.pay-card-sec {
    height: 100vh;
    display: flex;
    align-items: center;
}

.pc-sec-inner {
    width: 100%;
    margin: 0 auto;
    height: 250px;
    padding: 0 70px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
    border-radius: 15px;
}

.pc-sec-inner h2 {
    margin: 17px 0 15px;
}

.pc-sec-inner .logo-two {
    width: 150px;
}

.swiper-button-hh-slide-next svg,
.swiper-button-hh-slide-prev svg {
    width: 100%;
    height: 100%;
    object-fit: contain;
    transform-origin: center
}

.swiper-button-hh-slide-prev {
    left: var(--swiper-navigation-sides-offset, 70px);
    right: auto
}

.swiper-button-hh-slide-next {
    right: var(--swiper-navigation-sides-offset, 70px);
    left: auto
}

.swiper-button-lock {
    display: none
}

.swiper-button-hh-slide-next:after,
.swiper-button-hh-slide-prev:after {
    font-family: swiper-icons;
    text-transform: none !important;
    letter-spacing: 0;
    font-variant: initial;
    line-height: 1;
    font-size: 18px;
    color: #fff;
}

.swiper-button-hh-slide-prev:after {
    content: 'prev'
}

.swiper-button-hh-slide-next:after {
    content: 'next'
}

.bh-second-home {
    height: calc(100% - 53px);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    text-align: center;
    padding: 0px 30px;
}

.bh-second-home .bh-sh-head {
    font-size: 65px;
    color: #fff;
    font-weight: 600;
    margin-bottom: 25px;
}

.bh-second-home .bh-sh-content {
    font-weight: 400;
    line-height: 1.5;
}

.bh-sh-btn button {
    border: 1px solid #fff;
    width: 230px;
    color: #f5c33e;
    padding: 0 30px;
    background: #fff;
    border-radius: 8px;
    font-size: 30px;
    height: 70px;
    font-weight: 500;
}

.bh-sec-margin {
    margin-top: 100px;
}

/*become host page end*/
/*about us page*/
.about-sec {
    position: relative;
}

.aboutsec-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    width: 100%;
}

.about-content-clr {
    color: #181818;
}

.story-content h1 {
    font-size: 75px;
    color: #fff;
}

.story-content h2 {
    font-size: 45px;
}

.believe-content p {
    font-size: 26px;
}

.about-bg-black {
    background-color: #181818;
    max-width: max-content;
    padding: 15px 20px;
    border-radius: 35px;
    margin: 0 auto;
    color: #fff;
}

.airplane-img {
    position: absolute;
    left: 0;
    width: 28%;
    bottom: -50px;
}

.found-content {
    top: 33%;
}

.found-content h3 {
    margin-top: 40px;
}

.about-sec-mid {
    background-color: #f4f4f4;
    padding: 80px 0px;
}

.as-mid-img {
    position: relative;
}

.as-main-img {
    position: relative;
    z-index: 9999;
    width: 85%;
    height: 350px;
    margin: 0 auto;
}

.as-mid-img .as-main-img-inner {
    width: 100%;
    height: 100%;
    border-radius: 15px;
    object-fit: cover;
}

.traingle-shape {
    position: absolute;
    top: -114px;
    left: 50%;
    transform: translateX(-50%);
}

.as-mid-img .as-pattern-img {
    position: absolute;
    z-index: 99;
    left: -27px;
    top: -22px;
}

.as-mid-img .as-triangle-img {
    position: absolute;
    bottom: 60px;
    right: 10px;
    z-index: 99999;
}

.as-mid-content h4 {
    line-height: 1.4;
    font-weight: 400;
    font-size: 28px;
}

.about-sm-two {
    padding: 80px 0px;
}

.about-smt-image {
    position: relative;
    text-align: center;
}

.baij-img {
    width: 300px;
}

.adven-mid-content h1 {
    font-weight: 700;
    margin-top: 30px;
}

.challeng-content h1 {
    font-size: 65px;
    max-width: max-content;
    position: relative;
    margin: 0 auto 30px;
    font-weight: 600;
}

.challeng-content h1::before {
    content: "";
    position: absolute;
    bottom: 19px;
    left: 0;
    width: 100%;
    height: 10px;
    background-color: var(--theme-primary);
    z-index: -1;
}

.about-sec-last {
    margin: -34px 0 100px;
}

.about-sl-content {
    top: 30%;
}

.about-sl-cw {
    background: #fff;
    border: 1px solid #000000;
    border-radius: 15px;
    padding: 60px 20px;
    position: relative;
}

.about-sl-cw h1 {
    font-weight: 600;
    margin-bottom: 15px;
}

.about-sl-cw h2 {
    margin-bottom: 35px;
}

.about-sl-cw p {
    font-size: 26px;
    margin-bottom: 0;
}

.white-shape-two {
    position: absolute;
    bottom: -13px;
    left: 50%;
    transform: translateX(-50%);
}

.about-sl-profile {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    margin-top: 50px;
}

.about-sl-profile img {
    width: 160px;
    object-fit: cover;
    border-radius: 50%;
}

.about-sl-profile h3 {
    margin: 10px 0 0;
}

.about-sl-profile p {
    font-size: 24px;
}

.about-sl-sd {
    top: auto;
    bottom: -70px;
    background-color: #181818;
    padding: 8px 20px 8px 8px;
    display: flex;
    align-items: center;
    border-radius: 40px;
    max-width: max-content;
}

.about-sl-sd img {
    margin-right: 20px;
    width: 55px;
}

.about-sl-sd h1 {
    color: #fff;
    margin-bottom: 0;
    font-weight: 600;
}

/*about us page*/
/*contact host page*/
.contact-host-sec {
    padding: 50px 0;
}

.contact-hs-inner {
    padding: 0 30px 0 0;
}

.ch-host-profile {
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 1px solid var(--grey-one);
    margin-bottom: 20px;
    padding-bottom: 15px;
}

.ch-sec-pcontent {
    flex: 1;
}

.ch-sec-pcontent h3 {
    color: #181818;
}

.ch-sec-pcontent p {
    font-size: 18px;
}

.ch-sec-profile img {
    width: 60px;
}

.ch-host-message {
    margin: 20px 0;
}

.ch-host-message h3 {
    color: #181818;
}

.ch-host-message textarea {
    resize: none;
    height: 200px !important;
    margin: 30px 0 40px !important;
}

.ch-host-message textarea::placeholder {
    font-size: 18px;
}

.ch-host-btns {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.ch-hb-btn {
    display: flex;
    align-items: center;
}

.ch-hb-btn img {
    width: 10px;
    margin: 0 8px 2px 0;
}

.ch-host-btns button,
.ch-host-btns a {
    padding: 0 20px;
    height: 52px;
    font-size: 20px;
}

.ch-host-btns a:hover {
    color: #000000;
}


.pay-sec .pay-sec-label input[type="radio"] {
    height: 20px;
    width: 20px;
    margin-right: 10px;
    filter: grayscale(100%) !important;

}

.pay-sec .pay-sec-label {
    cursor: pointer;
    width: 100%;
    border: 1px solid #d1d1d1;
    border-radius: 6px;
    display: flex;
    align-items: center;
    padding: 10px;
}

/* .pay-sec .pay-sec-label .pay-sl-content {
  display: flex;
  padding: 6px 5px;
  border: 2px solid #e1e2e7;
  border-radius: 8px;
  position: relative;
  align-items: center;
  justify-content: center;
  transition: .3s ease-in-out;
} */
.pay-sec .pay-sec-label .pay-sl-content img {
    /* margin-right: 5px; */
    height: 20px;
    object-fit: cover;
    width: auto;
    transition: .3s ease-in-out;
}

.pay-sec .pay-sec-label .pay-sl-cdetail span {
    display: block;
    font-size: 15px;
    color: var(--them-secondary);
    font-weight: 500;
    transition: .3s ease-in-out;
}

/* .pay-sec .pay-sec-label .pay-sl-content:hover {
  border: 2px solid #181818;
}
.pay-sec .pay-sec-label .pay-sl-content:hover img{
  transform: scale(1.1);
} */
.pay-sec .pay-sec-label .pay-sl-content:hover .pay-sl-cdetail span {
    color: #181818;
}

.pay-sec .pay-sec-label input[type="radio"]:checked+.pay-sec-label {
    border: 2px solid #181818;
    background: #ededed63;
    -webkit-transition: ease-in 0.3s;
    -o-transition: ease-in 0.3s;
    transition: ease-in 0.3s;
}

/* .pay-sec .pay-sec-label input[type="radio"]:checked + .pay-sl-content img{
  filter: brightness(0);
  transform: scale(1.1);
} */
/* .pay-sec .pay-sec-label input[type="radio"]:checked + .pay-sl-content .pay-sl-cdetail span{
  color: #181818;
} */
.pay-sec.reserv-pay-sec {
    margin: 0 0 15px;
}

/* .pay-sec.reserv-pay-sec .pay-sec-label {
    width: 100%;
} */

.mt-ask-about h3 {
    color: #181818;
}

.mt-ask-content {
    margin-top: 25px;
}

.mt-ask-content h5 {
    font-size: 18px;
    color: #181818;
}

.mt-ask-content p {
    position: relative;
    padding-left: 15px;
    margin: 0 0 0 20px;
}

.mt-ask-content p::before {
    content: "";
    position: absolute;
    left: 0;
    width: 5px;
    height: 5px;
    background-color: var(--them-secondary);
    border-radius: 50%;
    top: 50%;
    transform: translateY(-50%);
}

.hst-msg-head {
    padding: 10px 0;
    box-shadow: 6px 4px 16px #0000000D;
}

.hst-msg-prop {
    display: flex;
    align-items: center;
    padding: 5px 0px 0;
}

.hm-prop-content {
    flex: 1;
}

.hm-prop-content h3 span {
    font-weight: 400;
    font-size: 19px;
}

.hm-prop-content p {
    color: #000000;
}

.hm-prop-img img {
    width: 100px;
    border-radius: 5px;
    object-fit: cover;
}

.hm-prop-content ul {
    display: flex;
    align-items: center;
    margin: 0;
}

.hm-prop-content ul li {
    position: relative;
    padding-right: 10px;
    font-size: 14px;
    display: flex;
    align-items: center;
}

.hm-prop-content ul li img {
    margin: 0 2px 2px 0;
    width: 11px;
    object-fit: cover;
}

.hm-prop-content ul li span {
    font-size: 14px !important;
}

.hm-prop-content ul li:nth-child(2) span {
    margin-right: 0px;
    display: inline-block;
}

.hm-prop-content ul li:nth-child(2)::before,
.hm-prop-content ul li:nth-child(2)::after {
    content: '';
    position: absolute;
    top: 51%;
    transform: translateY(-50%);
    width: 2px;
    height: 2px;
    background-color: #000;
    border-radius: 50%;
}

.hm-prop-content ul li:nth-child(2)::before {
    left: -6px;
}

.hm-prop-content ul li:nth-child(2)::after {
    right: 4px;
}

.conthst-property-pricing {
    padding: 30px 20px !important;
}

/*host agreement*/
.rl-fields {
    margin: 25px 0 25px;
}

.rl-fields form {
    height: 100%;
}

.rl-fields form input {
    height: 40px;
}

.listing-hstAgree {
    justify-content: center;
    /* margin: 25px 0 25px; */
    /* max-width: max-content; */
    /* border: 1px solid #dbdbdb; */
    /* border-radius: 8px; */
    /* padding: 20px; */
}

.listing-hstAgree p {
    /* font-weight: 500; */
    font-size: 15px;
}

/*host agreement end*/
/*contact host pagr end*/

/* host mode start */
/*header host mode*/
.host-header {
    box-shadow: rgba(0, 0, 0, 0.05) 0px 25px 15px -22px
}

.host-header .header-inner {
    padding: 15px 0;
}

.host-navbar {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0;
}

.host-navbar li {
    /* text-transform: capitalize; */
    margin-right: 40px;
}

.host-navbar li:last-child {
    margin-right: 0;
}

.host-navbar li a {
    color: #5F5F5F;
    font-weight: 500;
    position: relative;
    font-size: 20px;
    transition: transform 0.3s ease-in;
    display: flex;
    align-items: center;
}

.host-navbar li a i {
    margin-left: 5px;
}

.host-navbar li a:hover {
    color: #181818;
}

.host-navbar li a::after {
    content: '';
    position: absolute;
    width: 100%;
    transform: scaleX(0);
    height: 2px;
    bottom: -2px;
    left: 0;
    background: #000000;
    transform-origin: bottom right;
    transition: transform 0.3s ease-in;
}

.host-navbar li a:hover::after {
    transform: scaleX(1);
    transform-origin: bottom left;
}

.hs-incoming-mail {
    position: relative;
}

.hs-incoming-mail .hs-incoming-mail-notify {
    position: absolute;
    top: -10px;
    right: -20px;
    background-color: #FF0000;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
}

.host-user-main {
    display: flex;
    align-items: center;
    justify-content: end;
}

.host-user-main button:first-child {
    margin-right: 15px;
}

.host-user-main button img {
    width: 40px;
}

.host-navbar li .host-active {
    color: #181818;
    font-weight: 600;
}

.host-navbar li .host-active::after {
    transform: scaleX(1);
    transform-origin: bottom left;
}

.host-user-notification {
    position: relative;
}

.host-user-notification .host-user-notify {
    position: absolute;
    top: -2px;
    right: -4px;
    background-color: #FF0000;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 11px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
}

/*header host mode end*/
/*new reviews start*/
#review-step-stepper {
    width: 100%;
    position: relative;
}

#review-step-stepper:before {
    content: "";
    position: fixed;
    left: 0;
    bottom: 79px;
    height: 3px;
    width: 100%;
    background-color: #d8d8d8;
    transition: transform 0.5s ease;
}

#review-step-stepper:after {
    content: "";
    position: fixed;
    left: 0;
    bottom: 79px;
    height: 3px;
    width: calc(100% / 6);
    background-color: #333333;
    transform: scaleX(1);
    transform-origin: left center;
    transition: transform 0.5s ease, -webkit-transform 0.5s ease;
}

#review-step-stepper.step-2:after {
    transform: scaleX(2);
    -webkit-transform: scaleX(2);
}

#review-step-stepper.step-3:after {
    transform: scaleX(3);
    -webkit-transform: scaleX(3);
}

#review-step-stepper.step-4:after {
    transform: scaleX(4);
    -webkit-transform: scaleX(4);
}

#review-step-stepper.step-5:after {
    transform: scaleX(5);
    -webkit-transform: scaleX(5);
}

#review-step-stepper.step-6:after {
    transform: scaleX(6);
    -webkit-transform: scaleX(6);
}






/* custom for co host */
.co-hs-stp.step-2:after {
    transform: scaleX(4) !important;
    -webkit-transform: scaleX(4) !important;
}

.co-hs-stp.step-3:after {
    transform: scaleX(6) !important;
    -webkit-transform: scaleX(6) !important;
}

/* .co-hs-stp.step-4:after {
    transform: scaleX(6) !important;
    -webkit-transform: scaleX(6) !important;
} */

/* .co-hs-stp.step-5:after {
    transform: scaleX(6) !important;
    -webkit-transform: scaleX(6) !important;
}

.co-hs-stp.step-6:after {
    transform: scaleX(6) !important;
    -webkit-transform: scaleX(6) !important;
} */







.review-step-footer {
    width: 100%;
    position: fixed;
    bottom: 0;
    background: #FAFAFA;
    padding: 10px;
}

.review-step-footer-inner {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.review-step-footer .rs-theme-btn {
    display: inline-block;
    background-color: var(--theme-primary);
    color: #fff;
    padding: 14px 45px;
    border-radius: 8px;
    font-weight: 700;
    border: 2px solid var(--theme-primary);
    transition: 0.3s ease;
    cursor: pointer;
    text-align: center;
    font-size: 18px;
    letter-spacing: .5px;
}

.button-container .rs-theme-btn:hover {
    background-color: transparent;
    color: var(--theme-primary);
    -webkit-transform: scale(1.02);
    transform: scale(1.02);
}

.review-steps {
    display: none;
    animation: fadeIn 0.5s ease forwards;
    transition: .3s ease-in-out;
    padding: 80px 0;
}

/*new start review step 1*/
.review-step-start {
    display: flex;
    flex-direction: column;
    align-items: start;
    height: 100%;
    justify-content: start;
    margin: 30px 150px;
}

.review-step-center {
    justify-content: center;
}

.rs-fs-title {
    font-size: 39px;
    color: #181818;
    font-weight: 600;
}

.rs-fs-title-child {
    font-size: 32px;
    color: #181818;
    font-weight: 600;
    margin-bottom: 15px;
}

.rs-fs-content {
    font-size: 24px;
    color: #A3A3A3;
    font-weight: 500;
}

.rs-fs-content-child {
    font-size: 25px;
    color: #181818;
    margin-bottom: 10px;
    line-height: 1.2;
}

.rs-fs-cc-another {
    color: #A3A3A3;
    font-size: 20px;
}

/*section*/
.rs-start-sec .rs-start-sec-inner img {
    width: 120px;
    height: 120px;
    margin-bottom: 50px;
    box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
}

.rs-start-sec .rs-start-sec-inner h1 {
    margin-bottom: 25px;
}

.rs-start-sec .rs-start-sec-inner p {
    line-height: 1.3;
    margin-bottom: 30px;
    letter-spacing: .5px;
}

#start-review-loader {
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background: #fff;
    z-index: 99999;
}

#start-review-loader img {
    position: absolute;
    top: 50%;
    transform: translate(-50%, -50%);
    left: 50%;
}

.rs-rating-star .star-rating label {
    font-size: 2.5rem;
    padding-right: 15px;
}

.rs-second-sec .rs-star-rating img {
    width: 70px;
    margin-bottom: 25px;
}

.rs-second-sec .rs-star-rating h1 {
    margin-bottom: 15px;
}

.rs-second-sec .rs-star-rating .star-rating {
    margin-bottom: 10px;
}

.sr-checks-input .listing-checkbox {
    margin: 0;
    width: auto;
}

.sr-checks-input .listing-checkbox-wrapper {
    width: max-content;
    height: max-content;
    margin: 0 8px 12px 0;
}

.sr-checks-input .listing-checkbox-wrapper .listing-checkbox-tile {
    padding: 8px 20px;
    border-radius: 25px;
    border: 1px solid #707070;

}

.sr-checks-input .listing-checkbox-wrapper .listing-checkbox-tile .listing-checkbox-label {
    height: auto;
    margin: 0;
}

.sr-checks-input .listing-checkbox-wrapper .listing-checkbox-tile .listing-checkbox-content {
    color: #8A8A8A;
    font-weight: 500;
    font-size: 17px;
}

.sr-checks-input .listing-checkbox-input:checked+.listing-checkbox-tile .listing-checkbox-content {
    color: #181818;
}

.sr-checks-input .listing-checkbox-wrapper .listing-checkbox-input:checked+.listing-checkbox-tile {
    border-color: #181818;
    background-color: #FAFAFA;
    box-shadow: none;
}

.rs-clean-field {
    margin-top: 8px;
    visibility: hidden;
    opacity: 0;
}

.rs-clean-field input,
.rs-ra-textarea textarea {
    height: 60px;
    border-radius: 13px;
    border: 1px solid #707070;
}

.rs-clean-field input::placeholder {
    color: #8A8A8A;
    font-weight: 600;
    font-size: 18px;
}

.rs-ra-textarea textarea::placeholder {
    color: #D1D1D1;
    font-size: 18px;
}

.rs-clean-field .cc-display,
.rs-ra-textarea .cc-display {
    color: #181818;
    font-size: 16px;
}

.rs-review-add {
    width: 100%;
}

.rs-ra-textarea {
    width: 70%;
}

.rs-ra-textarea textarea {
    resize: none;
    height: 200px !important;
}

.rs-review-add img {
    width: 60px;
    margin-bottom: 25px;
}

.rs-or-inner.sr-checks-input .listing-checkbox-wrapper .listing-checkbox-tile {
    padding: 8px 40px;
}

.rs-overall-review img {
    width: 60px;
    margin-bottom: 25px;
}

.rs-review-add .rs-fs-content-child {
    margin-bottom: 25px;
}

.rs-or-inner {
    margin-top: 15px;
}

@keyframes fadeIn {
    0% {
        opacity: 0;
    }

    100% {
        opacity: 1;
    }
}

/*new reviews start end*/
/*dashboard start*/
.host-main-mg-tb {
    margin: 55px 0;
}

.dr-main-title {
    color: var(--dr-shade-gray);
    margin-bottom: 50px;
}

.dr-md-title {
    color: var(--dr-shade-gray);
}

.dr-sh-right {
    display: flex;
    align-items: center;
    justify-content: end;
}

.dr-sh-right-cont {
    margin-right: 20px;
}

.dr-sh-right-cont:hover {
    color: var(--dr-shade-gray);
}

.dr-md-title {
    font-size: 33px;
}

.dr-sh-right-cont span {
    font-weight: 500;
    color: var(--dr-shade-gray);
    font-size: 28px;
}

.dr-cust-arrow-btn button {
    border: 1px solid #707070;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.dr-cust-prev {
    margin-right: 10px;
}

.dr-cust-arrow-btn button i {
    color: var(--dr-shade-gray);
    font-size: 24px;
}

.day-reservation-tabbing .dr-tabs {
    border-bottom: 0;
    margin: 25px 0;
}

.day-reservation-tabbing .dr-tabs .nav-link {
    border: 1px solid var(--dim-gray);
    border-radius: 25px;
    padding: 0 20px;
    height: 39px;
    color: var(--dr-shade-gray);
    font-weight: 500;
    font-size: 16px;
    margin-right: 15px;
}

.day-reservation-tabbing .dr-tabs .nav-link:last-child {
    margin-right: 0;
}

.day-reservation-tabbing .dr-tabs .nav-link.active {
    background-color: #FAFAFA;
    border: 2px solid var(--dr-shade-gray);
}

.day-reservation-tabbing .tab-pane {
    margin-bottom: 0;
}

.day-reserv-box {
    border: 1px solid #D1D1D1;
    padding: 10px 0 0;
    border-radius: 10px;
}

.ht-btn-main button {
    margin: 0 0 0 20px;
}

.ht-btn-main button:last-child {
    margin-left: 0;
}

.dr-box-head {
    padding: 0 10px;
    justify-content: space-between;
}

.dr-box-head h6 {
    color: #FF1111;
    font-size: 18px;
}

.dr-box-head img {
    width: 60px;
    margin: 0 0 10px 5px;
    height: 60px;
}

.dr-box-content {
    padding: 0 10px;
}

.dr-box-content ul {
    margin: 0;
}

.dr-box-content ul li span {
    color: var(--dr-shade-gray);
    font-size: 18px;
    font-weight: 500;
    display: inline-block;
}

.dr-box-content ul li:last-child span {
    color: var(--grey-three);
    margin-top: 10px;
    font-size: 17px;
}

.dr-box-foot {
    border-top: 1px solid #D1D1D1;
    padding: 10px;
    text-align: center;
}

.dr-box-foot a {
    display: block;
    color: var(--dr-shade-gray);
    font-size: 20px;
    font-weight: 500;
}

.dr-box-foot .content-skeleton {
    margin: 5px auto;
    padding: 12px 0;
}

.avatar-skeleton {
    width: 70px;
    height: 70px;
    background: linear-gradient(90deg, var(--loader-background-color) 25%, var(--loader-highlight-color) 50%, var(--loader-background-color) 75%);
    background-size: 200% 100%;
    animation: loading 2s infinite ease-in-out;
    border-radius: 50%;
    margin-bottom: 20px;
}

.content-skeleton {
    width: 150px;
    height: 15px;
    background: linear-gradient(90deg, var(--loader-background-color) 25%, var(--loader-highlight-color) 50%, var(--loader-background-color) 75%);
    background-size: 200% 100%;
    animation: loading 2s infinite ease-in-out;
    margin-bottom: 10px;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }

    100% {
        background-position: -200% 0;
    }
}

.reservation-nodata {
    background-color: rgb(247, 247, 247);
    min-height: 220px;
    max-height: max-content;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    border-radius: 10px;
    margin-bottom: 30px;
}

.reserv-nd-inner {
    width: 200px;
    text-align: center;
}

.reservation-nodata img {
    width: 50px;
    margin-bottom: 20px;
}

.reservation-nodata P {
    color: var(--dr-shade-gray);
    line-height: 1.3;
    font-size: 18px;
    margin-bottom: 0;
}

.reminder-cards-sec {
    background-color: #F9F7F4;
    padding: 20px 0;
}

.reminder-card {
    background-color: #fff;
    border-radius: 10px;
    padding: 20px 25px;
    position: relative;
    height: 100%;
}

.rc-cancel img {
    width: 11px;
}

.rc-profile {
    width: 30px;
}

.rc-card-content {
    margin: 8px 0 50px 0;
}

.rc-card-content h6 {
    color: var(--dr-shade-gray);
    font-size: 20px;
    margin: 15px 0;
}

.rc-card-content p {
    color: var(--dr-shade-gray);
    font-weight: 400;
    font-size: 16px;
}

.rc-cc-unique {
    color: #A3A3A3;
    font-size: 16px;
    display: inline-block;
    font-weight: 500;
    margin-bottom: 10px;
}

.rc-card-foot {
    position: absolute;
    bottom: 20px;
}

.rc-card-button {
    border: 1px solid var(--dim-gray) !important;
    color: var(--dim-gra);
    transition: .3s ease-in-out;
    padding: 0 18px;
    height: 40px;
    font-size: 18px;
    border-radius: 7px;
    font-weight: 500;
    margin-right: 10px;
}

.rc-card-button:hover {
    background-color: #000000;
    color: #fff;
}

.grill-icon {
    width: 22px;
}

.all-reservation-sec {
    margin-top: 130px;
}

.all-reserv-head {
    margin: 20px 0 60px;
}

.pm-back-btn {
    width: 14px;
}

.all-reservation-sec .nav-tabs {
    margin-top: 25px;
}

/**** vue table design ****/
.ht-dot-btn {
    border-radius: 50%;
    transition: .3s ease-in-out;
    height: 28px;
    width: 28px;
}

.ht-dot-btn img {
    width: 17px;
}

.ht-dot-btn:hover {
    background-color: #FAFAFA;
}

.host-table table.dataTable tbody td,
.host-table table.dataTable thead th {
    border-bottom: 1px solid #D1D1D1 !important;
    padding-top: 20px;
    padding-bottom: 20px;
}

.host-table table.dataTable thead th {
    color: var(--grey-three);
    font-size: 18px;
    font-weight: 400 !important;
}

.host-table table.dataTable tbody td {
    vertical-align: baseline;
    font-size: 15px;
}

.host-table table.dataTable tbody td p {
    color: var(--dr-shade-gray);
}

.host-table table.dataTable.no-footer {
    margin-bottom: 60px;
}

.allreserv-nodate {
    text-align: center;
    padding: 100px 0;
}

.allreserv-nodate h5 {
    margin-bottom: 0;
    color: var(--dr-shade-gray);
}

.allreserv-nodate .p-button {
    color: var(--dr-shade-gray) !important;
    font-weight: 500;
    font-size: 20px;
}

.all-reservation-sec .tab-pane {
    margin-bottom: 0;
}

.bg-black {
    background: #000000;
    height: 200px;
    flex: 0 0 29%;
    margin-left: 10px;
}

.host-table-btn {
    justify-content: end;
}

.hst-fil-group {
    margin-right: 10px;
}

.host-table-btn .hst-bd-btn {
    padding: 0 15px;
    font-size: 18px;
    font-weight: 500;
}

.host-table-btn .hst-bd-btn span {
    text-transform: capitalize;
    letter-spacing: .5px;
    color: var(--dr-shade-gray);
    transition: .3s ease-in-out;
}

.host-table-btn .hst-bd-btn .p-button-icon {
    font-size: 21px;
}

.host-table-btn .hst-export-btn .p-button-icon {
    font-size: 16px;
}

.hs-fil-drd-pd {
    padding: 0 15px;
}

.hs-filter-drd-dtrange {
    display: flex;
    align-items: center;
    border-bottom: 1px solid var(--grey-one);
    padding-bottom: 15px;
}

.hs-filter-drd-dtrange label {
    color: var(--dr-shade-gray);
}

.hs-filter-drd-dtrange .form-control {
    border-color: var(--dim-gray);
}

.hs-filter-drd-dtrange .form-control::placeholder {
    color: #000000;
    font-size: 15px;
}

.hs-filter-drd-dtrange .form-dtrange {
    border-radius: 25px 0 0 25px;
    border-right: none;
}

.hs-filter-drd-dtrange .to-dtrange {
    border-radius: 0 25px 25px 0;
}

.hs-filter-drd-btn {
    background-color: var(--grey-three);
    color: #fff;
    font-size: 18px;
    font-weight: 500;
    width: 100%;
    border: none;
    height: 45px;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 90%;
    margin: 0 auto !important;
}

.hs-fil-head {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    font-size: 13px;
    border-bottom: 1px solid var(--grey-one);
    padding: 15px;
}

.hs-fil-head .host-filter-close {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: transparent;
    color: black;
    border: none;
    font-size: 18px;
    justify-content: start;
}

.hs-fil-head .host-filter-close:hover {
    color: #000000 !important;
}

.hs-fil-head h5 {
    color: #000000;
}

.hs-fil-body {
    padding: 20px 15px;
}

.hs-fil-body .hs-fil-input-left {
    /* border-right: none; */
    border-radius: 8px 0 0 8px;
}

.hs-fil-body .hs-fil-input-right {
    border-radius: 0 8px 8px 0;
}

.hs-fil-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-top: 1px solid var(--grey-one);
    padding: 10px 15px;
}

.p-calendar {
    transition: .3s ease-in-out;
}

.hs-filter-dropdown form {
    padding-top: 10px;
    padding-bottom: 10px;
}

/* host detail modal */
.mdl-hostreserv-width {
    max-width: 600px;
}

.hs-custdetail-title-mg {
    margin-bottom: 25px;
}

.hs-custdetail-fwc {
    font-weight: 500;
    font-size: 17px;
}

.hs-cust-paddingtb-border {
    padding: 20px 0;
    border-bottom: 10px solid #FAFAFA;
}

.hs-cust-paddinglr {
    padding-left: 20px;
    padding-right: 20px;
}

.host-bg-trans-btn {
    background-color: transparent;
    border: 1px solid var(--dim-gray);
    color: var(--dr-shade-gray);
    transition: .3s ease-in-out;
    border-radius: 5px;
}

.host-bg-trans-btn:hover {
    background-color: var(--dr-shade-gray);
    color: #fff !important;
}

.hs-detail-modal-main .cm-bd-header {
    height: auto;
    padding: 0 20px;
}

.hs-cd-head {
    margin: 60px 0 20px;
    padding: 0 15px;
    justify-content: space-between;
}

.hs-cd-head h3 {
    color: var(--dr-shade-gray);
}

.hs-cd-head button {
    border: 1px solid var(--dim-gray);
    color: var(--dr-shade-gray);
    height: 40px;
    padding: 0 15px;
    border-radius: 5px;
    text-transform: capitalize;
    font-weight: 500;
    font-size: 17px;
    transition: .3s ease-in-out;
}

.hs-cd-head button:hover {
    background-color: var(--dr-shade-gray);
    color: #fff;
}

.host-custdetail-main {
    align-items: start;
}

.hs-cd-content ul li {
    margin-bottom: 8px;
}

.hs-cd-content ul li:last-child {
    margin-bottom: 0;
}

.hs-cd-content ul li h5 {
    color: var(--dr-shade-gray);
    line-height: 1.3;
    margin-bottom: 0;
}

.hs-cd-content ul li p {
    color: var(--grey-three);
    line-height: 1.3;
    margin-bottom: 0;
}

.hs-cd-image img {
    width: 100px;
    box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
    margin-left: 5px;
    height: 100px;
}

.host-custabout-content li {
    margin-bottom: 15px;
}

.host-custabout-content li:last-child {
    margin-bottom: 0;
}

.host-custabout-content img {
    margin-right: 20px;
    width: 23px;
}

.host-custabout-content p {
    color: var(--dr-shade-gray);
    margin-bottom: 0;
}

.hs-custview-btn {
    color: var(--dr-shade-gray);
    text-decoration: underline;
    margin: 30px 0;
    display: block;
}

.hover-transparent:hover {
    color: var(--dr-shade-gray);
}

.hs-custbtn-sec button {
    height: 55px;
    margin-bottom: 17px;
    font-weight: 500;
    font-size: 18px;
}

.hs-custbtn-sec a {
    height: 55px;
    margin-bottom: 17px;
    font-weight: 500;
    font-size: 18px;
    justify-content: center;
}

.hs-cust-pn span {
    font-size: 13px;
    color: var(--grey-three);
    font-weight: 500;
}

.host-cr-detail-content {
    margin: 0;
}

.host-cr-detail-content li {
    border-bottom: 1px solid #D1D1D1;
    padding-top: 20px;
    padding-bottom: 20px;
}

.host-cr-detail-content li:last-child {
    border-bottom: unset;
}

.host-cr-detail-content li h6 {
    line-height: 1.3;
}

.hs-guest-offer {
    color: var(--dr-shade-gray);
    text-decoration: underline;
}

.hs-guest-offer:hover {
    color: var(--dr-shade-gray);
}

.host-cr-detail-content li p {
    color: var(--grey-three);
    margin-bottom: 0;
    line-height: 1.3;
    font-weight: 400;
}

.host-custamount-main ul li {
    margin-bottom: 22px;
}

.host-custamount-main ul li:last-child {
    margin-bottom: 0;
}

.host-custamount-main ul li p {
    color: var(--dr-shade-gray);
    line-height: 1.3;
    margin-bottom: 0;
    font-size: 16px;
}

.host-custamount-main ul li:last-child p {
    font-weight: 600;
    font-size: 17px;
}

.hs-custarrow-div .hs-ca-div-content {
    border-top: 1px solid #D1D1D1;
    padding-top: 20px;
    padding-bottom: 20px;
}

.hs-custarrow-div .hs-ca-div-content p {
    margin-bottom: 0;
    color: var(--dr-shade-gray);
    font-size: 20px;
}

.hs-custarrow-div .hs-ca-div-content img {
    rotate: 180deg;
    width: 11px;
}

.host-custform-main {
    padding-top: 20px;
    padding-bottom: 20px;
}

.host-custform-main p {
    margin: 25px 0 25px 25px;
    color: var(--dr-shade-gray);
}

.host-custform-main form textarea {
    border-color: var(--dim-gray);
    resize: none;
    height: 200px;
    margin-bottom: 20px;
}

.host-custform-main form textarea::placeholder {
    font-size: 18px;
}

.host-custform-main form button {
    height: 55px;
}

/**** host footer ****/
.host-footer .social-icon {
    justify-content: space-evenly;
}

.host-footer .store-link img {
    width: 70%;
}

.host-footer .store-link {
    text-align: center;
}

.all-reser-head .host-table-btn button {
    margin-right: 0 !important;
}

/**** inbox ****/
.chat-reservation-detail {
    height: calc(100vh - 107px);
    overflow: auto;
    padding: 0 15px 0 0;
}

.chat-reservation-detail .hs-cd-head {
    border-bottom: 1px solid #D1D1D1;
    padding-bottom: 20px;
    margin: 10px 0;
    align-items: center;
}

.hs-cd-cin p {
    margin-bottom: 6px !important;
}

.chat-reservation-detail .hs-cd-image img {
    width: 50px;
    height: 50px;
}

.chat-reservation-detail .host-custform-main p {
    margin: 15px 0 15px 0;
    font-size: 16px;
    font-weight: 400;
}

.chat-reservation-detail .hs-custdetail-fwc {
    font-size: 16px;
}

.chat-reservation-detail .host-custabout-content img {
    margin-right: 10px;
    width: 20px;
}

.chat-reservation-detail .hs-custbtn-sec button {
    font-size: 16px;
}

.chat-reservation-detail .hs-custarrow-div .hs-ca-div-content p {
    font-size: 17px;
}

.chat-reservation-detail .hs-custarrow-div .hs-ca-div-content img {
    width: 9px;
}

.chat-reservation-detail .host-custamount-main ul li p {
    font-weight: 400;
}

.chat-reservation-detail .host-custamount-main ul li:last-child p {
    font-weight: 600;
}

.chat-reservation-detail .hs-custview-btn {
    margin: 10px 0;
}

.chat-reservation-detail .host-cr-detail-content li:last-child {
    padding-bottom: 0;
}

.chat-reservation-detail .host-custamount-main ul li {
    margin-bottom: 15px;
}

.chat-reservation-detail .hs-custdetail-title-mg {
    margin-bottom: 20px;
}

.chat-reservation-detail .hs-custview-btn {
    margin: 15px 0;
}
.nInbox #no-detail-panel {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    gap: 10px;
}
.host-custform-main form textarea::placeholder {
    font-size: 14px;
}

.hosting-situation-msg {
    margin-right: 40px;
}

.host-filter-btn button img {
    width: 18px;
}

.host-export-btn button img {
    width: 14px;
}

.host-table-btn .ht-bd-btn {
    height: 45px;
    padding: 0 15px;
    font-size: 18px;
    font-weight: 500;
}

.host-table-btn button.ht-bd-btn:hover {
    background-color: transparent;
    color: var(--dr-shade-gray);
}

.host-table-btn button {
    margin-right: 20px;
}

.host-table-btn button img {
    margin-left: 5px;
}

.host-table-btn .dropdown-menu li .dropdown-item {
    border-bottom: 1px solid var(--grey-one);
    padding: 14px 15px;
}

.host-table-btn .dropdown-menu li:last-child .dropdown-item {
    border-bottom: none;
}

.host-table-btn .dropdown-menu {
    min-width: 20rem;
    top: 10px !important;
    box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
    border: none;
}

.exp-dropdown li a img {
    width: 21px;
}

.hs-fil-drd-pd {
    padding: 0 15px;
}

.hs-filter-drd-dtrange {
    display: flex;
    align-items: center;
    border-bottom: 1px solid var(--grey-one);
    padding-bottom: 15px;
}

.hs-filter-drd-dtrange label {
    color: var(--dr-shade-gray);
}

.hs-filter-drd-dtrange .form-control {
    border-color: var(--dim-gray);
}

.hs-filter-drd-dtrange .form-control::placeholder {
    color: #000000;
    font-size: 15px;
}

.hs-filter-drd-dtrange .form-dtrange {
    border-radius: 25px 0 0 25px;
    border-right: none;
}

.hs-filter-drd-dtrange .to-dtrange {
    border-radius: 0 25px 25px 0;
}

.hs-filter-drd-btn {
    background-color: var(--grey-three);
    color: #fff;
    font-size: 18px;
    font-weight: 500;
    width: 100%;
    border: none;
    height: 45px;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 90%;
    margin: 0 auto !important;
}

.hs-fil-drd-head {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    font-size: 13px;
    border-bottom: 1px solid var(--grey-one);
    padding-top: 10px;
    padding-bottom: 10px;
}

.hs-fil-drd-head .host-filter-close {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
}

.hs-fil-drd-head .host-filter-close img {
    width: 10px;
}

.hs-fil-drd-head h4 {
    color: #000000;
}

.hs-filter-dropdown form {
    padding-top: 10px;
    padding-bottom: 10px;
}

.host-footer .social-icon {
    justify-content: space-evenly;
}

.host-footer .store-link img {
    width: 70%;
}

.host-footer .store-link {
    text-align: center;
}

.hosting-situation-msg {
    margin-right: 40px;
}

.hosting-situation-msg span {
    font-size: 14px;
}

.host-chat-date {
    position: absolute;
    top: 0;
    right: 0;
}

.host-chat-date span {
    font-size: 14px;
    color: #7B8793;
}

.chat-name .ht-bd-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 45px;
    margin-top: 5px;
}

.chat-name .ht-bd-btn img {
    margin-right: 5px;
    width: 17px;
}

.chat-name .ht-bd-btn span {
    font-weight: 500;
    font-size: 14px;
}

.chat-name .ht-bd-btn:hover {
    background-color: #fff;
    color: var(--dr-shade-gray);
}

/**** receipt page ****/

.print-div a {
    border: 1px solid rgba(0, 0, 0, 0.175);
}

.print-div a:hover {
    color: #000000;
}

.receipt-logo {
    /* width: 150px; */
    margin: 20px 0 0;
}

.receipt-title {
    color: var(--dr-shade-gray);
    margin: 20px 0;
    font-weight: 500;
}

.pdf-row {
    margin-bottom: 20px;
    align-items: start;
}

.pdf-one-column {
    margin-right: 10px;
}

.pdf-ar-content {
    text-align: right;
}

.pdf-title {
    font-size: 15px;
    margin-bottom: 5px !important;
    line-height: 1.3;
    font-weight: 500;
}

.pdf-detail {
    font-size: 15px;
    font-weight: 500;
    margin-bottom: 0px !important;
    line-height: 1.5;
    font-weight: 400;
}

/**** iqama ****/
.pro-doc-verify.accordion-flush .accordion-item .accordion-button {
    border-top-left-radius: 15px;
    border-top-right-radius: 15px;
    padding: 10px 15px 10px 0;
    transition: .3s ease-in-out;
}

.pro-doc-verify .accordion-button:focus {
    border: none;
    box-shadow: none;
}

.pro-doc-verify .accordion-button:not(.collapsed) {
    background-color: var(--grey-two);
    padding: 10px 15px 10px 15px !important;
}

.pro-doc-verify .accordion-button:not(.collapsed)::after {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='var%28--bs-body-color%29'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}

.pro-doc-verify .datepicker tbody tr>td.day.selected,
.datepicker tbody tr>td.day.selected:hover,
.datepicker tbody tr>td.day.active,
.datepicker tbody tr>td.day.active:hover {
    background-color: var(--dr-shade-gray);
}

.pro-doc-verify .datepicker tbody tr>td.day.today {
    background: var(--dr-shade-gray) !important;
}

.receipt-title {
    color: var(--dr-shade-gray);
    margin: 20px 0;
    font-weight: 700;
}

.pdf-row {
    margin-bottom: 20px;
    align-items: center;
}

.pdf-one-column {
    margin-right: 10px;
}

.pdf-ar-content {
    text-align: right;
}

.pdf-title {
    font-size: 15px;
    margin-bottom: 5px;
    line-height: 1.3;
    font-weight: 700;
}

.pdf-detail {
    font-size: 15px;
    font-weight: 500;
    margin-bottom: 0px !important;
    line-height: 1.3;
    font-weight: 700;
}

/**** iqama ****/
.pro-doc-verify.accordion-flush .accordion-item .accordion-button {
    border-top-left-radius: 15px;
    border-top-right-radius: 15px;
    padding: 10px 15px 10px 0;
    transition: .3s ease-in-out;
}

.pro-doc-verify .accordion-button:focus {
    border: none;
    box-shadow: none;
}

.pro-doc-verify .accordion-button:not(.collapsed) {
    background-color: var(--grey-two);
    padding: 10px 15px 10px 15px !important;
}

.pro-doc-verify .accordion-button:not(.collapsed)::after {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='var%28--bs-body-color%29'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}

.pro-doc-verify .datepicker tbody tr>td.day.selected,
.datepicker tbody tr>td.day.selected:hover,
.datepicker tbody tr>td.day.active,
.datepicker tbody tr>td.day.active:hover {
    background-color: var(--them-secondary);
}

.pro-doc-verify .datepicker tbody tr>td.day.today {
    background: var(--theme-primary) !important;
}

/* new design invoice */
.in-box {
    border: 2px dashed #e2e2e2;
    padding: 15px 10px 5px;
    margin-bottom: 20px;
}

.in-box-ar {
    text-align: right;
    direction: rtl;
}

.in-inner {
    display: flex;
    align-items: center;
}

.br-cont span {
    color: gray;
    font-size: 11px;
}

.br-cont h5 {
    font-size: 15px;
    color: #f5c33e;
    margin: 0;
}

.br-cont {
    padding-left: 10px;
}

a.language_footer.ar {
    display: none;
}

.br-cont h5 {
    line-height: 10px;
    margin-bottom: 7px;
}

.step-one-row {
    flex-wrap: wrap-reverse;
}

.step-one-row .right-side {
    height: auto !important;
}

/* .step-one-row .left-side{
    height: auto  !important;
} */
.listing-footer {
    position: fixed;
    width: 100%;
    bottom: 0;
}

.listing-footer {
    height: 80px !important;
}




/* new design invoice */
.in-box {
    border: 2px dashed #e2e2e2;
    padding: 15px 10px 5px;
    margin-bottom: 20px;
}

.in-box-ar {
    text-align: right;
    direction: rtl;
}

.in-inner {
    display: flex;
    align-items: center;
}

.in-content-en h6 {
    padding-right: 5px;
    margin-bottom: 3px;
}

.in-content-ar h6 {
    padding-right: 5px;
    margin-bottom: 3px;
}

.in-content-ar {
    direction: rtl;
}

.in-box .receipt-title,
.in-box-ar .receipt-title {
    margin-top: 10px;
    text-align: center;
    font-weight: 500;
    margin-bottom: 20px;
}

.in-box-en .in-data {
    margin-left: 3px;
}

.in-box-en .in-data {
    margin-left: 3px;
}

p.in-data {
    margin-bottom: 3px;
    font-size: 14px;
}

.in-content-en,
.in-content-ar {
    margin-bottom: 15px;
}

.in-content-ar .in-inner p.in-data {
    padding-right: 7px;
}

.in-head {
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
}

.receipt-summary .in-box {
    border-bottom: none;
    margin-bottom: 0;
}

.sm-tax-invoice .in-box {
    border-bottom: 0;
    margin-bottom: 0;
}

a.pdf-print {
    height: 60px;
    width: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 25px;
    border-radius: 38px;
    color: gray;
    position: fixed;
    bottom: 60px;
    background: #fff;
    right: 11%;
    box-shadow: -1px 4px 9px 4px #0000001c;
    border: transparent;
}

/**** component loader ****/
.component-loader {
    width: 100%;
    height: calc(100vh - 380px);
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #ffffffc2;
}

.dot-pulse {
    position: relative;
    left: -9999px;
    width: 10px;
    height: 10px;
    border-radius: 5px;
    background-color: var(--dr-shade-gray);
    color: var(--dr-shade-gray);
    box-shadow: 9999px 0 0 -5px;
    animation: dot-pulse 1.5s infinite linear;
    animation-delay: 0.25s;
}

.dot-pulse::before,
.dot-pulse::after {
    content: "";
    display: inline-block;
    position: absolute;
    top: 0;
    width: 10px;
    height: 10px;
    border-radius: 5px;
    background-color: var(--dr-shade-gray);
    color: var(--dr-shade-gray);
}

.dot-pulse::before {
    box-shadow: 9984px 0 0 -5px;
    animation: dot-pulse-before 1.5s infinite linear;
    animation-delay: 0s;
}

.dot-pulse::after {
    box-shadow: 10014px 0 0 -5px;
    animation: dot-pulse-after 1.5s infinite linear;
    animation-delay: 0.5s;
}

@keyframes dot-pulse-before {
    0% {
        box-shadow: 9984px 0 0 -5px;
    }

    30% {
        box-shadow: 9984px 0 0 2px;
    }

    60%,
    100% {
        box-shadow: 9984px 0 0 -5px;
    }
}

@keyframes dot-pulse {
    0% {
        box-shadow: 9999px 0 0 -5px;
    }

    30% {
        box-shadow: 9999px 0 0 2px;
    }

    60%,
    100% {
        box-shadow: 9999px 0 0 -5px;
    }
}

@keyframes dot-pulse-after {
    0% {
        box-shadow: 10014px 0 0 -5px;
    }

    30% {
        box-shadow: 10014px 0 0 2px;
    }

    60%,
    100% {
        box-shadow: 10014px 0 0 -5px;
    }
}

/**** component loader end ****/

.overlay-group .p-overlaypanel {
    top: 45px !important;
    right: 0;
    left: auto !important;
    width: max-content;
}

.host-edit-btn {
    display: flex;
    align-items: baseline;
    height: max-content;
}

.cust-double-rangepicker.daterangepicker .drp-calendar {
    max-width: 300px !important;
    width: 300px;
}

.cust-double-rangepicker.daterangepicker {
    padding: 15px;
}

.cust-double-rangepicker.daterangepicker .drp-calendar.left {
    margin-right: 6px;
    padding: 0;
}

.cust-double-rangepicker.daterangepicker .drp-calendar.right {
    margin-left: 6px;
    padding: 0;
}

.cust-double-rangepicker.daterangepicker .calendar-table th.month {
    font-size: 16px;
}

.anchor-prnt-btn {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.custom-cursor {
    cursor: pointer;
}

/****** new design guest reservation ******/
.guest-reservation {
    margin: 20px 0 40px;
}

.gr-nav-pills.nav-pills {
    background: #EDEDED;
    border-radius: 5px;
    padding: 5px;
    margin: 0;
    height: 48px;
}

/* .gr-nav-pills.nav-pills .nav-item{
    width: 50%;
  } */
.gr-nav-pills.nav-pills .nav-link {
    height: 38px;
    padding: 0 20px;
    width: 100%;
    color: #9a9a9c;
    font-weight: 500;
    font-size: 18px;
}

.gr-nav-pills.nav-pills .nav-link.active,
.nav-pills .show>.nav-link {
    color: var(--dr-shade-gray);
    background-color: #fff;
    font-weight: 700;
}

.guest-reservation .gr-body .tab-content {
    overflow: inherit;
}

.gr-card {
    position: relative;
}

.gr-cardImage img {
    width: 65px;
    height: 65px;
    object-fit: cover;
    border-radius: 7px;
    transition: .3s ease-out;
}

.gr-cardBody {
    padding: 0 12px;
    position: relative;
}

.gr-cardBody p {
    color: var(--dr-shade-gray);
    line-height: 1.2;
}

.gr-cardBody .gr-cb-title {
    color: var(--dr-shade-gray);
    line-height: 1.2;
}

.gr-cardBody .gr-cb-title p {
    line-height: 1.2;
    font-size: 14px;
}

.gr-card-bd {
    border-bottom: 1px solid #ecebef;
}

.gr-card-ptb {
    padding: 10px 0;
}

.gr-cb-buttons a span {
    margin-left: 5px;
}

.gr-cb-buttons a i {
    font-size: 18px;
}

.gr-cb-buttons a:hover {
    background-color: #f2f2f2;
    color: var(--dr-shade-gray);
}

.gr-ci-infoBtn a,
.gr-ci-infoBtn .gr-ci-no-phone-num {
    padding: 12px;
    color: var(--dr-shade-gray);
}

.gr-ci-infoBtn a img,
.gr-ci-infoBtn .gr-ci-no-phone-num img {
    width: 35px;
    margin-right: 10px;
}

.gr-ci-infoBtn a i,
.gr-ci-infoBtn .gr-ci-no-phone-num i {
    font-size: 30px;
    margin-right: 5px;
}

.gr-ci-infoBtn a span,
.gr-ci-infoBtn .gr-ci-no-phone-num span {
    color: var(--dr-shade-gray);
    font-size: 16px;
    font-weight: 500;
}

.gr-card-infoMd .cm-bd-body {
    padding: 17px 0 10px;
}

.gr-card-infoMd .cm-bd-footer {
    border-top: none;
    padding: 0;
}

.gr-ci-infoFooter button {
    margin: 0 12px 10px;
}

.gr-card-main {
    padding: 5px;
    display: block;
    transition: .3s ease-out;
    border-radius: 7px;
    margin-bottom: 20px;
}

.gr-card-main:hover {
    background: #f2f2f2;
}

.gr-card-main:hover .gr-cardImage img {
    box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
}

@-webkit-keyframes stretch {
    0% {
        transform: scale(0.5);
        background-color: #f5c33e;
    }

    50% {
        background-color: #f5c33e;
    }

    100% {
        transform: scale(1);
        background-color: #f5c33e;
    }
}

@keyframes stretch {
    0% {
        transform: scale(0.5);
        background-color: #f5c33e;
    }

    50% {
        background-color: #f5c33e;
    }

    100% {
        transform: scale(1);
        background-color: #f5c33e;
    }
}

.load-more-btn {
    font-size: 17px;
    align-items: center;
    background: #f5c33e;
    border-radius: 7px;
    color: #ffffff;
    cursor: pointer;
    height: 45px;
    justify-content: center;
    border: 2px solid transparent;
    display: flex;
    width: 150px;
    margin: 25px auto 0;
}

.load-more-btn:hover {
    color: #f5c33e !important;
    background: transparent;
    border: 2px solid #f5c33e;
}

.load-more-btn.lm-btn-loading {
    font-size: 0;
    background: transparent;
    border: 2px solid #f5c33e;
    width: 150px;
    height: 45px;
}

.load-more-btn.lm-btn-loading .lm-btn-loading-dots {
    display: flex;
}

.load-more-btn.lm-btn-loading .lm-btn-loading-dots i {
    animation-direction: alternate;
    animation-duration: 0.5s;
    animation-fill-mode: none;
    animation-iteration-count: infinite;
    animation-name: stretch;
    animation-play-state: running;
    animation-timing-function: ease-out;
    border-radius: 100%;
    display: block;
    height: 10px;
    margin: 0 1px;
    width: 10px;
    animation-delay: 0.1s;
    margin: 0 5px;
}

.load-more-btn.lm-btn-loading .lm-btn-loading-dots i:first-child {
    animation-delay: 0s;
    margin: 0;
}

.load-more-btn.lm-btn-loading .lm-btn-loading-dots i:last-child {
    animation-delay: 0.2s;
    margin: 0;
}

.load-more-btn i {
    font-weight: normal;
}

.load-more-btn .lm-btn-loading-dots {
    display: none;
}

/****** guest reservation detail page******/
.gr-detail-main {
    overflow: hidden;
    height: calc(100vh - 78px);
}

.gr-detail-sec {
    padding: 10px;
}

.gr-detail-sec .gr-detail-sec-inner {
    height: calc(100vh - 98px);
    overflow-y: auto;
    box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
    border-radius: 5px;
}

.gr-detail-sec .gr-detail-sec-inner::-webkit-scrollbar {
    width: 5px;
}

.gr-ds-propImages .swiper-pagination-bullet {
    width: var(--swiper-pagination-bullet-width, var(--swiper-pagination-bullet-size, 12px));
    height: var(--swiper-pagination-bullet-height, var(--swiper-pagination-bullet-size, 12px));
    background: #a5a3a4;
    opacity: 1;
    border: 1px solid #fff;
}

.gr-ds-propImages .swiper-pagination-bullet-active {
    background-color: #a5a3a4 !important;
}

.gr-ds-propImages .swiper-slide img {
    width: 100%;
    object-fit: cover;
    height: 330px;
}

.gr-ds-propImages .swiper-slide::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.3);
}

.gr-ds-padding {
    padding: 12px 15px;
}

.gr-ds-margin {
    margin: 0 15px;
}

.gr-ds-checkDate p {
    color: var(--dr-shade-gray);
}

.gr-ds-checkIn {
    position: relative;
}

.gr-ds-checkIn::before {
    content: "";
    position: absolute;
    right: 20px;
    top: 0px;
    width: 1px;
    height: 100%;
    background-color: #f2f2f2;

}

.gr-ds-checkDate .gr-ds-cd-inner {
    width: 50%;
}

.gr-ds-checkDate .gr-ds-cd-inner .check,
.gr-ds-checkDate .gr-ds-cd-inner .date {
    font-weight: 500;
    font-size: 16px;
}

.gr-ds-checkDate .gr-ds-cd-inner .time {
    color: var(--them-secondary);
    font-size: 14px;
}

.gr-ds-hostInfo li i {
    font-size: 35px;
}

.gr-ds-hostInfo li a:hover {
    color: var(--dr-shade-gray);
}

.gr-ds-hostInfo li .gr-ds-hostInfo-inner {
    flex: 1;
    margin-left: 15px;
}

.gr-ds-hostInfo li .gr-ds-hostInfo-inner h6 {
    margin-bottom: 5px;
    color: var(--dr-shade-gray);
}

.gr-ds-hostInfo li .gr-ds-hostInfo-inner p {
    line-height: 1.2;
}

.gr-ds-paddingtb-border {
    padding: 15px;
    border-top: 10px solid #FAFAFA;
}

.gr-ds-dc-title {
    color: var(--dr-shade-gray);
}

.gr-ds-dc-inner {
    padding: 12px 0;
}

.gr-ds-dc-innerTitle {
    margin-bottom: 5px;
}

.gr-ds-dc-innerContent {
    margin-bottom: 0;
    color: var(--dr-shade-gray);
    font-weight: 500;
}

.gr-ds-reservDetail .gr-ds-dc-inner input {
    border: none;
    background: transparent;
    color: var(--dr-shade-gray);
    margin-right: 10px;
    width: 100px;
    outline: none;
}

.gr-ds-reservDetail .gr-ds-dc-inner input:focus {
    outline: none;
    border: none;
}

.gr-ds-reservDetail .gr-ds-dc-inner i {
    font-size: 25px;
    color: var(--dr-shade-gray);
}

.gr-ds-termPolicies ul li {
    padding-left: 15px;
    position: relative;
    font-weight: 500;
}

.gr-ds-termPolicies ul li::after {
    content: "";
    position: absolute;
    left: 0;
    top: 50%;
    width: 10px;
    height: 10px;
    background-color: var(--dr-shade-gray);
    border-radius: 50%;
    transform: translateY(-50%);
}

.gr-ds-checkinInfo {
    height: 100%;
    padding: 0;
    margin: 0;
}

.gr-ds-checkinInfo .gr-ds-dc-inner {
    height: 100%;
}

.gr-ds-dc-inner:has(.gr-ds-contIcon) {
    justify-content: space-between;
}

.gr-ds-contIcon .gr-ds-dc-innerContent {
    margin-left: 10px;
    flex: 1;
}

.gr-ds-dc-smIcon {
    font-size: 23px;
    line-height: 1;
}

.gr-ds-dc-lgIcon {
    font-size: 27px;
    line-height: 1;
}

.gr-ds-hr-checkInOut-list li i {
    line-height: 1;
    margin-right: 10px;
    color: var(--dr-shade-gray);
}

.gr-ds-hr-checkInOut .gr-ds-detailContent {
    margin: 15px 0;
}

.gr-ds-hr-checkInOut .gr-ds-dc-innerContent {
    font-weight: 400;
}

.guest-reservation .reservation-nodata {
    height: 300px;
    margin-bottom: 0;
}

.guest-reservation .tab-pane {
    margin-bottom: 0;
}

.text-copy-btn {
    position: relative;
}

.textCopy-tooltip {
    position: absolute;
    top: 30px;
    left: -30px;
    background: #373737;
    padding: 10px 15px;
    display: flex;
    justify-content: center;
    color: #fff;
    font-size: 14px;
    border-radius: 4px;
    letter-spacing: 1px;
    opacity: 0;
}

.textCopy-tooltip.appear {
    animation: appear 1s ease;
}

@keyframes appear {
    0% {
        opacity: 0;
    }

    20% {
        transform: translateY(10px);
        opacity: 1;
    }

    80% {
        transform: translateY(0px);
        opacity: 1;
    }

    100% {
        opacity: 0;
    }
}

/****** guest reservation detail page end******/

/* ************** account delete ************** */
.account-delete-sec {
    width: 100%;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.account-delete-sec .acc-dlt-form {
    width: 450px;
    margin: 0 auto;
    box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
    border-radius: 10px;
    padding: 40px 20px;
    height: max-content;
}

.acc-dlt-logo {
    text-align: center;
    margin-bottom: 35px;
}

.acc-dlt-logo img {
    width: 160px;
}

.acc-dlt-btn {
    text-align: center;
}

.acc-dlt-input {
    padding: 10px 10px !important;
}

.acc-dlt-textarea {
    resize: none;
}

.acc-dlt-btn button {
    width: 170px;
}

/**** filter dropdown****/
.search-location-inner {
    height: 47px;
}

.search-filter-select {
    width: 100% !important;
    background-color: transparent;
    border: 2px solid #d5d5d5;
    border-radius: 4px;
}

.search-filter-select .dropdown-toggle {
    background-color: transparent;
}

.search-filter-select .dropdown-toggle.show {
    border: unset !important;
}

.search-filter-select .btn.dropdown-toggle:hover,
.search-filter-select .btn.dropdown-toggle:focus {
    background-color: transparent;
    border-color: transparent;
    outline: none !important;
}

.search-filter-select .dropdown-menu.show {
    transform: translate(-44px, 86px) !important;
    width: 260px;
    border-radius: 25px;
    padding: 10px 0;
    border: 0;
    box-shadow: 1px 4px 10px 0 #00000059;
    z-index: 9999 !important;
}

.search-filter-select .bs-searchbox {
    padding: 0 !important;
    position: relative;
}

.search-filter-select.bootstrap-select .bs-searchbox::before {
    content: '\f002';
    font-family: 'Font Awesome 5 Free';
    position: absolute;
    top: 50%;
    left: 15px;
    font-size: 16px;
    color: #000000;
    font-weight: 800;
    transform: translateY(-50%);
}

.search-filter-select .dropdown-menu.show .bs-searchbox .form-control {
    border-radius: 0 !important;
    height: 40px;
    border: none;
    border-bottom: 1px solid #d5d5d5;
    border-radius: 0 !important;
    padding-left: 36px !important;
}

.search-filter-select .dropdown-menu.inner.show {
    transform: unset !important;
    width: 100%;
    border-radius: unset;

}

.search-filter-select .dropdown-menu.inner.show li {
    width: 100% !important;
    margin-bottom: 2px;
    padding: 0;
}

.search-filter-select .dropdown-menu.inner.show li .dropdown-item {
    padding: 10px 15px;
    border-radius: 0;
}

.search-filter-select .dropdown-menu.inner.show li .dropdown-item span {
    margin-bottom: 0;
    color: #000000;
    font-size: 16px;
}

.search-filter-select.bootstrap-select.show-tick .dropdown-menu li a span.text {
    text-transform: capitalize;
}

.search-filter-select.bootstrap-select.show-tick .dropdown-menu .selected span.check-mark {
    right: 20px;
    top: 45%;
    transform: translateY(-50%);
}

.search-filter-select.bootstrap-select .bs-ok-default:after {
    width: 0.42em;
    height: 0.8em;
}

.search-filter-select.bootstrap-select>.dropdown-toggle {
    height: 46px;
}

.search-filter-select.bootstrap-select .dropdown-toggle .filter-option {
    display: flex;
    align-items: center;
}

.search-filter-select.bootstrap-select .dropdown-item.active {
    background-color: #FFF3E0 !important;
}

.search-filter-select.bootstrap-select .dropdown-item:hover {
    background-color: #FFF3E0 !important;
}

/***** ilm yaqeen ****/
.after-payment-ilmyakeen .modal-body {
    direction: rtl;
}

/**** ilm yaeen modal ui *****/
.im-country-select {
    width: 100% !important;
    background-color: transparent;
    border: 1px solid #d5d5d5;
    border-radius: 17px;
    margin-top: 10px;
}

.im-country-select .dropdown-toggle.show {
    border: unset !important;
}

.im-country-select .dropdown-toggle {
    background-color: transparent;
}

.im-country-select.bootstrap-select>.dropdown-toggle {
    height: 43px;
    border-radius: 25px;
    padding-left: 24px !important;
}

.im-country-select .btn.dropdown-toggle:hover,
.im-country-select .btn.dropdown-toggle:focus {
    background-color: transparent;
    border-color: transparent;
    outline: none !important;
}

.im-country-select .bs-searchbox {
    padding: 0 !important;
    position: relative;
}

.im-country-select.bootstrap-select .bs-searchbox::before {
    content: '\f002';
    font-family: 'Font Awesome 5 Free';
    position: absolute;
    top: 50%;
    left: 15px;
    font-size: 16px;
    color: #000000;
    font-weight: 800;
    transform: translateY(-50%);
}

.im-country-select .dropdown-menu.show .bs-searchbox .form-control {
    height: 40px;
    border: none;
    border-bottom: 1px solid #d5d5d5;
    border-radius: 0 !important;
    padding-left: 36px !important;
}

.im-country-select .dropdown-menu.show .bs-searchbox .form-control:focus {
    outline: none !important;
}

.im-country-select .dropdown-menu.inner.show {
    transform: unset !important;
    width: 100%;
    border-radius: unset;
}

.im-country-select .dropdown-menu.inner.show li {
    width: 100%;
    margin-bottom: 2px;
    padding: 0;
}

.im-country-select .dropdown-menu.inner.show li .dropdown-item {
    padding: 5px 15px;
    border-radius: 0;
}

.im-country-select.bootstrap-select .dropdown-item.active {
    background-color: #FFF3E0 !important;
}

.im-country-select.bootstrap-select.show-tick .dropdown-menu .selected span.check-mark {
    right: 20px;
    top: 45%;
    transform: translateY(-50%);
}

.im-country-select .dropdown-menu.inner.show li .dropdown-item span {
    margin-bottom: 0;
    color: #000000;
    font-size: 16px;
}

.im-country-select.bootstrap-select.show-tick .dropdown-menu li a span.text {
    text-transform: capitalize;
}

.im-country-select.bootstrap-select .dropdown-toggle .filter-option {
    display: flex;
    align-items: center;
}

.im-country-select .dropdown-toggle .filter-option-inner-inner {
    color: var(--them-secondary);
    font-size: 14px;
}

.hijri-calendar-check {
    display: none;
}

.hijri-calendar-check.active {
    display: block;
}

.after-payment-ilmyakeen .im-country-select.bootstrap-select>.dropdown-toggle {
    padding-left: 0 !important;
    padding-right: 24px !important;
}

.hijri-calendar-check-pay {
    display: none;
}

.hijri-calendar-check-pay.active {
    display: block;
}

#ilmyakeen .invalid-feedback {
    text-align: start;
}

/*product slider btn prev next*/
.swiper-button-hp-next,
.swiper-button-hp-prev {
    position: absolute;
    top: var(--swiper-navigation-top-offset, 50%);
    width: 40px;
    height: 40px;
    background: #ffff;
    border-radius: 80px;
    box-shadow: 1px 3px 5px 1px #0000002b;
    padding: 5px;
    align-items: center;
    z-index: 10;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.swiper-button-hp-next.swiper-button-disabled,
.swiper-button-hp-prev.swiper-button-disabled {
    opacity: .35;
    cursor: auto;
    pointer-events: none
}

.swiper-button-hp-next.swiper-button-hidden,
.swiper-button-hp-prev.swiper-button-hidden {
    opacity: 0;
    cursor: auto;
    pointer-events: none
}

.swiper-navigation-disabled .swiper-button-hp-next,
.swiper-navigation-disabled .swiper-button-hp-prev {
    display: none !important
}

.swiper-button-hp-next svg,
.swiper-button-hp-prev svg {
    width: 100%;
    height: 100%;
    object-fit: contain;
    transform-origin: center
}

.swiper-rtl .swiper-button-hp-next svg,
.swiper-rtl .swiper-button-hp-prev svg {
    transform: rotate(180deg)
}

.swiper-button-hp-prev,
.swiper-rtl .swiper-button-hp-next {
    left: var(--swiper-navigation-sides-offset, 10px);
    right: auto
}

.swiper-button-hp-next,
.swiper-rtl .swiper-button-hp-prev {
    right: var(--swiper-navigation-sides-offset, 10px);
    left: auto
}

.swiper-button-lock {
    display: none
}

.swiper-button-hp-next:after,
.swiper-button-hp-prev:after {
    font-family: swiper-icons;
    text-transform: none !important;
    letter-spacing: 0;
    font-variant: initial;
    line-height: 1;
    font-size: 18px;
    color: #000000;
}

.swiper-button-hp-prev:after,
.swiper-rtl .swiper-button-hp-next:after {
    content: 'prev'
}

.swiper-button-hp-next,
.swiper-rtl .swiper-button-hp-prev {
    right: var(--swiper-navigation-sides-offset, 10px);
    left: auto
}

.swiper-button-hp-next:after,
.swiper-rtl .swiper-button-hp-prev:after {
    content: 'next'
}

/***** host profile image style *****/
.host-profile-setting {
    max-width: max-content;
    display: flex;
    align-items: center;
    flex-direction: column;
}

.hp-setting-img {
    position: relative;
}

.hp-setting-img .host-upload-btn {
    position: absolute;
    right: 1px;
    bottom: 10px;
    background-color: transparent;
    border: none;
    font-size: 18px;
}

.hp-setting-img .host-upload-btn i:hover {
    transition: all .3s cubic-bezier(.175, .885, .32, 1.275);
    color: #999;
}

.hp-setting-img img {
    width: 100px;
    height: 100px;
    object-fit: cover;
    border-radius: 50%;
    box-shadow: 0 1px 10px #0000001f, 0 4px 5px #00000024, 0 2px 4px -1px #0003;
}

.hp-setting-btn {
    margin-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hp-setting-btn button {
    background-color: transparent;
    border: 1px solid #181818;
    border-radius: 25px;
    padding: 0 15px;
    font-size: 14px;
    height: 25px;
}

.hp-setting-btn button:first-child {
    margin-right: 5px;
}

.hp-setting-btn button:hover {
    background-color: #181818;
    color: #ffffff;

}

/**** review modal single page ***/
.show-review-sidebar {
    padding: 0 20px 0 0;
}

.sr-sidebar-progress .review-progress p {
    margin-right: 10px;
}

.sr-sidebar-progress .review-progress .progress {
    height: 5px;
}

.sr-sidebar-rate {
    margin: 35px 0 0;
}

.sr-sidebar-rate-inner {
    justify-content: space-between;
    border-bottom: 1px solid #bfbbbb8f;
    padding: 4px 0;
}

.sr-sidebar-rate-inner .sr-sr-inner-left i {
    font-size: 25px;
    color: #575757;
    margin-right: 20px;
}

.sr-sidebar-rate-inner p {
    color: #575757;
    font-size: 18px;
}

.sr-sidebar-rate-inner .sr-sr-inner-right {
    justify-content: space-between;
    flex: 1;
}

.sr-sidebar-rate-inner .sr-sr-inner-right span {
    font-size: 16px;
}

.sr-sidebar-rate li:last-child .sr-sidebar-rate-inner {
    border-bottom: none;
}

.sr-content-head {
    justify-content: space-between;
    transition: position .3s;
}

.sr-content-head select {
    width: 120px;
    height: 40px;
    padding: 10px;
}

.sr-content-search {
    position: relative;
    margin: 15px 0;
}

.sr-content-search input {
    height: 40px;
    padding-left: 40px;
}

.sr-content-search i {
    position: absolute;
    top: 50%;
    left: 15px;
    transform: translateY(-50%);
    font-size: 20px;
    color: var(--them-secondary)
}

.sr-content-body {
    height: 420px;
    overflow-x: auto;
    padding: 0 10px;
}

.sr-comment {
    margin-bottom: 25px;
}

.sr-profile .sr-profile-img img {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    object-fit: cover;
}

.sr-profile .sr-profile-name {
    margin-left: 10px;
}

.sr-profile .sr-profile-name p {
    font-size: 14px;
}

.sr-pc-star-rate {
    display: flex !important;
    align-items: center;
}

.sr-pc-star-rate .star-rating {
    display: flex !important;
    align-items: center;
}

.sr-pc-star-rate .star-rating label {
    cursor: default;
    font-size: 0;
    line-height: 1;
}

.sr-pc-star-rate .star-rating label:hover {
    color: #bbb;
}

.sr-pc-star-rate .star-rating label:hover~label {
    color: #bbb;
}

.sr-pc-star-rate .star-rating label i {
    font-size: 14px;
}

.sr-cb-time {
    margin-left: 10px;
}

.sr-cb-time p {
    color: #575757;
    font-size: 14px;
}

.sr-profile-comment .sr-pc-content {
    line-height: 1.3;
}

.sr-comment-host {
    margin: 0 0 0 20px;
}

.sr-comment-host .sr-profile .sr-profile-img img {
    width: 35px;
    height: 35px;
}

.sr-comment-host .sr-profile-name h6 {
    font-size: 14px;
}

.sr-comment-host .sr-profile-name p {
    font-size: 12px;
}

.sr-comment-host .sr-profile-comment .sr-pc-content {
    font-size: 15px;
}

.sr-sidebar-title {
    display: flex;
    align-items: normal;
}

.sr-sidebar-title i {
    font-size: 35px;
    margin-right: 10px;
}

.show-all-review-btn {
    width: max-content;
    margin: 20px auto 0;
    display: block;
}

/*** listing bank page ***/
.add-bank-account-listting .right-side {
    position: relative;
    flex-direction: column;
    padding-bottom: 20px;
    text-align: center;
    min-height: calc(100vh - 200px);
    display: flex;
    align-items: center;
    ;
    justify-content: center;
}

.add-bank-account-listting .right-side input {
    width: 100%;
}

.list-skip-btn {
    margin-right: 5px;
}

/**** host agreement modal ****/
.accept-term .modal-title {
    top: 0;
}

.accept-term .form-check-main {
    margin: 20px 0 25px;
}

.accept-term .form-check {
    display: flex;
    align-items: center;
}

.accept-term .form-check input {
    margin-top: 0;
}

.accept-term .form-check label p {
    margin: 0 0 0 5px;
}

.accept-term button {
    width: 100%;
    padding: 10px 0;
}

/**** insurance page ****/
.if-mb {
    margin-bottom: 30px;
}

.insurance-page {
    margin: 40px 0 0 0;
}

.insurance-page p {
    color: #7D7D7D;
    font-size: 17px;
}

.insur-feature-box {
    border: 2px solid #F2F2F2;
    border-radius: 16px;
    padding: 20px;
    height: 100%;
}

.if-box-icon {
    width: 50px;
    height: 50px;
    border-radius: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #FCB83340;

}

.insur-feature-box img {
    width: 26px;
}

.insur-feature-box h4 {
    margin: 10px 0 20px;
}

.insurance-cover .insurance-cover-list {
    margin: 0;
    padding: 0;
    list-style: none;
    margin-top: 20px;
}

.insurance-cover .insurance-cover-list li {
    display: flex;
    align-items: start;
}

.insurance-cover .insurance-cover-list li .ic-list-icon img {
    width: 26px;
    margin-right: 10px;
}

.inurance-limit .incurance-limit-box {
    border: 2px solid #F2F2F2;
    border-radius: 16px;
    padding: 20px;
    height: 100%;
    display: flex;
    align-items: start;
}

.inurance-limit .ic-box-icon {
    width: 50px;
    height: 50px;
    border-radius: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #FCB83340;
    margin-right: 20px;
}

.inurance-limit .ic-box-icon img {
    width: 26px;
}

.inurance-claim .insurance-claim-list {
    counter-reset: list-counter;
    list-style: none;
}

.inurance-claim .insurance-claim-list li {
    position: relative;
    padding-left: 20px;
    margin-bottom: 25px;
}

.inurance-claim .insurance-claim-list li::before {
    counter-increment: list-counter;
    content: counter(list-counter);
    color: #FCB833;
    position: absolute;
    top: 1px;
    left: 0;
    font-size: 20px;
    font-weight: 700;
    line-height: 1;
}

.incurance-contact-ic {
    display: flex;
    align-items: center;
}

.incurance-contact-ic img {
    width: 30px;
    margin-right: 5px;
}

.incurance-contact-ic h4 {
    line-height: 1;
}

.insur-foot-logo img {
    width: 450px;
}

.iq-page {
    height: calc(100vh - 82px);
    display: flex;
    flex-direction: column;
    justify-content: center;
}

/** wallet pay button**/
.ch-wallet-pay {
    margin-top: 35px;
    position: relative;
}

.ch-wallet-pay::before {
    content: "";
    position: absolute;
    top: -19px;
    left: 0;
    width: 100%;
    border-top: 1px solid #d1d1d1;
}

.ch-wallet-pay label {
    position: relative;
    display: block;
    width: 40px;
    height: 22px;
    background: transparent;
    border-radius: 30px;
    transition: all 300ms linear;
    transition: all 300ms linear;
    border: 1px solid #666;
    margin: 0 10px 0 0 !important;
}

.ch-wallet-pay label::before {
    position: relative;
    content: "";
    width: 15px;
    height: 15px;
    background: #666;
    display: block;
    border-radius: 50%;
    top: 3px;
    left: 4px;
    transition: all 300ms linear;
}

.ch-wallet-pay input:checked+label {
    border-color: var(--theme-primary);
}

.ch-wallet-pay input:checked+label::before {
    left: 19px;
    background-color: var(--theme-primary);
}

.ch-wallet-pay input {
    display: none;
}

.ch-wallet-pay img {
    margin-right: 5px;
}

/* hyper pay section*/
.hyper-pay-sec .pc-sec-inner {
    height: max-content;
    padding: 40px 40px 5px;
}

.hyper-pay-sec .pc-sec-inner .logo-two {
    margin-bottom: 30px;
}

/** question page modal **/
.permit {
    position: relative;
    flex-direction: column;
    padding-bottom: 20px;
    text-align: center;
    min-height: calc(100vh - 200px);
    display: flex;
    align-items: center;
    justify-content: center;
}

.permit i {
    font-size: 75px;
}

.permit h4 {
    margin: 40px 0 10px;
}

.permit p {
    font-size: 18px;
    margin-bottom: 15px;
}

.permit input::file-selector-button {
    border-radius: 12px;
    border: none;
}

.permit input {
    padding-left: 18px !important;
}

/** payment option update**/
.pay-sec p {
    color: #575757;
    font-size: 16px;
    margin-bottom: 6px;
}

.prop-payment-option {
    display: flex;
    align-items: center;
    flex-flow: wrap;
}

.prop-payment-option .pay-sec-label {
    position: relative;
    width: 48%;
    border: none;
    border-radius: unset;
    padding: 0;
    display: block;
}

.prop-payment-option .pay-sec-label:nth-child(1),
.prop-payment-option .pay-sec-label:nth-child(3) {
    margin-right: 3px;
}

.prop-payment-option .pay-sec-label:nth-child(2),
.prop-payment-option .pay-sec-label:nth-child(4) {
    margin-left: 3px;
}

.prop-payment-option .pay-sec-label input {
    opacity: 0;
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    margin: 0;
    cursor: pointer;
}

.prop-payment-option .pay-sl-content {
    width: 100%;
    color: var(--primary);
    box-shadow: none;
    border: 2px solid transparent;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 4px 25px 0 rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 25px 0;
    cursor: pointer;
}

.prop-payment-option input:checked+.pay-sl-content::after {
    content: "\f058";
    color: var(--theme-primary);
    position: absolute;
    right: 5px;
    top: 6px;
    font-family: "Font Awesome 5 Free";
    font-style: normal;
    font-size: 16px;
    font-weight: 900;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    -webkit-animation-name: fadeInCheckbox;
    animation-name: fadeInCheckbox;
    -webkit-animation-duration: 0.3s;
    animation-duration: 0.3s;
    -webkit-animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.prop-payment-option input:checked+.pay-sl-content {
    border: 2px solid var(--theme-primary);
    -webkit-transition: border 0.3s;
    -o-transition: border 0.3s;
    transition: border 0.3s;
}

.pay-sec .pay-sec-label .pay-sl-content img {
    object-fit: contain;
    width: 70px;
    transition: .3s ease-in-out;
    height: 20px;
}

@-webkit-keyframes fadeInCheckbox {
    from {
        opacity: 0;
        -webkit-transform: rotateZ(-20deg);
    }

    to {
        opacity: 1;
        -webkit-transform: rotateZ(0deg);
    }
}

@keyframes fadeInCheckbox {
    from {
        opacity: 0;
        transform: rotateZ(-20deg);
    }

    to {
        opacity: 1;
        transform: rotateZ(0deg);
    }
}

/** payment option update end**/

/** wallet pay button**/
.ch-wallet-pay {
    margin-top: 35px;
    position: relative;
}

.ch-wallet-pay::before {
    content: "";
    position: absolute;
    top: -19px;
    left: 0;
    width: 100%;
    border-top: 1px solid #d1d1d1;
}

.ch-wallet-pay label {
    position: relative;
    display: block;
    width: 40px;
    height: 22px;
    background: transparent;
    border-radius: 30px;
    transition: all 300ms linear;
    transition: all 300ms linear;
    border: 1px solid #666;
    margin: 0 10px 0 0 !important;
}

.ch-wallet-pay label::before {
    position: relative;
    content: "";
    width: 15px;
    height: 15px;
    background: #666;
    display: block;
    border-radius: 50%;
    top: 3px;
    left: 4px;
    transition: all 300ms linear;
}

.ch-wallet-pay input:checked+label {
    border-color: var(--theme-primary);
}

.ch-wallet-pay input:checked+label::before {
    left: 19px;
    background-color: var(--theme-primary);
}

.ch-wallet-pay input {
    display: none;
}

.iqama-support-contact a {
    color: #25d366;
    position: relative;
    font-weight: 500;
    margin-left: 2px;
    transition: .3s all;
    font-size: 14px;
}

.iqama-support-contact a i {
    font-size: 17px;
}

.iqama-support-contact a::before {
    position: absolute;
    content: "";
    bottom: 3px;
    left: 0;
    width: 100%;
    transition: .3s all;
    border-bottom: 2px solid #25d366;
}

.iqama-support-contact a:hover::before {
    border-color: #000000;
}

/* annoucement page */
.annoucement {
    padding: 50px 0;
}

.annoucement p {
    color: #000;
}

/* hyper pay section*/
.hyper-pay-sec .pc-sec-inner {
    height: max-content;
    padding: 40px 40px 5px;
}

.hyper-pay-sec .pc-sec-inner .logo-two {
    margin-bottom: 30px;
}

/** question page modal **/
.permit {
    position: relative;
    flex-direction: column;
    padding-bottom: 20px;
    text-align: center;
    min-height: calc(100vh - 200px);
    display: flex;
    align-items: center;
    justify-content: center;
}

.permit i {
    font-size: 75px;
}

.permit h4 {
    margin: 40px 0 10px;
}

.permit p {
    font-size: 18px;
    margin-bottom: 15px;
}

.permit input::file-selector-button {
    border-radius: 12px;
    border: none;
}

.permit input {
    padding-left: 18px !important;
}

.list-skip-btn {
    margin-right: 5px;
}

@media (max-width: 490px) {
    .permit i {
        font-size: 60px;
    }

    .permit h4 {
        font-size: 18px;
    }
}

.iqama-support-contact a:hover::before {
    border-color: #000000;
}

/* annoucement page */
.annoucement {
    padding: 50px 0;
}

.annoucement p {
    color: #000;
}

.annoucement .main-title h1 {
    font-weight: 700;
    /* text-align: center; */
    font-size: 40px;
}

.ancmnt-banner-main {
    margin: 30px 0;
}

.annoucement .ancmnt-banner {
    height: auto;
    object-fit: cover;
    width: 100%;
}

.ancmnt-banner-main {
    margin: 30px 0;
}

.annoucement .ancmnt-banner {
    /* height: 100vh; */
    object-fit: cover;
    width: 100%;
}

.ancmnt-banner-main p {
    font-weight: 500;
    margin-top: 15px;
}

.ancmnt-content p {
    font-size: 17px;
    margin-bottom: 25px;
}


.ancmnt-content a {
    color: var(--theme-primary);
    text-decoration: underline;
}

.dots {
    width: 56px;
    height: 26.9px;
    background: radial-gradient(circle closest-side, #f5c33e 90%, #0000) 0% 50%,
        radial-gradient(circle closest-side, #f5c33e 90%, #0000) 50% 50%,
        radial-gradient(circle closest-side, #f5c33e 90%, #0000) 100% 50%;
    background-size: calc(100%/3) 13.4px;
    background-repeat: no-repeat;
    animation: dots-7ar3yq 1s infinite linear;
}

@keyframes dots-7ar3yq {
    20% {
        background-position: 0% 0%, 50% 50%, 100% 50%;
    }

    40% {
        background-position: 0% 100%, 50% 0%, 100% 50%;
    }

    60% {
        background-position: 0% 50%, 50% 100%, 100% 0%;
    }

    80% {
        background-position: 0% 50%, 50% 50%, 100% 100%;
    }
}

.search-reservation li {
    width: 43% !important;
}

.search-reservation li:nth-child(2) {
    width: 43% !important;
}

.search-reservation li:last-child {
    width: 14% !important;
}

button.submit-header {
    line-height: 1 !important;
    padding-top: 25px !important;
}

.heade-date input {
    text-align: start !important;
}

/**** Property Vverify Modal ****/
.prop-verify-modal .modal-content {
    /* background-color: var(--theme-primary); */
}

.prop-verify-modal .alert-modal img {
    width: 70px;
}

.prop-verify-modal h3 {
    color: #181818;
}

.prop-verify-modal .alert-modal p {
    color: #181818;
    font-weight: 500;
    font-size: 16px;
}

.prop-verify-modal .host-btn-black {
    background: #181818;
    padding: 10px 20px;
    font-size: 16px;
    display: block;
    width: max-content !important;
    margin: 0 auto;
}

.prop-verify-modal a {
    color: #181818;
    font-weight: 500;
    display: inline-block;
}

.prop-verify-modal a:hover {
    color: #fff;
}

/**** host license css ****/
.mb-license-option-select {
    display: flex;
    align-items: center;
    justify-content: center;
}

.mb-license-option {
    padding: 0 5px;
}

/**** instruction page ****/
.instruction .aboutsec-content {
    text-align: start;
}

.instruction .instruct-title {
    color: #181818;
    font-weight: 600;
}

.instruction .instruct-content {
    color: #181818;
    font-weight: 500;
    font-size: 18px;
}

.instruction .instruct-list {
    list-style: none;
    counter-reset: line;
    margin: 15px 0 0 15px;
}

.instruction .instruct-list li {
    position: relative;
    font-weight: 500;
    font-size: 18px;
    margin-bottom: 10px;
}

.instruction .instruct-list li a {
    color: var(--theme-primary);
}

.instruction .instruct-list li:before {
    position: absolute;
    inset-inline-start: -18px;
    display: inline-block;
    border-radius: 50%;
    color: #181818;
    text-align: center;
    counter-increment: line;
    content: counter(line) '.';
    font-size: 17px;
}

.instruction-title-style {
    position: relative;
    width: max-content;
}

.instruction-title-style::before {
    content: "";
    position: absolute;
    left: 0;
    bottom: 8px;
    height: 4px;
    width: 100%;
    background-color: var(--theme-primary);
    z-index: -1;
}

.instruct-reg-prop-sec {
    position: relative;
    padding-top: 30px;
}

.instruct-reg-prop-sec::before {
    content: '';
    position: absolute;
    top: 100px;
    left: 0;
    background-image: url(../images/instruction/theme-dots-pattern.svg);
    background-size: contain;
    background-repeat: no-repeat;
    width: 140px;
    height: 270px;
}

.instruct-reg-prop-sec::after {
    content: '';
    position: absolute;
    bottom: -160px;
    right: 0;
    background-image: url(../images/instruction/airplane.svg);
    background-size: cover;
    background-repeat: no-repeat;
    width: 140px;
    height: 130px;
}

.instruct-reg-prop-sec .instruct-list {
    margin: 30px 0 0 55px;
    display: flex;
    flex-flow: wrap;
    align-items: center;
    gap: 20px;

}

.instruct-reg-prop-sec .instruct-list li {
    flex: 0 0 45%;
    max-width: 45%;
}

.instruct-reg-prop-sec .instruct-list li:before {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    color: #181818;
    background-color: #E7E7E7;
    content: counter(line);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 22px;
    inset-inline-start: -55px;
}

.instruct-reg-prop-sec .instruct-list li p {
    width: 90%;
    color: #181818;
    font-size: 18px;
    font-weight: 500;
}

.instruct-reg-prop-sec .instruct-list li img {
    width: 90%;
    height: 290px;
    border: 1px solid #E7E7E7;
    border-radius: 8px;
}

.instruc-help-sec-main{
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 30px;
    gap: 10px;
}

/* .instruc-help-sec {
    display: inline;
} */

.instruc-help-sec .instruc-help-sec-icon {
    width: 120px;
    height: 120px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.instruc-help-sec .instruc-help-sec-icon img {
    width: 85px;
}

.instruc-help-sec-icon .ih-si-two {
    width: 70px !important;
}

.instruc-help-sec h5 {
    font-weight: 600;
    margin: 15px 0;
}

.instruc-help-sec button {
    padding: 0 15px;
    height: 51px;
    font-size: 18px;
    border-radius: 8px;
}

.need-help-sec .aboutsec-content {
    top: 60%;
}

/* .instruction .prg-main-title {
    font-size: 36px;
    font-weight: 700;
    color: #5F5F5F;
    margin-bottom: 20px;
  }

  .instruction .card-container {
    display: flex;
    gap: 30px;
    flex-wrap: wrap;
    justify-content: center;
  }

  .instruction .card {
    background-color: #fff;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    flex: 1;
    max-width: 50%;
    transition: all 0.3s ease;
  }

  .instruction .card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
  }

  .instruction .card h2 {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 20px;
    color: #5F5F5F;
    position: relative;
  }

  .instruction .card h2:before {
    content: "";
    position: absolute;
    display: block;
    width: 60px;
    height: 4px;
    background-color: var(--theme-primary);
    bottom: -4px;
  }

  .instruction .steps {
    display: flex;
    flex-direction: column;
    gap: 15px;
  }

  .instruction .step {
    display: flex;
    align-items: center;
    gap: 15px;
    background-color: #f9f9f9;
    padding: 12px;
    border-radius: 10px;
    transition: all 0.3s ease;
  }

  .instruction .step-number {
    background-color: #5F5F5F;
    color: #fff;
    font-weight: 700;
    font-size: 18px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
  }

  .instruction .step p {
    margin: 0;
    font-size: 16px;
    color: #666;
    flex: 1;
  }

  .instruction a {
    color: var(--theme-primary);
    font-weight: 600;
    text-decoration: none;
  }

  .instruction a:hover {
    color: #ff8a00;
  }

  .instruction .registered {
    background-color: #f0f0f0;
  }

  @media (max-width: 768px) {
    .instruction .card-container {
      flex-direction: column;
    }
  } */

@media (max-width: 1560px) {
    .instruct-reg-prop-sec {
        padding-top: 50px;
    }

    .instruct-reg-prop-sec::after {
        bottom: -132px;
    }

    .instruct-reg-prop-sec::before {
        top: 100px;
    }

    .instruct-reg-prop-sec::before {
        left: -30px;
    }

    .instruct-reg-sec .believe-content {
        padding-top: 30px;
    }
}

@media (max-width: 1440px) {
    .search-filter-select .dropdown-menu.show {
        transform: translate(-44px, 70px) !important;
    }

    .search-filter-select.bootstrap-select>.dropdown-toggle {
        height: 41px;
    }
}

@media (max-width: 1280px) {
    .instruct-reg-prop-sec::after {
        bottom: -115px;
    }
}

@media(max-width: 1366px) {
    .story-content h1 {
        font-size: 65px;
    }

    .story-content h2 {
        font-size: 35px;
    }

    .believe-content p {
        font-size: 20px;
    }

    .airplane-img {
        width: 29%;
    }

    .as-mid-content h4 {
        font-size: 24px;
        line-height: 1.5;
    }

    .baij-img {
        width: 260px;
    }

    .about-sl-cw {
        padding: 30px 15px;
    }

    .about-sl-cw h1 {
        font-size: 30px;
    }

    .about-sl-cw h2 {
        font-size: 28px;
    }

    .about-sl-cw p {
        font-size: 22px;
    }

    .pay-sec {
        margin: 22px 23.5px 10px 0;
    }

    .pay-sec p {
        font-size: 14px;
    }

    .ch-host-btns button,
    .ch-host-btns a {
        padding: 0 20px;
        height: 44px;
        font-size: 16px;
    }

    .ch-hb-btn img {
        width: 8px;
    }

    /* .pay-sec .pay-sec-label {
    /* .pay-sec .pay-sec-label {
        width: 130px;
    } */

    .pay-sec .pay-sec-label .pay-sl-content img {
        /* width: 22px; */
        margin-right: 0px;
    }

    .pay-sec .pay-sec-label .pay-sl-cdetail span {
        font-size: 14px;
    }

    .rs-start-sec .rs-start-sec-inner img {
        width: 120px;
        margin-bottom: 20px;
    }

    .rs-start-sec .rs-start-sec-inner p {
        font-size: 22px;
        margin-bottom: 25px;
    }

    .rs-fs-title {
        font-size: 34px;
    }

    .rs-start-sec .rs-start-sec-inner h1 {
        margin-bottom: 18px;
    }

    .rs-fs-title-child {
        font-size: 26px;
    }

    .rs-fs-content-child {
        font-size: 23px;
    }

    .rs-ra-textarea textarea {
        height: 170px !important;
    }

    /*host-chat*/
    .chat-reservation-detail .hs-cust-paddinglr {
        padding-left: 0;
        padding-right: 0;
    }

    .chat-reservation-detail .hs-custdetail-fwc {
        font-size: 14px;
    }

    .chat-reservation-detail .host-custabout-content img {
        width: 18px;
    }

    .instruct-reg-prop-sec .instruct-list li img {
        height: 220px;
    }
}

@media(max-width: 1024px) {
    .pay-sec .prop-payment-option .pay-sec-label .pay-sl-content img {
        width: 64px;
        object-fit: contain;
    }

    .rl-fields {
        margin: 15px 0 20px;
    }

    .instruct-reg-sec {
        height: 300px;
    }

    .instruc-help-sec {
        margin-top: 0;
    }

    .instruc-help-sec .instruc-help-sec-icon {
        width: 90px;
        height: 90px;
    }

    .instruc-help-sec .instruc-help-sec-icon img {
        width: 75px;
    }

    .instruc-help-sec-icon .ih-si-two {
        width: 60px !important;
    }

    .instruc-help-sec button {
        font-size: 18px;
        height: 45px;
    }
}

@media(max-width: 991px) {
    .what-next {
        margin-top: 30px;
    }

    .listing-hstAgree {
        border: none;
        padding: 0;
        margin-bottom: 20px;
    }
}

@media (max-width: 767px) {
    .pay-card-sec {
        align-items: start;
        padding-top: 40px;
        height: auto;
    }
}

@media (max-width: 700px) {


    .tr-f img {
        width: 100%;
    }

    .upload__img-close {
        line-height: 1.4;
        text-align: center;
    }

    .heade-date input {
        height: 18px;

    }

    .no-scroll {
        padding-right: 0px;
    }

    /* .listing-checkbox-tile {
    border: none;
  } */
    .listing-checkbox-input:checked+.listing-checkbox-tile {
        box-shadow: none;
    }

    .pricing-inner form .reserve,
    .property-detail .property-gallery .inner-image img,
    footer .copy-right,
    ul.side-inner {
        margin-bottom: 0;
    }

    .pr-mini-detail h4,
    button#view-cl {
        text-align: left;
    }

    img.fl-check {
        position: absolute;
        bottom: -18px;
        right: 0;
        transform: translateX(46%);
        width: 20px !important;
    }

    .payment-detail {
        height: auto;
    }

    .card-detail {
        margin-bottom: 60px !important;
    }

    .ls-item .col-2,
    .total-credit-detail {
        width: 100%;
    }

    .nt-date {
        font-size: 11px;
        color: gray;
        font-weight: 600;
    }

    .notfctn-user-detail p {
        font-size: 12px;
        width: 120px;
    }

    .notification-user {
        padding: 15px;
    }

    .notification-main {
        padding: 10px;
    }

    .reserv-head .reserv-head-btn {
        justify-content: space-between;
    }

    button#view-cl {
        display: flex;
    }

    .navmenu {
        right: -60px;
    }

    .product-category li:hover {
        border: none;
    }

    .feature .bg {
        border-radius: 10px;
        text-align: center;
    }

    .for-mobile,
    .property-inner-detail,
    .show {
        display: block !important;
    }

    .floated-img,
    .for-desktop,
    .host-help::before,
    .notification-drop,
    .wish-icons span {
        display: none;
    }

    header .nav .header-btn {
        padding-right: 10px;
        font-size: 11px;
        height: 20px;
    }

    header .search-reservation li {
        font-size: 10px;
        padding: 0 5px;
        position: relative;
        width: 36%;
    }

    header .search-reservation li span,
    header .search-reservation p {
        font-size: 12px;
    }

    .heade-date input {
        font-size: 12px;
        font-weight: 700;
    }

    .floated-img {
        position: relative;
        height: 16vh;
        padding-top: 15px;
    }

    .loader-bg {
        background: #fff;
    }

    header .search-reservation li:last-child {
        padding-right: 7px;
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
        }
    }

    .animate-flicker {
        animation: 0.8s infinite alternate fadeIn;
    }

    section.home-bg {
        height: 78vh;
        background: #f5c33e;
        padding: 40px 0;
    }

    .feature .bg .d img {
        opacity: 34%;
        z-index: -1;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        right: unset;
    }

    .feature .bg {
        background-color: var(--theme-primary);
        position: relative;
        margin-bottom: 20px;
        z-index: 1;
        padding: 0 10px;
    }

    .feature-content {
        color: #fff;
        height: 100%;
        align-items: center;
        justify-content: center;
    }

    .feature-content .fea-btn button:last-child {
        margin-left: 0;
        padding: 6px 25px;
        margin-top: 11px;
    }

    footer .social-icon {
        justify-content: center;
        padding-bottom: 10px;
        margin-top: 16px;
    }

    .footer-logo {
        text-align: center;
        margin-bottom: 24px;
    }

    button.submit-header {
        width: 36px;
        height: 36px;
        font-size: 14px;
    }

    .banner-content {
        position: inherit;
        bottom: 30px;
    }

    #pagination .pl-3,
    .fl-mob,
    .list-btn,
    .text-md-center {
        text-align: center;
    }

    .daterangepicker {
        background: #fff;
        padding: 10px 15px;
        border-radius: 30px;
        width: 90%;
        left: 15px !important;
        margin: 0 auto !important;
    }

    .listing-footer,
    .nav-header,
    .side-menu-inner {
        background: #ffff;
    }

    .host-help {
        padding: 60px 0;
    }

    .host-banner {
        height: auto;
        padding-bottom: 60px;
    }

    section.host-banner .fl-mob img {
        filter: drop-shadow(6px 8px 5px #00000026);
    }

    /* .host-help .content1 {
      position: inherit;
      background: 0 0;
      color: var(text-primary);
      padding: 11px;
    } */
    .change-pro,
    section.host-help {
        margin: 0;
    }

    .host-help .help-content li img {
        width: 30px;
        padding-top: 0;
        margin: 0 auto 15px;
    }

    .phone-verification {
        padding: 0;
    }

    .side-list {
        padding-left: 0;
    }

    ul.side-inner {
        display: flex;
        align-items: center;
        justify-content: start;
        overflow-y: hidden;
        overflow-x: scroll;
    }

    ul.side-inner li a svg {
        padding-bottom: 0px;
        display: block;
        margin: 0px auto;
        position: relative;
        top: 13px;
    }

    ul.side-inner::-webkit-scrollbar {
        width: 0;
        height: 0px;
    }



    .list-hd {
        padding-top: 25px;
        padding-bottom: 0;
        text-align: center;
    }

    ul.side-inner li {
        margin-bottom: 0;
        margin-right: 10px;
        margin-left: 10px;
    }

    .side-op {
        font-size: 22px;
    }

    ul.side-inner li a {
        text-align: center;
        font-size: 12px;
        border: none;
        padding: 5px;
        width: 100px;
        height: 70px;
        border-radius: 10px;
        display: block;
    }

    .list-manage {
        padding: 30px 0;
    }

    .side-menu-inner {
        border: none;
        position: fixed;
        bottom: -1px;
        height: 77px;
        width: 100%;
        left: 0;
        right: 0;
        box-shadow: 0 -2px 7px 3px #0000002e;
        padding-top: 2px;
        z-index: 99;
        margin: 0;
        border-radius: 10px 10px 0 0;
    }

    .acc-inner ul li,
    header .search-reservation {
        align-items: center;
    }

    .chat-inner-2,
    .main-image {
        margin-bottom: 15px;
    }

    .property-detail .property-inner-detail .user {
        max-width: 100%;
    }

    .custom-subtotal {
        margin: 15px 0;
        padding: 5px 0;
    }

    .gst {
        margin: 10px 0 !important;
    }

    .property-detail .property-inner-detail .property-description {
        max-width: 100%;
        text-transform: capitalize;
    }

    .custom-small-modal-width,
    .listing-counter .minus,
    .plus,
    .product-category li {
        width: auto;
    }

    .px-2 {
        padding-right: 13px !important;
        padding-left: 13px !important;
    }

    .header-inner .fl-wr,
    .row.rv-mb {
        flex-wrap: wrap-reverse;
    }

    .prof-in {
        margin-bottom: 20px;
    }

    .pro-img,
    .title {
        margin-bottom: 10px;
    }

    .change-pro .pro-img {
        margin-bottom: 0;
    }

    section.Inbox {
        padding-top: 20px;
    }

    .change-pro a,
    .clndr-btn {
        text-decoration: none;
    }

    .ls-img.rs-ls-img img {
        width: 100%;
        height: 200px;
    }

    .title {
        margin-top: 10px;
    }

    ul.side-inner li a i {
        font-size: 22px;
        margin-right: 0;
        padding-bottom: 5px;
        display: block;
    }

    ul.side-inner li a span {
        display: block;
        line-height: 12px;
    }

    section.wishlist {
        padding: 30px 0 60px;
    }

    .list-manage {
        border: none !important;
    }

    .nt-time {
        text-align: end;
        font-size: 10px;
    }

    .no-booking {
        padding: 70px 0;
    }

    .fc .fc-toolbar-title {
        font-size: 1.5em;
        margin: 0;
    }

    .reservation {
        margin: 30px 0;
    }

    .list-descrip {
        font-size: 15px;
        margin-bottom: 10px !important;
    }

    .prev-btn {
        margin-right: 20px;
        float: left;
    }

    button.next-btn {
        float: right;
    }

    .ls-img img {
        width: 100%;
        height: 180px;
    }

    .fltr-rang,
    .list-hd h1,
    .ls-img,
    input#front-search-field {
        margin-bottom: 20px;
    }

    .mini-profile {
        justify-content: flex-start;
    }

    .loader-bg img.for-mobile.animate-flicker {
        width: 150px;
    }

    .md-mb-3 {
        margin-bottom: 2em;
    }

    .slick-slider {
        margin-bottom: 0 !important;
    }

    span.pagingInfo {
        padding: 2px 10px;
        position: absolute;
        right: 15px;
        bottom: 25px;
        background: #00000059;
        color: #fff;
        text-align: center;
        border-radius: 6px;
        font-size: 14px;
    }

    .header-inner,
    .pg-slide {
        position: relative;
    }

    .property-detail .property-gallery .inner-image {
        height: calc(250px - 8px);
    }

    .mb-slid {
        border-radius: 10px !important;
        overflow: hidden;
    }

    .property-detail .head-content {
        margin: 15px 0;
    }

    .listing-checkbox-wrapper.fsrch {
        width: 60px;
        height: 60px;
        margin: 0;
    }

    /* .listing-checkbox-content {
        font-size: 10px;
    } */

    .product-category-2 {
        display: flex;
        justify-content: space-around;
        align-items: center;
        margin: 30px 0;
    }

    .services .services-btn .filter-btn {
        justify-content: space-evenly;
    }

    .pagination {
        justify-content: center;
        text-align: center;
    }

    .cust-no-msg {
        height: 73vh !important;
    }

    .ls-item {
        border-top: none;
    }

    .account-detail .ls-item {
        border-top: 1px solid #d5d5d5b3;
    }

    .history-bk .ls-item {
        border-top: 1px solid #d5d5d5b3;
    }

    .mb-5 {
        margin-bottom: 1.5rem !important;
    }

    .social-signup-btn a {
        margin-right: 0;
    }

    .social-signup-btn button h6 {
        font-size: 9px;
    }

    .social-signup-btn p {
        line-height: 18px;
        font-size: 11px;
    }

    .popup-user {
        width: 100%;
        max-width: 100% !important;
        flex: 0 0 100% !important;
        justify-content: flex-start !important;
        margin-top: 10px;
    }

    .popup-font {
        text-align: left !important;
    }

    .custom-modal-body {
        padding: 25px;
    }

    .host-help .help-content li .content h5 {
        margin-bottom: 20px;
        color: #575757;
    }

    .host-help .help-content li {
        display: block;
        align-items: center;
        margin-bottom: 50px;
        text-align: center;
    }

    /* .host-help .content1 h1 {
      color: #f5c33e;
      font-weight: 500;
      text-align: center;
      text-transform: capitalize;
      margin-bottom: 25px;
    } */
    /* .new-listing .left-side {
      height: 51vh;
      background: var(--theme-primary);
      padding: 70px 40px 0;
    } */
    .new-listing {
        height: auto;
        overflow: auto;
    }

    .right-content {
        padding: 0;
        height: auto;
    }

    .new-listing .right-side {
        height: auto;
        padding: 60px 55px;
    }

    .listing-footer {
        height: 80px;
        position: fixed;
        left: 0;
        right: 0;
        width: 100%;
    }

    .alrt-position,
    .listing-map,
    header {
        position: relative;
    }

    .alrt-position {
        width: 100%;
        top: -10px;
        margin: 0 auto;
    }

    .listing-position {
        padding: 0 !important;
        height: 60vh;
        z-index: 9;
    }

    /* .filter-btn .custom-dropdown-menu {
      left: 0 !important;
    } */
    .listing-map {
        padding-bottom: 30px;
        height: calc(108vh - 64px);
    }

    .listing-checkbox-wrapper {
        width: calc(31% - 7px);
        height: 80px;
    }

    .listing-checkbox-icon {
        margin-bottom: 2px;
    }

    .listing-checkbox {
        width: calc(95% + 8px);
    }

    .listing-counter-main.pr-rs {
        height: 32vh;
    }

    header {
        z-index: 999;
    }

    header .popup-main .popup {
        padding: 16px;
        left: 5px !important;
        top: 36px;
    }

    .popup.search-location-popup {
        width: 97% !important;
    }

    .blkd {
        top: 80px;
    }

    header .popup-main .popup.guest-popup {
        width: 87%;
        padding: 20px 30px !important;
        left: 23px !important;
        position: relative;
    }

    .daterangepicker .calendar-table td,
    .daterangepicker .calendar-table th {
        width: 37px;
        height: 37px;
        min-width: 37px;
    }

    .daterangepicker .drp-calendar {
        margin: 0 auto;
    }

    .daterangepicker .drp-calendar.left {
        margin-right: auto;
        padding: 8px 0;
    }

    header .nav .nav-menu .user-profile {
        margin-right: 5px;
    }

    .property-gallery .popup-main-image {
        height: 220px;
        margin-bottom: 15px;
    }

    .modal-body.custom-modal-body .slick-slide img {
        display: block;
        height: 190px;
    }

    .listing-counter input {
        font-size: 27px;
        line-height: 1.5;
        height: 48px;
    }

    .fl-on-mb {
        position: absolute;
        top: 11px;
        left: -6px;
    }

    header .nav {
        margin-bottom: 11px;
    }

    .wkText {
        font-size: 11px;
        margin-bottom: 0;
    }

    .calender_box {
        height: 75px;
    }

    header .search-reservation p {
        font-weight: 700;
    }

    button.host.header-btn a {
        background: #f5c33e;
        color: #ffff;
        padding: 5px 10px;
        border-radius: 5px;
        margin-right: 9px;
    }

    .home-content {
        padding-top: 45px;
    }


    .fl-mob img {
        width: 75%;
    }

    .home-cont h1,
    .list-hd h1 {
        font-size: 24px;
    }

    .notfctn-user-detail p {
        width: auto;
    }

    .upload__btn {
        padding: 12px 13px;
    }

    .btn-mini {
        height: 40px;
        padding: 8px 10px;
    }

    .tb-btn-sc input.cust-btn.theme-btn {
        width: 100%;
    }

    .caution {
        margin-bottom: 30px;
    }

    h3.edit-place-name {
        margin-top: 20px;
    }

    .promo-not-avail {
        height: 40vh;
        padding-top: 30px;
    }

    .receipt-card .col-3 {
        width: 50%;
        margin-bottom: 24px;
    }

    .receipt-logo {
        width: 60%;
    }

    .report-wrap {
        bottom: 20px;
        right: 25px;
    }

    .report-panel {
        width: 340px;
        bottom: 55px;
    }

    .report-badge img {
        top: 14px;
    }

    .whatsapp-icon {
        bottom: 30px;
    }

    .pc-sec-inner {
        padding: 0px 20px;
        height: 230px;
    }
}

@media (max-width: 540px) {
    .right-content .hj-nonarabic-mt {
        margin-top: 10px;
    }
}

@media (max-width: 430px) {

    header .search-reservation li span,
    header .search-reservation p {
        font-size: 9px;
    }

    button.submit-header {
        width: 30px;
        height: 30px;
        font-size: 12px;
    }

    header .nav .header-btn {
        padding-right: 3px;
        font-size: 11px;
        height: 16px;
    }

    header .search-reservation li {
        padding: 0 5px;
        width: 40%;
    }

    .property-detail .property-inner-detail .user .cust-btn {
        padding: 8px 10px;
        font-size: 12px;
    }

    .user.dropdown {
        margin-left: 4px;
    }

    section.home-bg {
        height: 85vh;
    }

    .heade-date input {
        font-size: 8px;
        font-weight: 700;
        position: relative;
        top: -1px;
    }

    .inbox-property-img {
        flex: 0 0 17%;
    }

    .new-listing .ilq-chk .hs-ls-item h4 {
        font-size: 14px !important;
        margin-bottom: 0;
    }

    .new-listing label.ilq-chk {
        align-items: center;
    }
}

@media (max-width: 480px) {
    .ac-img img {
        width: 23px;
    }

    .total-guest {
        width: auto;
    }

    header .popup h3 {
        padding-left: 0;
    }

    .pop-img img {
        width: 75px !important;
        height: 75px !important;
    }

    .feature {
        margin-bottom: 50px;
        height: auto;
    }

    label.check-lb {
        display: block;
        padding-bottom: 0;
        padding-top: 0;
    }

    .head-miniheight {
        height: 13px;
    }

    .popup-inner-image.re-img,
    .social-signup-btn a button {
        width: 100%;
    }

    .listing-checkbox-wrapper.fsrch {
        width: 55px;
        height: 55px;
        margin: 0;
    }

    .listing-checkbox-icon {
        width: 20px;
        height: 20px;
    }

    .listing-checkbox-label {
        margin: 2px;
    }

    header .logo {
        width: 66px;
    }

    .heade-date input,
    header .search-reservation li span,
    header .search-reservation p {
        font-size: 10px;
    }

    .services .head h1 {
        font-size: 26px;
    }

    header .nav .header-btn {
        font-size: 10px;
    }

    .product-category li {
        padding: 6px 5px !important;
        margin: 4px;
    }

    .for-mobile.fl-mob img {
        width: 90%;
    }

    .user-detail {
        margin: 0 5px;
    }

    header .search-reservation li:nth-child(2) {
        width: 62%;
    }

    .daterangepicker .calendar-table td,
    .daterangepicker .calendar-table th {
        width: 30px;
        height: 30px;
        min-width: 30px;
        font-size: 11px;
        line-height: 20px;
    }

    .daterangepicker .drp-calendar.left {
        margin-right: auto !important;
        padding: 0;
    }

    .daterangepicker .calendar-table th,
    .heade-date .daterangepicker .calendar-table td {
        padding: 5px !important;
    }

    .daterangepicker.show-calendar .ranges {
        margin-top: 0;
    }

    .daterangepicker td.active,
    .daterangepicker td.active:hover,
    td.active.end-date.in-range.available {
        background: #000;
        border-radius: 48px !important;
        padding: 8px 5px !important;
    }

    .daterangepicker .drp-calendar {
        display: none;
        max-width: 100% !important;
    }

    .side-list {
        padding: 0 13px 0 0;
        height: 65vh;
    }

    .your-side-list {
        height: 50vh !important;
    }

    .ls-img {
        margin-bottom: 10px;
    }

    ul.help-content {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
    }

    ul.help-content li {
        width: 100%;
        border: 1px solid #e3e3e3;
        padding: 15px;
        border-radius: 10px;
        margin-bottom: 15px !important;
    }

    .host-help::before {
        display: none;
    }

    .start-host {
        text-align: center;
        margin-bottom: 30px;
        text-transform: capitalize;
    }

    .host-help .help-content li .content h5 {
        margin-bottom: 10px;
        font-size: 17px;
        font-weight: 500;
    }

    section.host-banner .row {
        flex-wrap: wrap-reverse;
    }

    .tabs-sc {
        padding-top: 30px;
    }

    .inbox-property-date span {
        font-size: 12px;
    }

    .fl-on-mb {
        top: 17px;
    }

    .listing-layout-one {
        flex-flow: wrap;
        flex-direction: column-reverse;
        padding-bottom: 20px;
        margin-bottom: 28px !important;
    }

    .ll-one-left {
        width: 100%;
    }

    .ll-one-right {
        width: 100%;
    }

    .list-right-img img {
        width: 100%;
        height: 150px;
        margin-bottom: 10px;
    }

    .ll-two-left {
        flex-flow: wrap;
    }

    .ll-tl-img {
        height: 150px;
        width: 100%;
    }
}

@media (max-width: 360px) {
    .feature-content .fea-btn a {
        display: block;
    }

    .feature-content .fea-btn a:last-child button {
        margin-left: 0;
    }

    .feature-content p {
        margin-bottom: 0;
    }
}

@media only screen and (min-device-width: 701px) and (max-device-width: 1023px) {
    .header-inner .fl-wr {
        justify-content: end;
    }

    .navmenu {
        right: -92px;
    }

    header .search-reservation li span {
        font-size: 14px;
    }

    .heade-date input,
    header .search-reservation p {
        font-size: 13px;
        font-weight: 700;
    }

    .host-help .help-content li img {
        margin-right: 20px;
        width: 38px;
        margin-top: 3px;
    }

    .host-help .help-content li .content h5 {
        margin-bottom: 10px;
        font-size: 17px;
        font-weight: 500;
    }

    .start-host {
        text-align: center;
        margin-bottom: 30px;
        text-transform: capitalize;
        margin-top: 20px;
    }

    ul.help-content {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
    }

    ul.help-content li {
        width: 49%;
        border: 1px solid #e3e3e3;
        padding: 15px;
        border-radius: 10px;
        margin-bottom: 15px !important;
    }

    section.home-bg {
        height: 55vh;
    }

    .for-mobile,
    .for-mobile.fl-mob,
    .host-help::before {
        display: none;
    }

    img.fl-7 {
        bottom: 31%;
    }

    img.fl-2 {
        bottom: 42%;
    }

    .header-inner .fl-wr,
    .row.rv-mb {
        flex-wrap: wrap-reverse;
    }

    .host-sc,
    .pg-slide,
    .view-sc {
        position: relative;
    }

    header .search-reservation {
        margin-top: 18px;
        margin-bottom: 6px;
    }

    /* .nav-header {
      height: 125px;
      z-index: 99;
    } */
    .host-banner,
    .reservation-left,
    .side-list,
    section.list {
        height: auto;
    }

    header .popup-main .search-location-popup {
        top: 47px !important;
        left: 5%;
    }

    header .popup h3 {
        padding-left: 0;
    }

    .product-category {
        justify-content: center;
        padding-top: 10px;
    }

    img.fl-5 {
        right: 33%;
        top: 44%;
        z-index: 8;
    }

    .feature .bg .d img {
        right: 10px;
    }

    .daterangepicker {
        left: 5% !important;
    }

    .guest-popup {
        right: 5%;
        top: 47px !important;
    }

    .host-sc {
        right: 17px;
    }

    .host-help {
        padding: 30px 0 0;
        margin-top: 0;
    }

    .theme-btn {
        padding: 6px 20px;
    }

    .v-filter {
        position: absolute;
        position: inherit;
        right: 0;
        top: 0;
    }

    .view-sc .product-category-2 {
        justify-content: center;
        justify-content: center;
        margin-top: -20px;
    }

    .property-detail .property-gallery .inner-image {
        height: calc(150px - 8px);
    }

    .property .product .image .product-img {
        height: 200px;
    }

    #staticBackdrop .modal-dialog {
        width: 70%;
    }

    span.pagingInfo {
        padding: 2px 10px;
        position: absolute;
        right: 15px;
        bottom: 25px;
        background: #00000059;
        color: #fff;
        text-align: center;
        border-radius: 6px;
        font-size: 14px;
    }

    .property-detail .property-gallery .inner-image img {
        margin-bottom: 0;
    }

    .slick-slider {
        margin-bottom: 10px !important;
    }

    .loader-bg img.for-mobile.animate-flicker {
        width: 150px;
    }

    .animate-flicker {
        animation: 0.8s infinite alternate fadeIn;
    }

    .property-pricing {
        margin: 10px 50px;
    }

    .slick-slide img {
        width: 100%;
    }

    .property-description {
        margin-bottom: 15px;
    }

    .user-detail {
        margin: 0;
    }

    .user-detail h6,
    .user-detail p {
        text-align: left !important;
    }

    .property-detail .property-inner-detail .popup-user {
        justify-content: start;
    }

    .popup-check {
        position: relative;
        margin: 25px 0 35px;
        background: #f3f3f3;
        padding: 20px;
        border-radius: 10px;
    }

    .property-detail .property-inner-detail .user img {
        width: 60px;
        margin-right: 10px;
    }

    .user-detail {
        margin-right: 15px;
    }

    .modal-dialog {
        max-width: 80% !important;
    }

    .property-pricing {
        padding: 30px;
    }

    .listing-checkbox-wrapper.fsrch {
        width: 65px;
        height: 65px;
    }

    .services .head h1 {
        font-size: 23px;
    }

    .listing-checkbox-label {
        margin-top: 0;
    }

    .listing-checkbox-content {
        font-size: 10px;
    }

    .side-list {
        padding: 0 15px 0 0;
    }

    .list-manage {
        padding: 30px 0 20px;
    }

    .list-hd {
        padding: 30px 0 0;
    }

    img.verified {
        bottom: -7px;
        right: 52px;
    }

    .slide-btn {
        display: initial;
    }

    section.reservation li.nav-item {
        width: 48%;
    }

    section.reservation li.nav-item button {
        width: 100%;
        border: 1px solid #f4f4f4;
        font-size: 23px;
        height: 56px;
        font-weight: 500;
    }

    .tabs-sc {
        padding-top: 34px;
        border-top: 1px solid #ede6e6;
        margin-top: 30px;
    }

    .reservation .nav-pills {
        justify-content: space-between;
    }

    .reserv-check {
        padding: 15px 20px;
    }

    .rs-ls-img img {
        width: 100%;
        height: 85px;
        object-fit: cover;
    }

    .reserv-pricing {
        padding: 20px;
    }

    .prof-in {
        margin-bottom: 30px;
    }

    .change-pro {
        margin-right: 20px;
    }

    .list-manage {
        /* height: 70vh;
      overflow-x: hidden;
      overflow-y: auto; */
    }

    .your-side-list {
        height: 55vh !important;
    }

    .ls-img img {
        height: 70px;
    }

    .title .product-rate {
        font-size: 12px;
    }

    label.listed {
        font-size: 14px;
        margin-right: 5px;
    }

    .wait-approval {
        font-size: 9px;
    }

    .ls-item {
        border-bottom: 1px solid #d5d5d5b3 !important;
    }

    .cm-img img {
        height: 120px;
    }

    .cm-list-item {
        height: 60vh;
    }

    .guest-list-item {
        height: 57vh;
    }

    .account-side {
        height: 64vh;
    }

    .p-2 {
        padding: 1.5rem !important;
    }

    .chat-profile-list {
        padding-right: 0;
    }
}

@media only screen and (max-device-width: 1500px) {
    .tb-scroll {
        overflow-y: auto;
        max-height: 41vh !important;
    }

    .your-side-list,
    .side-list {
        height: 70vh !important;
        padding-bottom: 20px;
    }

    .tb-scroll td {
        font-size: 13px !important;
    }

    .h-auto-md {
        height: auto !important;
        padding-bottom: 20px;
    }

    a.edit-user-role {
        margin-left: 6px;
    }

    .price-range-sm {
        padding: 0px !important;

    }

    .listing-counter-main {
        height: 100%;

        padding-top: 20px;
    }

    .pb-30 {
        padding-bottom: 30px !important;
    }



}

@media (max-width:700px) {

    .browse-language {

        width: 140px;
        margin: 0px auto;
    }

    .browse-language a {
        display: flex;
        align-items: center;
    }

    .br-cont span {
        color: gray;
        font-size: 11px;
    }

    .br-cont h5 {
        font-size: 15px;
        color: #f5c33e;
        margin: 0;
    }

    .br-cont {
        padding-left: 10px;
    }

    a.language_footer.ar {
        display: none;
    }

    .br-cont h5 {
        line-height: 10px;
        margin-bottom: 7px;
    }

    .new-listing.step-one .cont-listing {
        height: auto !important;
    }

    .listing-group {
        width: 60%;
    }

    section.new-listing.slt-plc {
        display: block;
    }

    .nm-sc {
        width: 100%;
    }

    .listing-checkbox-main .host-btn-black {
        display: flex;
        align-items: center;
        padding: 0;
        justify-content: center;
    }

    .listing-counter-main {
        padding-left: 15px;
        padding-right: 15px;
    }

}

/** wallet pay button**/
.ch-wallet-pay {
    margin-top: 35px;
    position: relative;
}

.ch-wallet-pay::before {
    content: "";
    position: absolute;
    top: -19px;
    left: 0;
    width: 100%;
    border-top: 1px solid #d1d1d1;
}

.ch-wallet-pay label {
    position: relative;
    display: block;
    width: 40px;
    height: 22px;
    background: transparent;
    border-radius: 30px;
    transition: all 300ms linear;
    transition: all 300ms linear;
    border: 1px solid #666;
    margin: 0 10px 0 0 !important;
}

.ch-wallet-pay label::before {
    position: relative;
    content: "";
    width: 15px;
    height: 15px;
    background: #666;
    display: block;
    border-radius: 50%;
    top: 3px;
    left: 4px;
    transition: all 300ms linear;
}

.ch-wallet-pay input:checked+label {
    border-color: var(--theme-primary);
}

.ch-wallet-pay input:checked+label::before {
    left: 19px;
    background-color: var(--theme-primary);
}

.ch-wallet-pay input {
    display: none;
}

.iqama-support-contact a {
    color: #25d366;
    position: relative;
    font-weight: 500;
    margin-left: 2px;
    transition: .3s all;
    font-size: 14px;
}

.iqama-support-contact a i {
    font-size: 17px;
}

.iqama-support-contact a::before {
    position: absolute;
    content: "";
    bottom: 3px;
    left: 0;
    width: 100%;
    transition: .3s all;
    border-bottom: 2px solid #25d366;
}

.iqama-support-contact a:hover::before {
    border-color: #000000;
}

/* annoucement page */
.annoucement {
    padding: 50px 0;
}

.annoucement p {
    color: #000;
}

/* hyper pay section*/
.hyper-pay-sec .pc-sec-inner {
    height: max-content;
    padding: 40px 40px 5px;
}

.hyper-pay-sec .pc-sec-inner .logo-two {
    margin-bottom: 30px;
}

/** question page modal **/
.permit {
    position: relative;
    flex-direction: column;
    padding-bottom: 20px;
    text-align: center;
    min-height: calc(100vh - 200px);
    display: flex;
    align-items: center;
    justify-content: center;
}

.permit i {
    font-size: 75px;
}

.permit h4 {
    margin: 40px 0 10px;
}

.permit p {
    font-size: 18px;
    margin-bottom: 15px;
}

.permit input::file-selector-button {
    border-radius: 12px;
    border: none;
}

.permit input {
    padding-left: 18px !important;
}

.list-skip-btn {
    margin-right: 5px;
}

@media (max-width: 490px) {
    .permit i {
        font-size: 60px;
    }

    .permit h4 {
        font-size: 18px;
    }
}

.iqama-support-contact a:hover::before {
    border-color: #000000;
}

/* annoucement page */
.annoucement {
    padding: 50px 0;
}

.annoucement p {
    color: #000;
}

.annoucement .main-title h1 {
    font-weight: 700;
    /* text-align: center; */
    font-size: 40px;
}

.ancmnt-banner-main {
    margin: 30px 0;
}

.annoucement .ancmnt-banner {
    height: auto;
    object-fit: cover;
    width: 100%;
}

.ancmnt-banner-main {
    margin: 30px 0;
}

.annoucement .ancmnt-banner {
    /* height: 100vh; */
    object-fit: cover;
    width: 100%;
}

.ancmnt-banner-main p {
    font-weight: 500;
    margin-top: 15px;
}

.ancmnt-content p {
    font-size: 17px;
    margin-bottom: 25px;
}


.ancmnt-content a {
    color: var(--theme-primary);
    text-decoration: underline;
}

.dots {
    width: 56px;
    height: 26.9px;
    background: radial-gradient(circle closest-side, #f5c33e 90%, #0000) 0% 50%,
        radial-gradient(circle closest-side, #f5c33e 90%, #0000) 50% 50%,
        radial-gradient(circle closest-side, #f5c33e 90%, #0000) 100% 50%;
    background-size: calc(100%/3) 13.4px;
    background-repeat: no-repeat;
    animation: dots-7ar3yq 1s infinite linear;
}

@keyframes dots-7ar3yq {
    20% {
        background-position: 0% 0%, 50% 50%, 100% 50%;
    }

    40% {
        background-position: 0% 100%, 50% 0%, 100% 50%;
    }

    60% {
        background-position: 0% 50%, 50% 100%, 100% 0%;
    }

    80% {
        background-position: 0% 50%, 50% 50%, 100% 100%;
    }
}

.search-reservation li {
    width: 43% !important;
}

.search-reservation li:nth-child(2) {
    width: 43% !important;
}

.search-reservation li:last-child {
    width: 14% !important;
}

button.submit-header {
    line-height: 1 !important;
    padding-top: 25px !important;
}

.heade-date input {
    text-align: start !important;
}

/**** Property Vverify Modal ****/
/* .prop-verify-modal .modal-content{
    background-color: var(--theme-primary);
} */
.prop-verify-modal .alert-modal img {
    width: 70px;
}

.prop-verify-modal h3 {
    color: #181818;
}

.prop-verify-modal .alert-modal p {
    color: #181818;
    font-weight: 500;
    font-size: 16px;
}

.prop-verify-modal .host-btn-black {
    background: #181818;
    padding: 10px 20px;
    font-size: 16px;
    display: block;
    width: max-content !important;
    margin: 0 auto;
}

.prop-verify-modal .host-btn-black:hover {
    color: #fff;
}

/**** host license css ****/
.mb-license-option-select {
    display: flex;
    align-items: center;
    justify-content: center;
}

.mb-license-option {
    padding: 0 5px;
}

/**** instruction page ****/
/* .instruction{
    margin: 50px 0 70px;
  } */
.instruction .prg-main-title {
    font-size: 36px;
    font-weight: 700;
    color: #5F5F5F;
    margin-bottom: 20px;
}

.instruction .card-container {
    display: flex;
    gap: 30px;
    flex-wrap: wrap;
    justify-content: center;
}

.instruction .card {
    background-color: #fff;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    flex: 1;
    max-width: 50%;
    transition: all 0.3s ease;
}

.instruction .card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
}

.instruction .card h2 {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 20px;
    color: #5F5F5F;
    position: relative;
}

.instruction .card h2:before {
    content: "";
    position: absolute;
    display: block;
    width: 60px;
    height: 4px;
    background-color: var(--theme-primary);
    bottom: -4px;
}

.instruction .steps {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.instruction .step {
    display: flex;
    align-items: center;
    gap: 15px;
    background-color: #f9f9f9;
    padding: 12px;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.instruction .step-number {
    background-color: #5F5F5F;
    color: #fff;
    font-weight: 700;
    font-size: 18px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.instruction .step p {
    margin: 0;
    font-size: 16px;
    color: #666;
    flex: 1;
}

.instruction a {
    color: var(--theme-primary);
    font-weight: 600;
    text-decoration: none;
}

.instruction a:hover {
    color: #ff8a00;
}
.instruction-tabing.nav-pills{
    margin-right: 10px;
}
.instruction-tabing.nav-pills .nav-link.active, .nav-pills .show>.nav-link{
    background-color: #181818;
    color: #fff;
    border-color: #E7E7E7;
}
.instruction-tabing.nav-pills .nav-link{
    background-color: #F7F7F7;
    border: 1px solid #E7E7E7;
    color: #181818;
    padding: 12px 15px;
}
.instruction-tabing.nav-pills .nav-item:first-child .nav-link{
    border-radius: 25px 0 0 25px;
    border-right: unset;
}
.instruction-tabing.nav-pills .nav-item:last-child .nav-link{
    border-radius: 0 25px 25px 0;
    border-left: unset;
}
.instruct-reg-prop-sec .instruct-list.mob-instruct-list li img{
    width: inherit;
    height: 400px;
    border: unset;
    border-radius: 0;
}
.instruc-help-sec .hst-bbg-btn{
    display: flex;
    align-items: center;
    gap: 7px;
}
.instruc-help-sec:first-child .hst-bbg-btn img{
    width: 30px;
}
.instruc-help-sec .hst-bbg-btn img{
    width: 25px;
}
/* Registered Properties Card: Set background to light yellow */
.instruction .registered {
    background-color: #f0f0f0;
}

.host-lang-btn .header-btn {
    color: var(--theme-middle) !important;
}

/*** license modal listing journey ****/
.license-toggle-switch {
    display: flex;
    position: relative;
    width: 200px;
    background-color: #f0f0f0;
    border-radius: 8px;
    overflow: hidden;
    margin: 0 auto;
}

.license-toggle-switch input[type="radio"] {
    display: none;
}

.license-toggle-switch label {
    flex: 1;
    padding: 10px;
    text-align: center;
    cursor: pointer;
    font-weight: bold;
    z-index: 1;
}

.license-toggle-indicator {
    position: absolute;
    top: 0;
    bottom: 0;
    width: 50%;
    background-color: black;
    transition: transform 0.3s ease;
    z-index: 0;
}

.license-toggle-switch input#company:checked~.license-toggle-indicator {
    transform: translateX(0%);
}

.license-toggle-switch input#individual:checked~.license-toggle-indicator {
    transform: translateX(100%);
}

.license-toggle-switch label#company {
    color: white;
}

.license-toggle-switch input#company:checked+label,
.license-toggle-switch input#individual:checked+label {
    color: white;
}

.license-toggle-switch input#company:checked+label+label {
    color: black;
}

.license-datepicker {
    padding: 0 !important;
    border-radius: 12px !important;
}
.license-datepicker.daterangepicker .drp-calendar.left{
    padding: 8px 0 8px 8px !important;
}
.license-datepicker .drp-buttons {
    display: none !important;
}

.license-datepicker.daterangepicker .calendar-table td, .license-datepicker.daterangepicker .calendar-table th {
    width: 32px !important;
    height: 32px !important;
}

.license-datepicker.daterangepicker td.active, .license-datepicker.daterangepicker td.active:hover {
    border-radius: 50% !important;
}

/**** customer support ****/
.support-chat-main {
    position: fixed;
    bottom: 90px;
    right: 29px;
    z-index: 9999;
}

.sc-button {
    width: 50px;
    height: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    background-color: #ffffff;
    border: 1px solid #ebebeb;
    box-shadow: 1px 1px 13px 3px #0000001c;
    z-index: 100;
    position: relative;
}
.cs-counter{
    position: absolute;
    background-color: red;
    color: #fff;
    top: 0;
    right: 0;
    border-radius: 50%;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    animation: 2s infinite pulse-animation;
}

.sc-button i {
    font-size: 22px;
}

.support-chat {
    position: absolute;
    width: 410px;
    background: #fff;
    right: 0;
    bottom: 0;
    height: 490px;
    box-shadow: 0px 5px 15px 1px #ccc;
    padding: 20px 10px;
    border-radius: 12px;
}

.d-agent {
    display: flex;
    align-items: center;
    border-bottom: 1px solid #e1e1e1;
    padding-bottom: 15px;
    margin-bottom: 18px;
}

.d-agent-img {
    width: 45px;
    height: 45px;
    background: var(--theme-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    margin-right: 15px;
}

.d-agent-img img {
    width: 50%;
    margin-left: 6px;
}

.d-agent-content {
    flex: 1;
}

.d-agent-content h4 {
    font-size: 18px;
}

.d-agent-content p {
    line-height: 1.1;
    font-size: 14px;
}

.sc-close-button {
    background-color: transparent;
    border: none;
    font-size: 24px;
    margin-right: 8px;
}

.cs-chat-main {
    overflow-y: auto;
    padding-right: 5px;
    height: 304px;
}

.cs-chat-main .chat-date {
    margin-top: 2px;
}

.cs-chat-main .chat-pop P {
    margin-bottom: 0;
}

.cs-chat-field {
    width: 100%;
    padding: 10px 0;
    background: #fff;
}

.cs-chat-field input {
    width: 100%;
    height: 45px;
    position: relative;
    padding: 12px 75px 12px 12px;
    margin-right: 0;
    border-radius: 25px;
}

.cs-chat-field input::placeholder {
    font-size: 12px;
}

.cs-cf-btn {
    position: absolute;
    bottom: 26px;
    right: 15px;
}

.cs-cf-btn button {
    display: inline-block;
}

.cs-cf-btn .send-btn {
    width: 35px;
    height: 35px;
}

.cs-cf-btn .send-btn img {
    width: 15px;
}

.cs-chat-main::-webkit-scrollbar-thumb {
    background-color: #eaeaea;
    border-radius: 25px;
}

.cs-chat-main::-webkit-scrollbar-track {
    border-radius: 0;
    background-color: transparent;
    box-shadow: none;
    border-radius: none;
}
.support-chat-main .cs-chat-main img{
    width: 260px;
    height: 150px;
    object-fit: cover;
    border-radius: 8px;
}
#images-box {
    display: flex;
    align-items: center;
    flex-flow: wrap;
    width: 100%;
    margin-bottom: 5px;
}

#images-box .img-box {
    width: 60px;
    height: 50px;
    margin-right: 10px;
    position: relative;
}

#images-box .img-box::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    background: linear-gradient(180deg, rgba(0, 0, 0, 0.4) 0, transparent);
    border-radius: 5px;
}

#images-box .img-box img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 5px;
}

#images-box .img-box .cross {
    position: absolute;
    top: 4px;
    right: 4px;
    background: #fff;
    width: 14px;
    height: 14px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

#images-box .img-box .cross img {
    width: 8px;
    height: auto;
}
/**** property license alert ****/
.property-license-alert{
    background-color: #FCB8333D;
    padding: 20px 0;
    margin-top: 10px;
}
.property-license-alert img{
    width: 50px;
}
.property-license-alert h5{
    color: #181818;
    font-weight: 700;
    font-size: 22px;
}
.property-license-alert p{
    color: #181818;
}
.property-license-alert p a{
    font-weight: 500;
}

/**** property license modal ****/
.actvate-property-content{
    padding: 0 10px;
}
.ap-content-head{
    margin-bottom: 20px;
}
.ap-content-list li a{
    display: flex;
    align-items: start;
    gap: 20px;
    border: 2px solid #E7E7E7;
    padding: 10px;
    border-radius: 8px;
    margin-bottom: 10px;
}
.ap-content-list li:last-child a{
    margin-bottom: 0;
}
.ap-content-list li a img{
    width: 40px;
    height: 40px;
}
.ap-content-list li a .ap-cl-content h5{
    margin: 5px 0;
}
.ap-content-list li a .ap-cl-content P{
    line-height: 1.1;
}

/**** all transaction *****/
.all-transaction {
    padding: 50px 0;
}

.al-title {
    font-size: 1.8rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    text-align: center;
}

.transaction-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    margin-bottom: 12px;
    border-radius: 8px;
    background-color: #f9fafb;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.transaction-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08);
}

.transaction-details {
    display: flex;
    flex-direction: column;
}

.transaction-title {
    font-weight: 500;
    font-size: 1rem;
    color: #333;
}

.transaction-date {
    font-size: 0.85rem;
    color: #888;
    margin-top: 2px;
}

.transaction-amount {
    font-weight: 600;
    font-size: 1rem;
    display: flex;
    align-items: end;
    gap: 5px;
    flex-direction: column-reverse;
}
.badge {
    font-size: 12px;
    font-weight: 500;
    padding: 4px 8px;
    border-radius: 4px;
    color: #fff;
}

.credit {
    background-color: #27ae60;
}

.debit {
    background-color: #e74c3c;
}
/* .not-available-tag {
    line-height: 1.3;
    font-size: 16px;
    margin: 5px 0;
    overflow: hidden;
    color: #323232;
    font-weight: 500;
    position: relative;
    padding-left: 14px;
}
.not-available-tag::before{
    content: "";
    position: absolute;
    background-color: #ff4d4f;
    width: 11px;
    height: 11px;
    border-radius: 50%;
    top: 44%;
    left: 0;
    transform: translateY(-50%);
}
.not-available-main{
    text-align: center;
}
.not-available-main .not-available-tag{
    padding-left: 17px;
    width: max-content;
    margin: 5px auto 1px;
    font-size: 15px;
}
.not-available-main .not-available-tag::before{
    width: 13px;
    height: 13px;
}
.not-available-main .not-available-date{
    color: #000000;
} */

.not-available-tag {
    display: flex;
    align-items: center;
    background: #fff;
    color: #323232;
    font-size: 12px;
    font-weight: 600;
    padding: 4px 10px 3px;
    border-radius: 25px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    white-space: nowrap;
    position: absolute;
    bottom: 5px;
    left: 5px;
    z-index: 99;
    width: max-content;
    margin-bottom: 0;
}

.not-available-tag::before {
    content: "\f05e";
    margin-right: 4px;
    font-size: 16px;
    font-family: 'Font Awesome 5 Free';
    color: #ff4d4f;
}
.not-available-detail-tag-main {
    text-align: center;
    margin: 15px 0;
}

.not-available-detail-tag {
    display: inline-flex;
    align-items: center;
    background-color: #fff;
    color: #ff4d4f;
    font-size: 14px;
    font-weight: 500;
    padding: 7px 15px;
    border-radius: 25px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    border: 1px solid #ff4d4f;
    white-space: nowrap;
}

.not-available-detail-tag::before {
    content: "\f05e";
    font-size: 16px;
    margin-right: 8px;
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
}

.not-available-detail-date {
    color: #4a4a4a;
    font-size: 13px;
    font-weight: 400;
    margin-top: 5px;
}
.display-license-num{
    display: flex;
    align-items: center;
    gap: 5px;
    margin: 7px 0 0 0;
    font-size: 17px;
}
.display-license-num span{
    font-weight: 600;
}
.display-license-num img{
    width: 18px;
    height: 18px;;
}
@media (max-width: 768px) {
    .instruction .card-container {
        flex-direction: column;
    }
}

/* inprogress property page  */
.inprogress-listing {
    margin-top: 20px;
}

.inprogress-listing h3 {
    margin-top: 20px;
    color: #333;
}

.listing-item-main{
    max-height: 285px;
    overflow-y: auto;
    padding: 0 10px 0 0;
}

.inprogress-listing .listing-item {
    display: flex;
    align-items: center;
    padding: 15px;
    margin-bottom: 10px;
    border: 2px solid #ddd;
    gap: 15px;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.inprogress-listing .listing-item:hover {
    background-color: #f7f7f7;
    border-color: #333;
}

.inprogress-listing .listing-item img {
    width: 50px;
    height: 50px;
    border-radius: 8px;
    object-fit: cover;
}

.inprogress-listing .listing-item .info {
    flex: 1;
}

.inprogress-listing .listing-item .info h4 {
    margin: 0;
    font-size: 16px;
    color: #333;
}

.inprogress-listing .listing-item .info p {
    margin: 5px 0 0;
    font-size: 14px;
    color: #666;
}
.create-new-main{
    margin-top: 20px;
}
.create-new-main h3{
    color: #333;
}
.create-new {
    display: flex;
    align-items: center;
    padding: 15px;
    border: 2px solid transparent;
    gap: 15px;
    border-radius: 8px;
    cursor: pointer;
    transition: border-color 0.2s ease;
}

.create-new:hover {
    border-color: #333;
}

.create-new img {
    width: 35px;
} 
.create-new-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex: 1;
}
.create-new-info span {
    font-size: 18px;
    color: #333;
    font-weight: 500;
}
.create-new-info i{
    color: #494949;
    font-size: 30px;
}
.fixed-content.new-listing{
    height: max-content;
    padding: 50px 0;
}
.no-wishlist-content{
    height: 70vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 30px;
}
.no-wishlist-content a{
    text-decoration: underline;
    font-weight: 500;
    font-size: 18px;
}
.iti__search-input{
    display: none;
}
.iti--inline-dropdown .iti__dropdown-content{
    border: 1px solid #E2E2E2 !important;
    width: 250px !important;
}
.rb-fillter-inner h5{
    font-size: 18px;
    margin-bottom: 5px !important;
}
.rb-fi-rating{
    display: flex;
    flex-direction: column;
    padding-left: 15px;
}
.rb-fi-rating .form-check .form-check-input{
    cursor: pointer;
} 
.rb-fi-rating .form-check .form-check-label{
    cursor: pointer;
}
@media (max-width: 1440px){
    .no-wishlist-content{
        height: 60vh;
    }
    .no-wishlist-content img{
        width: 400px;
    }
}
.mw-discount-property{
    background-color: #fff3cd;
    padding: 8px;
    font-size: 12px;
    font-weight: 500;
    color: #ffc107;
    display: inline-block;
    text-align: center;
    position: absolute;
    top: 0;
    left: 0;
    border-radius: 8px 0 8px 0;
    padding: 12px 15px;
    font-weight: 700;
    z-index: 99;
}

.mw-discount{
    background-color: #fff3cd;
    border-left: 5px solid #ffc107;
    padding: 8px;
    font-size: 14px;
    font-weight: 500;
    color: #000;
    display: inline-block;
    text-align: center;
    border-radius: 4px;
    width: 100%;
}

.iti__search-input{
    display: none;
}
.iti--inline-dropdown .iti__dropdown-content{
    border: 1px solid #E2E2E2 !important;
    width: 250px !important;
}
.listing-footer-btn .black-btn img{
    display: none;
}
#licenseToast .toast-body i{
    color: #ffb02e;
}
.lf-rv-image-ar,
.rt-rv-img-ar{
    display: none;
}
.cm-bd-content .btn:disabled{
    background-color: var(--theme-primary);
    border-color: var(--theme-primary);
}
#per_night_price {
    color: #000000 !important;
}

/* landing page css */
.landing-page-sec {
    direction: rtl;
}
.landing-page-sec .container{
    width: 100%;
    max-width: 500px;
    margin: 0 auto;
    height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    align-items: center;
}
.lp-body{
    text-align: center;
}
.lp-body .lp-body-content{
    margin: 30px 0;  
}
.lp-body .lp-body-content h1{
    font-size: 28px;
    font-weight: 700;
}
.lp-body .lp-body-content p{
    font-size: 16px;
    color: #000000;
    margin-top: 20px;
}
.lp-body-banner-img{
    width: 100%;
    height: auto;
    max-height: 280px;
    object-fit: contain;
}
.lp-card{
    margin-top: 25px;
    position: relative;
}
.lp-card img{
    width: 100%;
}
.lp-card-inner{
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    transform: translate(-50%, -50%);
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
}
.lp-card h1{
    color: #ffffff;
    font-size: 16px;
    font-weight: 400;
    line-height: 1.6;
}
.lp-card .lp-card-amount{
    color: var(--theme-primary);
    font-weight: 700;
}
.lp-card .lp-card-city{
    font-weight: 700;
}
.lp-card-btn{
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 20px;
    gap: 15px;
}
.lp-card-btn button{
    font-weight: 700;  
    font-size: 14px;
}
.lp-card-themeb{
    color: #000000;
}
.lp-card-transb{
    border: none;
}
.lp-card-transb a:hover{
    color: var(--theme-primary) !important;
}
.lp-card-transb a{
    color: #ffffff;
}
.lp-footer-logos {
    display: flex;
    justify-content: center;
    gap: 25px;
}

.lp-footer-logos img {
    height: 45px;
    width: auto;
    object-fit: contain;
}

/* checkout page */
.checkout-page{
    padding: 40px 0;
}
.checkout-detail{
    padding: 20px;
    border-radius: 25px;
    box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
}
.checkout-detail-title {
    margin-bottom: 10px;
}
.checkout-payment .prop-payment-option{
    display: block;
}
.checkout-payment .prop-payment-option .pay-sec-label{
    width: 100%;
    margin-right: 0 !important;
}
.wallet-payment-option {
    border: 1px solid var(--grey-one);
    border-radius: 10px;
    padding: 15px;
    background-color: #fff;
    transition: all 0.3s;
}
.wallet-payment-option.checked.checked {
    border-color: #4CAF50;
    background-color: #f8fff8;
}
.wallet-payment-option .form-check {
    display: flex;
    align-items: flex-start;
    gap: 10px;
}
.wallet-payment-option .form-check-input {
    margin-top: 0;
}
.wallet-payment-option .form-check-label {
    flex: 1;
    cursor: pointer;
}
.wallet-payment-option .form-check-label strong {
    font-size: 16px;
    display: block;
    margin-bottom: 5px;
}
.wallet-balance {
    font-size: 14px;
}
.wallet-amount {
    color: #4CAF50;
    font-weight: 600;
}
.cd-property-detail{
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
}
.cd-property-detail img {
    width: 160px;
    height: 115px;
    object-fit: cover;
    border-radius: 12px;
}
.cd-pd-content{
    flex: 1;
}
.cd-pd-content h5{
    font-weight: 700;
    margin-bottom: 5px;
}
.ct-content{
    padding: 0 7px;
}
.ct-content h6{
    color: #333;
}
.ct-content p{
    font-size: 14px;
}
.checkout-summary-inner{
    padding: 0 7px;
}
.checkout-summary table{
    margin-bottom: 0;

}
.checkout-summary tr,
.checkout-summary td,
.checkout-summary th{
    border: none;
}
.checkout-summary td{
    padding-top: 0;
}
.checkout-summary .summary-total th{
    padding-bottom: 0;
    border-top: 2px solid var(--grey-one);
    color: var(--dr-shade-gray);
}
.checkout-registeration .phone-number input{
    border: none;
}
.checkout-coupon-code button.pd-promo-btn{
    right: 0px;
    height: 43px;
}
/* property landing page */
.property-landing-header .header-inner{
    padding: 20px 0;
}
.landing-page-dropdown .menubtn{
    color: #000000 !important;
    margin-left: auto;
    padding: 5px 20px;
    display: flex;
    align-items: baseline;
    justify-content: center;
    width: max-content;
    border-radius: 12px;
    box-shadow: rgba(0, 0, 0, 0.1) 0px 10px 15px -3px, rgba(0, 0, 0, 0.05) 0px 2px 6px 4px;
}
.property-landing-sec{
    height: calc(100vh - 82px);
    margin-top: 81px;
    display: flex;
    flex-direction: column;
    gap: 22px;
}
.pl-sec-banner{
    padding: 50px 0;
    background-color: #FFFBF2;
    height: 80%;
}
.pl-sb-content h1{
    font-size: 40px;
    color: #000000;
    margin-bottom: 45px;
    position: relative;
    font-weight: 600;
}
.pl-sb-content h1 img{
    position: absolute;
    left: 0;
    bottom: -18px;
}
.pl-sb-content h1 span{
    font-weight: 400;
}
.pl-sb-content p{
    font-size: 18px;
    color: #000000;
    margin-bottom: 25px;
}
.pl-sb-content button{
    font-size: 14px;
    font-weight: 500;
    height: 38px;
}
.pl-sb-img{
    text-align: right;
}
.pl-sb-img img{
    width: 430px;
    height: 407px;
    margin-left: auto;
}
.pl-sec-footer{
    background-color: #FFFBF2;
    padding: 10px;
    border-radius: 16px;
}
.pl-sec-footer a{
    text-decoration: underline;
    color: #000000;
}
.pl-sf-img-content{
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 5px;
}
.pl-sf-img-content a{
    font-weight: 500;
}
.pl-sf-simple-content a{
    font-weight: 600;
}
/* property managed modal */
.property-management-modals .cm-simple-header{
    padding: 30px 25px 35px;
}
.property-management-modals .cm-simple-header .btn-close{
    right: 30px;
}
.property-management-modals .cm-simple-body {
    padding: 0 25px 25px;
}
.property-management-modals .modal-footer{
    padding: 0 25px 25px;
}
.bg-btn-close{
    border: none !important;
    background-color: #F8F8F8;
    font-size: 11px !important;
    padding: 11px !important;
}
.pm-modal-content p{
    font-size: 18px;
    color: #010205;
    line-height: 1.3;
}
.info-box {
    background-color: #F6F6F6;
    border-radius: 8px;
    padding: 10px;
}
.info-item {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 1rem;
}
.info-item img{
    height: 35px;
    width: 35px;
}
.info-item h5{
    margin-bottom: 0;
    font-size: 18px;
    font-weight: 500;
}
.info-item:last-child {
    margin-bottom: 0;
}
.modal-footer {
    justify-content: space-between;
}
.pm-modal-footer button{
    font-size: 14px;
}
.pm-mc-link-btn{
    text-decoration: none;
    padding: 8px 30px;
    font-weight: 500;
}
.pm-mc-theme-btn{
    font-weight: 600;
}
/* property management registration modal */
.pm-reg-field{
    margin-bottom: 10px;
}
.pm-reg-field label{
    margin-bottom: 6px;
    font-size: 14px;
}
.pm-reg-field .form-control{
    border-radius: 8px;
    height: 44px;
    padding: 0 14px;
}
.info-note {
    font-size: 0.9rem;
    color: #333;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 1rem;
}

.info-note i {
    color: #36BFFA;
}
.property-detail .property-gallery .main-image{
    position: relative;
}
.custom-tag{
    display: flex;
    align-items: center;
    font-weight: 600;
    padding: 9px 12px;
    border-radius: 25px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    white-space: nowrap;
    position: absolute;
    z-index: 99;
    width: max-content;
    line-height: 1;
}
.exclusive-tag{
    font-size: 15px;
    color: #323232;
    background: #fff3cd;
    top: 10px;
    left: 10px;
}
.pb-exclusive-tag{
    bottom: 5px;
    right: 5px;
    font-size: 14px;
    color: #323232;
    background: #fff3cd;
}
#whishlist-listing{
    z-index: 9999;
}
/* booking collapse */
.booking-accordion{
    margin-bottom: 10px;
}
.booking-accordion .booking-accordion-header{
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex: 1;
    padding-right: 10px;
}
.booking-accordion .accordion-button{
    padding: 16px 7px;
}
.accordion-header .accordion-button:focus{
    box-shadow: none;
}
.accordion-header .accordion-button:not(.collapsed){
    background-color: #FFFBF2;
    color: #000000;
}
.accordion-header .accordion-button:not(.collapsed)::after{
    background-image: var(--bs-accordion-btn-icon);
}
.booking-accordion .accordion-body{
    padding: 16px 10px;
    max-height: 168px;
    overflow: auto;
}
.booking-accordion .accordion-body .table{
    margin-bottom: 0;
}
.booking-accordion .accordion-body .table>:not(caption)>*>*{
    padding: .3rem .3rem;
    border: 0;
}
.booking-accordion .accordion-body .table-bordered>:not(caption)>*{
    border: none;
}
.single-head-btn{
    display: flex;
    align-items: center;
    justify-content: end;
    gap: 5px;
}
.single-share-btn button{
    display: flex;
    align-items: center;
    gap: 0px;
    padding: 7px 10px;
    border-radius: 8px;
    transition: .3s ease-in-out;
    font-size: 16px;
}
.single-share-btn button img{
    width: 24px;
}
.single-share-btn button:hover{
    background-color: #F7F7F7;
}
.single-share-property{
    display: flex;
    align-items: center;
    gap: 15px;
}
.single-share-property img{
    width: 64px;
    height: 64px;
    border-radius: 8px;
}
.ss-property-content{
    flex: 1;
}
.ss-property-content p{
    color: #000000;
}
.ss-property-btns{
    margin-top: 20px;
}
.ss-property-btns button{
    border: 1px solid var(--grey-one);
    border-radius: 12px;
    display: flex;
    align-items: center;
    gap: 15px;
    width: 100%;
    margin-bottom: 15px;
    padding: 15px;
    font-weight: 500;
    transition: .3s ease-in-out;
    color: #000000;
}
.ss-property-btns button:hover{
    background-color: #F7F7F7;
}
.ss-property-btns button i{
    font-size: 21px;
}
#twitter-share-btn img{
    width: 20px;
}
.single-wishlist-icon{
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 7px 10px;
    gap: 3px;
    border-radius: 12px;
    cursor: pointer;
}
.single-wishlist-icon:hover{
    background-color: #F7F7F7;
}
.single-wishlist-icon .fav-icon.inr{
    position: relative;
    top: unset;
    left: unset;
    width: max-content;
    right: unset;
    line-height: 1;
} 
.single-wishlist-icon .fav-in{
    top: unset;
    left: unset;
    right: unset;
    position: relative;
}
.fav-icon.inr .fav-in img{
    width: 23px;
}

/* new property card */
.new-property-card {
    overflow: hidden;
    background: #fff;
    border: 1px solid #6A6A6A33;
    border-radius: 16px;
    margin-bottom: 25px;
}

.new-property-card .card-img-top {
    border-radius: 0;
}

.new-property-card .np-image-container {
    position: relative;
}
.np-image-container .not-available-overlay{
    filter: grayscale(100%);
}

.new-property-card .np-image-container .np-property-image{
    height: 320px;
    width: 100%;
}

.new-property-card .np-image-container .np-property-image img {
    object-fit: cover;
    border-radius: 16px;
    height: 100%;
    width: 100%;
}
.new-property-card .np-badge-overlay {
    position: absolute;
    top: 10px;
    left: 10px;
    display: flex;
    z-index: 9;
    gap: 8px;
}

.new-property-card .np-badge {
    font-size: 12px;
    padding: 4px 8px;
    gap: 4px;
    display: flex;
    align-items: center;
    font-weight: 500;
    border-radius: 25px;
    line-height: 1;
    backdrop-filter: blur(8px);
}

.new-property-card .badge-view {
    background-color: #00000099;
    color: white;
    border: 1px solid #6A6A6A;
}

.new-property-card .badge-exclusive {
    background-color: #F5C33E99;
    color: #000;
    border: 1px solid #F5C33E;
}
.new-property-card .badge-exclusive img{
    width: 16px;
}
.new-property-card .badge-not-available {
    background-color: #FFFFFF80;
    color: #D1372E;
    border: 1px solid #ffffff;
}
.new-property-card .np-action-icons {
    position: absolute;
    top: 10px;
    right: 10px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    z-index: 9;
}

.new-property-card .np-action-icons .icon-btn {
    background-color: #D9D9D9CC;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.new-property-card .np-action-icons .icon-btn .fav-in,
.new-property-card .np-action-icons .icon-btn.fav-icon{
    line-height: 1;
    top: unset;
    left: unset;
    right: unset;
    z-index: unset;
    position: unset;
}
.new-property-card .np-action-icons .icon-btn .fav-in img{
    width: auto;
}

.property-features-badges{
    position: absolute;
    bottom: 10px;
    left: 10px;
    z-index: 9;
}
  
.property-features-badges .pf-badge{
    display: flex;
    align-items: center;
    gap: 3px;
    border-image-slice: 1;
    border-radius: 6px;
    padding: 2px 8px 2px 2px;
    font-size: 12px;
    font-weight: 700;
    line-height: 1;
}
.pf-badge-high-demand{
    background: #E86629;
}
.pf-badge-top-feature{
    background-color: #B3A5E9;
}
.new-property-card .np-card-body {
    padding: 20px 12px 12px 12px;
}

.new-property-card .np-features {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
}

.new-property-card .np-features span {
    display: flex;
    align-items: center;
    padding: 4px;
    border-radius: 4px;
    gap: 4px;
    font-size: 14px;
    background-color: #E9ECEF;
    color: #4F4F4F;
}

.new-property-card .np-card-title-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 16px;
}

.new-property-card .np-card-title {
    font-size: 16px;
    margin-bottom: 0;
}

.new-property-card .np-location {
    font-size: 12px;
    margin-bottom: 0;
    margin-top: 4px;
    display: flex;
    align-items: center;
    gap: 4px;
}
.new-property-card .np-location a{
    color: #777;
}
.new-property-card .np-location span {
    text-decoration: underline;
}

.new-property-card .np-rating {
    font-size: 12px;
    font-weight: 400;
    display: flex;
    gap: 3px;
    align-items: center;
}

.new-property-card .np-total-rating {
    font-weight: 700;
}

.new-property-card .np-prices {
    margin-top: 10px;
    display: flex;
    gap: 10px;
    font-size: 14px;
    color: #6A6A6A;
    justify-content: space-between;
}
.np-price-per-night{
    display: flex;
    align-items: center;
    gap: 4px;
}
.np-price-left{
    flex: 1;
}
.np-price-discount-per{
    display: flex;
    background: #6AB988;
    color: #0A3219;
    font-weight: 700;
    padding: 1px 4px 0 4px;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
}

.new-property-card .np-price-per-night .np-total-price {
    font-weight: 700;
    font-size: 16px;
    color: #000000;
}

.new-property-card .total-price-link {
    color: #6c757d;
    text-decoration: underline;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 2px;
    width: max-content;
}
.new-property-card .tp-link-amount {
    font-weight: 500;
}

/* Custom Popover Styling */
.price-details-popover {
    width: 480px !important;
    box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.12);
    border: none !important;
    --bs-popover-max-width: 480px !important;
    border-radius: 24px !important;
}

.price-details-popover .popover-header {
    display: none;
}
.price-details-popover .popover-header-custom {
    font-weight: 700;
    font-size: 18px;
    border-bottom: none;
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.price-details-popover .popover-body{
    padding: 40px 32px !important;
    position: relative;
}

.price-details-popover .popover-body-custom {
    padding: 0;
}

.price-details-popover .price-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    font-size: 16px;
    color: #4F4F4F;
}

.price-details-popover .price-row span:last-child {
    font-weight: 500;
    color: #000;
}

.price-details-popover .price-row .riyal-symbol {
    width: 12px;
    margin-left: 2px;
    vertical-align: baseline;
}

.price-details-popover .discount {
    color: #198754 !important;
}

.price-details-popover .total-row {
    font-weight: 700;
    font-size: 18px;
    color: #000;
    border-top: 1px solid #E9ECEF;
    padding-bottom: 0;
}

.price-details-popover .btn-cancel-popover {
    background-color: #E9ECEF;
    color: #4F4F4F;
    border: none;
    padding: 10px 20px;
    font-weight: 500;
    cursor: pointer;
    position: absolute;
    top: 8px;
    right: 10px;
    border-radius: 50%;
}

.total-price-link {
    color: #6A6A6A;
    text-decoration: underline;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 2px;
}
.bs-popover-auto[data-popper-placement^=right]>.popover-arrow, .bs-popover-end>.popover-arrow,
.bs-popover-auto[data-popper-placement^=left]>.popover-arrow, .bs-popover-start>.popover-arrow,
.bs-popover-auto[data-popper-placement^=top]>.popover-arrow, .bs-popover-start>.popover-arrow,
.bs-popover-auto[data-popper-placement^=bottom]>.popover-arrow, .bs-popover-start>.popover-arrow{
    display: none;
}

.total-price-main-desktop {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.price-after-discount {
    display: inline-flex;
    align-items: center;
    gap: 2px;
    border: 1px solid #23BC4C;
    border-radius: 8px;
    font-size: 12px;
    font-weight: 500;
    background-color: #fff;
    overflow: hidden;
}
.pa-discount-icon{
    background: #2A9B54;
    height: 100%;
    display: flex;
    align-items: center;
    padding: 0 8px;
}
.price-after-discount img {
    width: 15px;
    height: 16px;
}
.pa-discount-text{
    padding: 0 5px;
}
.pa-discount-text p{
    color: #23BC4C;
    font-weight: 500;
}
.pa-dt-text span:nth-child(2){
    padding-left: 6px;
    position: relative;
}
.pa-dt-text span:nth-child(2)::after{
    content: "";
    position: absolute;
    top: 50%;
    left: 0;
    width: 3px;
    height: 3px;
    background: #2A9B54;
    border-radius: 50%;
    transform: translateY(-50%);
}
.total-price-main-mobile{
    display: none;
}
/* new property card end */

@media (max-width: 1440px){
    .lp-body-banner-img{
        max-height: 250px;
    }
    .lp-body .lp-body-content h1{
        font-size: 24px;
    }
    .lp-body .lp-body-content p{
        font-size: 15px;
    }
    .lp-body .lp-body-content{
        margin: 20px 0;
    }
    .lp-footer-logos img{
        height: 40px;
    }
}
@media(max-width: 1366px){
    .landing-page-sec .container{
        justify-content: space-between;
        padding: 0 0 20px; 
    }
    .lp-body-inner{
        padding-bottom: 85px;
        overflow: auto;
    }
    .lp-footer{
        position: fixed; 
        bottom: 0;
        background: #fff;
        width: 100%;
        padding-bottom: 20px;
    }
    .lp-footer-logos{
        margin-top: 20px;
    }
    .lp-card{
        margin-top: 0;
    }
    .lp-card-btn{
        margin-top: 10px;
    }
    .lp-body .lp-body-content h1{
        font-size: 22px;
    }
    .lp-body-banner-img{
        max-height: 225px;
    }
}
@media (max-width: 991px){
    .landing-page-sec .container{
        max-width: 650px;
    }
    .pl-sb-content h1{
        font-size: 32px;
    }
    .pl-sb-content p{
        font-size: 16px;
    }
    .pl-sb-img img{
        width: 100%;
        height: 100%;
    }
}
@media (max-width: 767px){
    .property-landing-sec{
        height: auto;
        padding-bottom: 50px;
    }
    .pl-sec-banner{
        height: auto;
    }
    .pl-sb-img{
        text-align: center;
    }
    .pl-sb-img img{
        width: 264px;
        height: 253px;
    }
    .pl-sf-img-content{
        gap: 4px;
    }
    .pl-sf-img-content img{
        width: 17px;
        height: 17px;
    }
    .pl-sec-footer{
        font-size: 14px;
    }
    .property-management-modals{
        z-index: 9999;
    }
    .pl-sb-content .theme-btn{
        padding: 7px 20px !important;
    }
    .pm-modal-footer{
        flex-direction: column-reverse;
    }
    .pm-modal-footer button{
        width: 100%;
    }
    .pm-mc-theme-btn{
        margin-bottom: 10px;
    }
}
@media (max-width: 480px){
    .property-management-modals .cm-simple-header h5{
        font-size: 18px;
    }
    .pm-mc-link-btn{
        padding: 8px 15px;
    }
}