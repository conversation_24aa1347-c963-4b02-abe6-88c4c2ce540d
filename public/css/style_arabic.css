.feature .bg,
.floated-img,
.mb-slid.slick-initialized.slick-slider {
  direction: ltr;
}

.floated-img,
a.dropdown-item,
img.fl-1,
img.fl-2,
img.fl-3,
img.fl-4 {
  direction: rtl !important;
}

.acc-inner ul li .ac-act,
.rcpt-btn::before,
header .search-reservation li::before {
  left: 0;
  right: unset;
}

img.fl-1 {
  right: -3%;
}

img.fl-2 {
  right: 10%;
}

img.fl-3 {
  right: 16%;
}

img.fl-4 {
  right: 33%;
}

img.fl-5 {
  right: 47%;
  z-index: 8;
}

img.fl-6 {
  right: 56%;
}

img.fl-7 {
  right: 70%;
  z-index: 4;
}

img.fl-8 {
  right: 78%;
}

header .nav .user .nav-menu {
  padding: 5px 5px 4px 7px;
}

.chat-text,
header .nav .nav-menu .user-profile {
  margin-right: 0;
  margin-left: 10px;
}

.notification-drop a {
  position: relative;
  margin-right: 10px;
  margin-left: 10px;
}

.navmenu ul.text-list li a {
  margin-right: 0px;
  margin-left: 20px;
}

.feature-content {
  text-align: right;
  justify-content: flex-end;
}

.feature .bg .d img {
  right: 29px;
  left: 70px;
}

footer .social-icon li {
  padding-right: 0;
  padding-left: 10px;
}

.social-signup-btn a button,
.total-guest .content,
[dir="rtl"] .pr-mini-detail h4,
[dir="rtl"] .textright-rtl,
a.dropdown-item,
footer .foot-nav li:nth-child(2n),
p.uncomp {
  text-align: right;
}

.custom-dropdown-menu li a {
  padding: 5px 15px 7px 5px;
  direction: rtl !important;
  text-align: right;
}

[dir="rtl"] .user-name {
  padding: 5px 0 7px 5px;
  text-align: right;
}

.social-signup-btn {
  justify-content: space-between !important;
  direction: rtl;
}

.notification-userimg,
.social-signup-btn .al-account {
  margin-right: 0;
  margin-left: 15px;
}

.social-signup-btn button img {
  margin-right: 0;
  width: 22px;
  margin-left: 10px;
}

.social-signup-btn button h6 {
  margin-bottom: -4px;
}

.mr-14 {
  margin-right: 0;
  margin-left: 14px;
}

.prev-btn img,
.social-signup-btn button i {
  margin-right: 0;
  margin-left: 12px;
}

.phone-number input {
  border-left: none;
  border-right: 1px solid #e2e2e2;
  padding-right: 8px;
}

.phone-number input:focus {
  border-left: unset;
  border-right: 1px solid #e2e2e2;
}

.phone-number select {
  margin-right: 0;
  direction: ltr;
  padding-left: 20px;
  text-align: right;
}

.product-category-2 li:first-child .listing-checkbox-wrapper.fsrch,
.social-btn-login a button img {
  margin-right: 0;
}

select {
  background-position: 4% 55% !important;
}

.side-menu-inner {
  border-right: none;
  border-left: 1px solid #bfbbbb8f;
}

ul.side-inner li a {
  direction: rtl;
  display: flex;
  align-items: center;
}

ul.side-inner li a i {
  top: 2px;
  margin-right: 0;
  margin-left: 10px;
}

[dir="rtl"] .host-sc img.fl-9 {
  right: 47px;
  left: unset;
}

[dir="rtl"] .host-sc img.fl-10 {
  right: 277px;
  left: unset;
}

[dir="rtl"] .host-sc img.fl-11 {
  left: 68px;
  right: unset;
}

[dir="rtl"] .host-sc img.fl-12 {
  left: -230px;
  right: unset;
}

[dir="rtl"] .host-sc img.fl-13 {
  left: -320px;
  right: unset;
}

[dir="rtl"] .host-help::before {
  right: 0;
  left: unset;
}

[dir="rtl"] .host-help .help-content li img {
  margin-right: 0;
  margin-left: 40px;
}

[dir="rtl"] .listing-category .category .category-content img {
  right: 40px;
  left: unset;
}

[dir="rtl"] .black-btn a {
  position: relative;
}

[dir="rtl"] .black-btn img {
  top: 55%;
  transform: scaleX(-1);
  margin-left: 5px;
}

[dir="rtl"] .listing-position {
  right: 200%;
}

[dir="rtl"] .cust-accordion .accordion-button::after {
  margin-left: 0;
  margin-right: auto;
}

[dir="rtl"] .listing-place-name .listing-input {
  padding: 10px 12px 0 0;
}

[dir="rtl"] .cust-check label {
  margin-right: 20px;
  margin-left: 0;
}

[dir="rtl"] .filter-check .filter-check-inner p {
  text-align: start;
}

.ls-actions,
[dir="rtl"] .textleft-rtl,
p.comp {
  text-align: left;
}

[dir="rtl"] .wait-approval i {
  margin-left: 8px;
  margin-right: 0;
}

[dir="rtl"] .pr-img {
  margin-left: 14px;
  margin-right: 0;
}

[dir="rtl"] .property-detail .property-feature-list li img {
  margin-left: 10px;
  margin-right: 0;
}

[dir="rtl"] .custom-subtotal {
  padding: 0 14px;
}

.property-detail .property-inner-detail .popup-user img {
  width: 45px;
  margin-right: 0;
  margin-left: 15px !important;
}

.popup-font {
  text-align: right !important;
}

.popup-check img {
  transform: rotate(179deg) !important;
}

.right-content.pr-cn {
  flex-wrap: wrap !important;
}

header .nav .header-btn {
  padding-right: 0;
  padding-left: 10px;
}

.product-category-2 li:last-child .listing-checkbox-wrapper.fsrch {
  margin-right: 10px;
}

.prev-btn,
.services .services-btn .filter-btn .inner-btn {
  margin-right: 15px;
  margin-left: 0px;
}

.clndr-btn,
.services .services-btn .filter-btn .inner-btn:nth-child(3),
.wait-approval {
  margin-left: 0;
}

.services .services-btn .filter-btn .inner-btn img {
  padding-right: 0;
  padding-left: 10px;
}

.services .services-btn .filter-btn .inner-btn:first-child img {
  padding-left: 0;
}

.ntf-main {
  transform: translate(110px, 42px) !important;
}

.side-list {
  padding: 0 0 0 25px;
}

.wait-approval {
  margin-right: auto;
}

.resrvbd-left {
  border-right: none !important;
  border-left: 1px solid var(--grey-one);
}

.clndr-btn {
  margin-right: 15px;
}

.clndr-btn img,
.next-btn img {
  margin-left: 0;
  margin-right: 12px;
}

.text-left {
  text-align: left !important;
}

.reservation-calend {
  left: -100%;
  right: unset;
}

.list-rate img {
  margin-right: 0;
  margin-left: 4px;
}

.slide-btn img {
  transform: rotate(180deg);
}

.chat-name p {
  margin-right: 7px;
}

.chat-profile-list {
  padding-right: 0;
  padding-left: 20px;
}

.product-rate img {
  margin-left: 5px;
  margin-right: 0 !important;
}

.inbox-property-img {
  margin-left: 10px;
  margin-right: 0;
}

.bk-pn-btn {
  display: flex;
  flex-flow: row-reverse;
  margin-left: 20px;
}

.calendar-month .month-nav-next {
  margin-right: 0;
}

.content-point {
  padding: 0 30px 0 0;
}

.downApp {
  justify-content: start;
}

.downApp-img img {
  right: -225px;
}

.back-line {
  font-size: 47px;
  margin-left: auto;
  margin-right: 0;
}

.downApp p {
  font-size: 48px;
}

/*help host banner slider*/
.swiper-rtl .swiper-button-hh-slide-next svg,
.swiper-rtl .swiper-button-hh-slide-prev svg {
  transform: rotate(180deg)
}

.swiper-rtl .swiper-button-hh-slide-next {
  left: var(--swiper-navigation-sides-offset, 70px);
  right: auto
}

.swiper-rtl .swiper-button-hh-slide-prev {
  right: var(--swiper-navigation-sides-offset, 70px);
  left: auto
}

.swiper-rtl .swiper-button-hh-slide-next:after {
  content: 'prev'
}

.swiper-rtl .swiper-button-hh-slide-prev:after {
  content: 'next'
}

/*about page*/
.airplane-img {
  left: auto;
  right: 0;
}

.as-mid-img .as-pattern-img {
  left: auto;
  right: -27px;
}

.as-mid-img .as-triangle-img {
  right: auto;
  left: 10px;
}

.traingle-shape {
  left: auto;
  right: 50%;
  transform: translateX(50%);
}

.as-mid-content {
  text-align: end;
}

.adven-mid-content {
  text-align: end;
}

.about-sl-sd {
  padding: 8px 8px 8px 20px;
}

.about-sl-sd img {
  margin: 0 0 0 20px;
}

.chat-name {
  margin: 0 15px 0 0;
}

.chat-property-media img {
  margin: 10px 48px 0 0;
}

.chat-profile h6 {
  margin: 48px 48px 0 0;
}

.chat-property-text p {
  margin: 5px 48px 0 0;
}

.chat-field-btn {
  right: auto !important;
  left: 15px;
}

.nInbox .chat-field .atach-icon img {
  margin: 0 0 0 10px;
}

.nInbox .chat-field input {
  padding: 10px 15px 12px 78px;
}

.pay-sec .cust-check label {
  margin-right: 0;
}

.contact-hs-inner {
  padding: 0 0 0 30px;
}

.ch-hb-btn img {
  margin: 0 0 2px 8px;
}

.mt-ask-content p {
  padding: 0 15px 0 0;
  margin: 0 20px 0 0;
}

.mt-ask-content p::before {
  left: auto;
  right: 0;
}

.hm-prop-content ul li img {
  margin: 0 0 2px 2px;
}

.ch-hb-btn img {
  margin: 0 0 2px 8px;
}

.hm-prop-content ul li {
  padding: 0 0 0 14px;
}

.dr-cust-arrow-btn {
  flex-direction: row-reverse;
}

.am-icon {
  margin-right: 0px;
  margin-left: 8px;
}

.promo-bx {
  direction: ltr;
}

button.gr-promo-btn {
  right: auto;
  left: 0;
  border-radius: 25px 0px 0px 25px;
}

.md-clndr-icon {
  right: auto;
  left: 20px;
}

.bootstrap-select .dropdown-toggle .filter-option-inner-inner {
  text-align: right;
}

.multi-slt i {
  right: auto;
  left: 13px;
}

.show-tick.cstm-slt-pick button {
  padding-left: 44px;
  padding-right: 25px;
}

.alert.alert-danger ul li {
  direction: ltr;
}

.alert.alert-danger ul li i {
  float: right;
  padding-left: 3px;
}

button.pd-promo-btn {
  left: 0;
  right: auto;
  border-radius: 25px 0px 0px 25px;
}


.form-check .form-check-input {
  margin-left: 0.5em;
}

span.close-report {
  float: left;
  font-size: 24px;
  position: relative;
  bottom: 22px;
  left: -16px;
}

.ticket-table .list-content {
  float: right;
}

.ticket-table .dataTables_wrapper .dataTables_filter {
  float: left !important;
}

a.report-pr {
  right: auto;
  left: 6px;
  display: flex;
}

a.report-pr i {
  top: 0px;
  padding-left: 3px;
}

.mini-profile [dir="rtl"] .pr-img {
  margin-right: 14px;
}

a.sign-in-btn {
  margin-right: 0;
  margin-left: 20px;
}


.filter-box {
  left: auto;
  right: 19%;
}

.search-reservation li span {
  text-align: right !important;
}

.popup1 {
  left: auto;
  right: 0;
}

.fd-icon {
  margin-right: 0;
  margin-left: 15px;
}

.popup h3 {
  text-align: right;
}

.downApp h2 {
  display: flex;
}

.fea-btn a:last-child button {
  margin-right: 15px;
}

.store-link {
  text-align: left;
}

.for-ar {
  display: inherit;
}

.for-eng {
  display: none;
}

[dir="rtl"] .slick-next::before {
  content: '\f053' !important;
}

[dir="rtl"] .slick-prev::before {
  content: '\f054' !important;
}

[dir='rtl'] .slick-next {
  right: auto;
  left: 50px;
}

[dir='rtl'] .slick-prev {
  right: 50px;
  left: auto;
}

.verification-code--inputs {
  direction: ltr;
}

header .nav .header-btn {
  padding-right: 20px;
  padding-left: 16px;
}

/* new design style */
.md-pb-pstatus {
  padding: 0 0 0 20px;
}

.md-pb-bio ul li .md-pb-bioicon {
  margin-right: 0px;
  margin-left: 7px;
}

.md-user-identity ul li i {
  margin-left: 10px;
  margin-right: 0;
}

.md-user-about ul li i {
  margin-right: 0;
  margin-left: 10px;
}

.rprt-btn i {
  margin-left: 8px;
  margin-right: 0;
}

.headBtn .swiper-button-prev {
  left: 0;
  right: auto;
}

.headBtn .swiper-button-next {
  left: 40px;
  right: auto;
}

.md-rb-usreview img {
  margin: 0 0 0 10px;
}

.sl-btn img {
  margin: 0 0 0 20px;
}

.share-si ul li {
  margin: 0 0 0 30px;
}

.dwc-inner label {
  left: auto;
  right: 20px;
}

.wl-property .pg-head h2 {
  margin: 0 24px 0 0;
}

.wl-right-btn button:nth-child(2) img {
  margin: 0 15px 0 0;
}

.wb-right {
  margin-left: 0;
  margin-right: 4px;
}

.wlisting-inner img {
  margin: 0 0 0 8px;
}

.rtl-leftArrowRotat {
  rotate: 180deg;
}

.cm-simple-header .btn-close {
  right: auto;
  left: 50px;
}

.type-place .input-container:nth-child(1) .radio-tile {
  border-top-right-radius: 10px;
  border-bottom-right-radius: 10px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-left: 0;
  border-right: 1px solid;
}

.type-place .input-container:nth-child(3) .radio-tile {
  border-top-left-radius: 10px;
  border-bottom-left-radius: 10px;
  border-right: 0;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-left: 1px solid;
}

.rb-input-container:first-child {
  margin-right: 0;
}

.main-title {
  text-align: right !important;
}

.inner-main-title {
  text-align: right;
}

.gr-btn button img {
  margin: 0 0 0 10px;
}

.ll-one-smallrate img {
  margin: 0 0 0 8px;
}

.ll-tl-img {
  margin: 0 0 0 20px;
}

.date-mark span {
  margin: 0 0 0 9px;
}

.ps-btn {
  margin: 0 0 0 15px;
}

.gr-inner img {
  margin: 0 0 0 5px;
}

.theme-user-rate img {
  margin: 0 0 0 6px;
}

.navmenu ul.text-list li {
  padding-right: 9px;
}

.popup2 {
  right: auto;
  left: 0;
}

.pr-list li a i {
  margin-right: 0;
  margin-left: 9px;
}

.currancy-dropdown {
  margin-right: 0px;
  margin-left: 20px;
}

.row-rev {
  flex-direction: row-reverse;
}

.rb-input-container:last-child {
  margin-right: 11px;
}

.verification-field {
  direction: ltr;
}

.airplane-img {
  left: auto;
  right: 0;
}

.as-mid-img .as-pattern-img {
  left: auto;
  right: -27px;
}

.as-mid-img .as-triangle-img {
  right: auto;
  left: 10px;
}

.traingle-shape {
  left: auto;
  right: 50%;
  transform: translateX(50%);
}

.as-mid-content {
  text-align: end;
}

.adven-mid-content {
  text-align: end;
}

.about-sl-sd {
  padding: 8px 8px 8px 20px;
}

.about-sl-sd img {
  margin: 0 0 0 20px;
}

.second-home {
  background-image: url(../images/sh-banner-ar.webp?v=1.0.3) !important;
}

.ts-back-btn img {
  margin: 0 0 0 10px;
}

.star-rating {
  direction: ltr;
}

.rs-rating-star .star-rating label {
  padding: 0 0 0 15px;
}

.sr-checks-input .listing-checkbox-wrapper {
  margin: 0 0 12px 8px;
}

.chat-up-icon {
  left: 5px;
  right: auto;
}

.chat-up-status {
  background-size: 15px 10px;
  padding: 0px 0px 0 19px;
}

.day-reserv-slider .slick-prev {
  right: auto;
  left: 50px !important;
}

.day-reserv-slider .slick-next {
  right: auto;
  left: 5px;
}

.dr-sh-right-cont {
  margin: 0 0 0 105px;
}

.day-reservation-tabbing .dr-tabs .nav-link:last-child {
  margin-right: 15px;
}

.day-reservation-tabbing .dr-tabs .nav-link:first-child {
  margin-right: 0;
}

.day-reserv-slider .slide .day-reserv-box {
  margin: 0 0 0 40px;
}

.hs-line-tabs .nav-link {
  margin: 0 0 0 40px;
}

.host-table-btn button img {
  margin: 0 5px 0 0;
}

.host-user-main button:first-child {
  margin: 0 0 0 15px;
}

.host-custabout-content img {
  margin: 0 0 0 20px;
}

.hs-cd-image {
  margin: 0 5px 0 0;
}

.hs-filter-drd-dtrange .form-dtrange {
  border-radius: 0 25px 25px 0;
  border-left: none;
  border-right: 1px solid var(--dim-gray);
}

.hs-filter-drd-dtrange .to-dtrange {
  border-radius: 25px 0 0 25px;
}

.rc-card-button {
  margin: 0 0 0 10px;
}

.chat-reservation-detail {
  padding: 0 0 0 15px;
}

.chat-reservation-detail .host-custabout-content img {
  margin: 0 0 0 10px;
}

.hs-custarrow-div .hs-ca-div-content img {
  rotate: 0deg;
}

.en-position {
  flex-direction: row-reverse;
}

.receipt-main .print-div {
  text-align: left;
}

/* new listing design  */
.get-img {
  margin-left: 0;
  margin-right: 35px;
}

.arrow {
  float: left;
}

.wrapper-dropdown {
  text-align: right;
  margin-right: 15px;
  margin-left: -1px !important;
  width: 96% !important;
}

.header-inner .col-xl-3.col-lg-3.text-right {
  text-align: left;
}

.logo-main-listing {
  text-align: right !important;
}

.offset-md-1 {
  margin-left: auto !important;
  margin-right: 8.33333333%;
}

.otp-input-wrapper input {
  direction: ltr;
  display: flex;
}

.pdf-content-detail>.row {
  direction: ltr;
}

.cust-check {

  padding-left: 0px;
}

.hl-sidebar {
  padding-right: 0;
  padding-left: 15px;
  border-right: none;
  border-left: 1px solid #E2E2E2;
}



/* host dashboard arabic fixes */
.p-datatable .p-datatable-tbody>tr>td {
  text-align: right !important;
}

.hst-reserv-table-btn .p-button {
  margin: 0px 5px !important;
}

/* .p-icon,.dr-cust-arrow-btn button i {
    transform: scaleX(-1);
} */
.overlay-group .p-overlaypanel {
  right: auto !important;
  left: 0 !important;
}

.p-button .p-button-icon-right {
  margin-left: auto;
  margin-right: 10px;
}

.host-navbar li:last-child {
  margin-right: 40px !important;
}

.hs-export-dropdown ul li a {
  display: flex;
  width: 100%;
  justify-content: space-between;
}

.hs-fil-body .hs-fil-input-right {
  border-radius: 8px 0px 0px 8px;
}

.hs-fil-body .hs-fil-input-left {
  /* border-right: none; */
  border-radius: 0px 8px 8px 0px;
}

.hls-list ul {
  padding: 0 0 0 10px;
}

.morepotos {
  right: auto;
  left: -2px;
  background: linear-gradient(to left, rgb(255 255 255 / 0%), rgb(255 255 255 / 89%) 59%, rgb(255, 255, 255)) !important
}

.host-edit-btn i {
  rotate: 180deg;
}

.hs-property img {
  margin: 0 0 0 10px;
}

label.hs-ls-it input {
  margin: 4px 0 0 10px;
}

.hostdash-total-guest .content {
  margin: 0 0 0 30px;
}

.container-host .ntf-main {
  transform: translate(-90px, 35px) !important;
}

.upload__box .col-6.text-right {
  text-align: left !important;
}

.cover-photo .text-right {
  text-align: left;
}

.reserv-head-inner .prev-btn {
  margin: 0 0 0 15px;

}

.nt-userdetail img {
  margin-right: auto;
  margin-left: 15px;
}

.newlstng-total-guest {
  width: 600px;
}

.district-slt-picker-main .dropdown-toggle::after {
  right: auto;
  left: 0;
}

.district-slt-picker-main .bootstrap-select.show-tick .dropdown-menu .selected span.check-mark {
  right: auto;
  left: 15px;
}

.district-slt-picker-main .dropdown-menu .dropdown-menu.inner .dropdown-item .text {
  margin: 0 0 0 34px;
}

.district-slt-picker-main .btn-light.dropdown-toggle {
  padding: 9px 0 6px 15px;
}

.property .product .product-detail .title h4 {
  margin-right: 0;
  margin-left: 10px;
}

.views i {
  margin-right: 0;
  margin-left: 3px;
}

.view-all {
  text-align: left;
}

.property .product .product-detail .title h4 {
  margin-right: 0 !important;
  margin-left: 10px;
}

.text-right-dir {
  text-align: left;
}


.rev-link-inner i {
  margin-right: 0;
  margin-left: 7px;
}


a.rev-lnk i.ri-arrow-right-s-line {
  transform: scale(-1);
}

.search-filter-select .dropdown-menu.show {
  transform: translate(43px, 86px) !important;
}



@media (max-width: 1680px) {
  /* .ntf-main {
    transform: translate(-129px, 35px) !important;
  } */
}

@media (max-width: 1440px) {
  .ntf-main {
    transform: translate(-138px, 42px) !important;
  }

  .search-filter-select .dropdown-menu.show {
    transform: translate(43px, 70px) !important;
  }
}

@media (max-width: 1366px) {
  header .search-reservation li {
    width: 26%;
  }

  header .search-reservation li:nth-child(2) {
    width: 38%;
  }

  .host-sc img.fl-10 {
    right: 207px;
  }

  .host-sc img.fl-11 {
    left: 138px;
  }

  .host-sc img.fl-12 {
    left: -90px;
  }

  .host-sc img.fl-13 {
    left: -190px;
  }

  .services .services-btn .filter-btn .inner-btn {
    margin-right: 0;
    margin-left: 15px;
  }

  .ntf-main {
    transform: translate(114px, 42px) !important;
  }

  .found-content h3 {
    font-size: 24px;
  }

  .hs-line-tabs .nav-link {
    margin: 0 0 0 15px;
  }
}

@media (max-width: 1280px) {
  .ntf-main {
    transform: translate(-110px, 42px) !important;
  }
}

@media (max-width: 1024px) {
  .host-sc img.fl-13 {
    left: -100px;
  }

  .host-sc img.fl-12 {
    left: -60px;
  }

  .services .services-btn .filter-btn .inner-btn:first-child {
    margin-right: 0;
    margin-left: 45px;
  }

  .services .services-btn .filter-btn .inner-btn:nth-child(2) {
    margin-left: 0;
  }

  .ntf-main {
    width: 240px !important;
    transform: translate(-100px, 35px) !important;
  }

  .form-control,
  .services .services-btn .discover-btn button {
    height: 43px;
  }
}

@media(max-width: 991px) {
  .navmenu ul.text-list li .tick-icon {
    left: 37px;
  }

  .navmenu ul.text-list li a {
    text-align: center;
  }

  .fl-on-mb {
    left: auto;
    right: 0;
  }

  header .nav .header-btn {
    padding-left: 0;
  }

  .found-content h3 {
    font-size: 24px;
  }

  .listing-checkbox-wrapper .listing-checkbox-icon {
    margin-right: 0px;
  }


}

@media (max-width: 600px) {

  body,
  html {
    overflow-x: hidden !important;
  }

  header .popup-main .popup.guest-popup {
    left: 29px !important;
    right: 23px;
  }

  header .popup h3 {
    text-align: right;
  }

  header .search-reservation li:last-child {
    padding-right: 0 !important;
  }

  header .nav .nav-menu .user-profile {
    margin-right: 0;
  }

  .popup-font {
    text-align: right !important;
  }

  .fl-on-mb {
    position: absolute;
    top: 15px;
    right: 0;
  }

  .services .services-btn .filter-btn .inner-btn:first-child {
    margin-right: 0;
    margin-left: 0;
  }

  .filter-btn .custom-dropdown-menu {
    left: auto !important;
  }

  .newlstng-total-guest {
    width: 100% !important;
  }

  .slt-plc {
    display: block;
    padding-top: 150px;
  }

}

ul.cs-amt li i {
  margin-right: 0;
  margin-left: 7px;
}

.sr-sidebar-title i {
  margin-right: 0;
  margin-left: 10px;
}

.sr-sidebar-progress .review-progress p {
  margin-right: 0;
  margin-left: 10px;
}

.sr-profile .sr-profile-name {
  margin-left: 0;
  margin-right: 10px;
}

.sr-cb-time {
  margin-left: 0;
  margin-right: 10px;
}

.sr-comment-host {
  margin: 0 25px 0 0;
}

.sr-content-search input {
  padding-left: 0;
  padding-right: 40px;
}

.sr-content-search i {
  left: unset;
  right: 15px;
}

.sr-sidebar-rate-inner .sr-sr-inner-left i {
  margin-right: 0;
  margin-left: 20px;
}

.tb-how {
  text-align: right;
  margin-top: 25px;
}

.tb-card {
  text-align: left;
}

span.tb-num {
  margin-right: 0px;
  margin-left: 8px;

}

.pay-sec .pay-sec-label input[type="radio"] {
  margin-right: 0;
  margin-left: 10px;
}

.pay-sec .pay-sec-label .pay-sl-content img {
  margin-right: 0;
  margin-left: 5px;
}

.ch-wallet-pay label {
  margin: 0 0 0 10px !important;
}

.ch-wallet-pay label::before {
  left: auto;
  right: 4px;
}

.ch-wallet-pay input:checked+label::before {
  left: auto;
  right: 19px;
}

.insurance-cover .insurance-cover-list li .ic-list-icon img {
  margin-right: 0;
  margin-left: 10px;
}

.inurance-limit .ic-box-icon {
  margin-right: 0;
  margin-left: 20px;
}

.inurance-claim .insurance-claim-list li {
  padding-left: 0;
  padding-right: 20px;
}

.inurance-claim .insurance-claim-list li::before {
  left: auto;
  right: 0;
}

.incurance-contact-ic img {
  margin-right: 0;
  margin-left: 5px;
}

.iqama-support-contact a {
  margin-left: 0;
  margin-right: 2px;
}

.list-skip-btn {
  margin-right: 0;
  margin-left: 5px;
}

.instruction .instruct-list{
  margin: 15px 15px 0 0;
}
.instruction .instruct-list li:before{
  left: unset;
  right: -18px;
}
.instruct-reg-prop-sec .instruct-list{
  margin: 30px 55px 0 0;
}
.instruct-reg-prop-sec .instruct-list li:before{
  left: unset;
  right: -50px;
}
.instruction-tabing.nav-pills .nav-item:first-child .nav-link{
  border-radius: 0 25px 25px 0;
}
.instruction-tabing.nav-pills .nav-item:last-child .nav-link {
  border-radius: 25px 0 0 25px;
  border-left: inherit;
  border-right: unset;
}
.listing-item-main{
  padding: 0 0 0 10px;
}
.create-new-info i{
transform: rotate(180deg);
}
.review-progress-count{
  margin-left: 0;
  margin-right: 20px;
}.lf-rv-img-en,
.rt-rv-img-en{
  display: none;
}
.lf-rv-image-ar,
.rt-rv-img-ar{
    display: block;
}
span.rv-num{
  margin-right: 0;
  margin-left: 10px;
}
span.amn-img{
  margin-right: 0;
  margin-left: 10px;
}
h6.rev-date.rev-dt{
  right: unset;
  left: 16px;
}
.rev-search i{
  left: unset;
  right: 14px;
}
.rev-search input.form-control{
  padding-left: 0;
  padding-right: 50px;
}
.rev-views-ppl{
  padding-right: 0;
  padding-left: 6px;
}
@font-face {
  font-family: 'DIN-NEXT-ARABIC';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url('fonts/din-next-arabic/DINNextLTArabic-Regular.ttf')
}

.property-pricing h4,
.property-pricing span,
.property-pricing td,
.property-pricing th {
  font-family: 'DIN-NEXT-ARABIC' !important;
}
.product-detail .sar-pr{
  font-size: 21px;
}
.mw-discount{
  right: unset;
  left: 0;
  border-radius: 20px 0 20px 0;
}
.property-inner-detail .product-rate{
  direction: ltr;
}
.ss-pr-inner{
  direction: rtl;
}
.checkout-detail h5,
.checkout-detail h6,
.checkout-detail span,
.checkout-detail td,
.checkout-detail th,
.checkout-detail .mw-discount{
  font-family: 'DIN-NEXT-ARABIC' !important;
}
.mw-discount{
  border-left: none;
  border-right: 5px solid #ffc107;
}
.checkout-coupon-code button.pd-promo-btn{
  border-radius: 16px 0px 0px 16px;
  right: auto;
}
.checkout-registeration #phone-input-signup{
  text-align: right;
}

.d-agent-img{
  margin-right: auto;
  margin-left: 15px;
}
.cs-chat-field input{
  padding: 12px 12px 12px 75px;
}
.cs-cf-btn{
  right: auto;
  left: 15px;
  bottom: 37px;
}
.not-available-tag::before{
  margin-right: 0;
  margin-left: 4px;
}

.d-agent-img{
  margin-right: auto;
  margin-left: 15px;
}
.cs-chat-field input{
  padding: 12px 12px 12px 75px;
}
.cs-cf-btn{
  right: auto;
  left: 15px;
  bottom: 37px;
}
.fav-icon{
  right: unset;
  left: 0;
}
.fav-in{
  right: unset;
  left: 7px;
}
.mw-discount-property{
  left: unset;
  right: 0;
  border-radius: 0 8px 0 8px;
}
.not-available-tag{
  left: unset;
  right: 5px;
}
.pl-sb-content h1 img{
  right: 0;
  left: auto;
}
.property-management-modals .cm-simple-header .btn-close{
  right: auto;
  left: 30px;
}
.pl-sb-img{
  text-align: left;
}
.landing-page-dropdown .menubtn{
  margin-left: unset;
  margin-right: auto;
}
.property-management-modals .form-control.is-invalid, .was-validated .form-control:invalid{
  background-position: left calc(.375em + .1875rem) center !important;
}
.property-management-modals .form-select.is-invalid:not([multiple]):not([size]), .form-select.is-invalid:not([multiple])[size="1"], .was-validated .form-select:invalid:not([multiple]):not([size]), .was-validated .form-select:invalid:not([multiple])[size="1"]{
  padding-right: 14px !important;
  padding-left: 4.125rem !important;
  background-position: left .75rem center,center left 2.25rem !important;
}
.exclusive-tag{
  left: unset;
  right: 10px;
}
.pb-exclusive-tag{
    left: 5px;
    right: unset;
}
.booking-accordion .booking-accordion-header{
  padding-right: unset;
  padding-left: 10px;
}
.booking-accordion .append_date .text-right{
  text-align: left !important;
}
.new-property-card .np-badge-overlay{
  left: unset;
  right: 10px;
}
.new-property-card .np-action-icons{
  right: unset;
  left: 10px;
}
.price-details-popover .btn-cancel-popover{
  right: unset;
  left: 10px;
}
.property-features-badges .pf-badge{
  padding: 2px 2px 2px 8px;
}
@media (max-width: 767px){
  .pl-sb-img{
    text-align: center;
  }
}
