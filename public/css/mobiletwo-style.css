/* global css */
@font-face {
  font-family: 'dubai-font';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url('../fonts/dubai-font/Dubai-Light.ttf')
}

@font-face {
  font-family: 'dubai-font';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url('../fonts/dubai-font/Dubai-Regular.ttf')
}

@font-face {
  font-family: 'dubai-font';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url('../fonts/dubai-font/Dubai-Medium.ttf')
}

@font-face {
  font-family: 'dubai-font';
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url('../fonts/dubai-font/Dubai-Bold.ttf')
}

.ob-br {
  object-fit: cover;
  border-radius: 50%;
}

.ts-back-btn img {
  margin-right: 10px;
  width: 10px;
}

.ts-back-btn span {
  color: #181818;
  font-size: 17px;
  font-weight: 500;
}

.df-align-center {
  display: flex;
  align-items: center;
}

.df-justify-between {
  display: flex;
  justify-content: space-between;
}

.success-green-color {
  color: var(--success-green);
}

.warning-red-color {
  color: var(--warning-red);
}

.ht-bd-btn {
  border: 1px solid #707070 !important;
  color: var(--dr-shade-gray);
  padding: 0 10px;
  border-radius: 5px;
  transition: .3s ease-in-out;
}

.ht-bd-btn:hover {
  background-color: var(--dr-shade-gray);
  color: #fff;
}

.ht-bbg-btn {
  background-color: var(--dr-shade-gray);
  border: 1px solid var(--dr-shade-gray);
  color: #fff;
  padding: 0 10px;
  border-radius: 5px;
  transition: .3s ease-in-out;
  line-height: 1;
}

.ht-bbg-btn:hover {
  background-color: #fff;
  color: var(--dr-shade-gray);
}

.theme-bd-btn {
  border: 1px solid var(--theme-primary);
  color: var(--theme-primary);
  background-color: transparent;
  height: 50px;
  font-weight: 500;
  border-radius: 7px;
}

/*modal css*/
.cm-sm-width {
  width: 400px !important;
}

.cm-lg-width {
  max-width: 620px !important;
}

.single-modal-content {
  font-size: 17px;
}

.fs-17 {
  font-size: 17px;
}

/*border modal css*/
.cm-bd-content {
  border-radius: 15px;
}

.cm-bd-header {
  border-color: #D1D1D1;
  padding: 0 15px;
  height: 55px;
}

.cm-bd-header h5 {
  font-size: 22px;
  color: #000;
}

.cm-bd-header .btn-close {
  border: none;
  font-size: 13px;
}

.cm-bd-body {
  padding: 17px 20px;
}

.cm-bd-footer {
  border-color: #D1D1D1;
  padding: 15px 20px;
  margin: 0;
}

.cm-footer-two {
  justify-content: space-between;
}

.cm-footer-two button {
  max-width: max-content;
}

.modal-footer>* {
  margin: 0;
}

.modal-form-control {
  border-radius: 10px;
  border: 1px solid #8A8A8A;
  height: 55px;
  padding: 10px 20px;
  width: 100%;
  color: #000;
}

.modal-form-control::placeholder {
  font-weight: 500;
  color: #8A8A8A;
}

.logout-btn {
  width: 100%;
  display: block;
  border: 2px solid;
  color: #000000;
  padding: 15px 0;
  text-align: center;
  border-radius: 8px;
  font-size: 18px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 2px;
  margin-top: 25px;
}

/*border modal css end*/

/*modal animation bottom to top*/
.modal-dialog {
  position: fixed !important;
  bottom: 0 !important;
  left: 0% !important;
  right: 0% !important;
  margin-bottom: 0 !important;
}

.modal.fade .modal-dialog {
  transform: translateY(100%);
  transition: transform 0.4s ease-in-out;
}

.modal.show .modal-dialog {
  transform: translateY(0px);
}

/*modal animation end*/
/*small modal behaviour bottom*/
.modal.modal-dr-bottom {
  padding: 0 !important;
}

.modal-dr-bottom .modal-dialog {
  margin: 0 !important;
  height: 100%;
}

.modal-dr-bottom .modal-content {
  margin: auto 0 -1px 0;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

/*small modal behaviour end*/
/*large modal behaviour*/

.modal.show .modal-dialog.modal-lg-bottom {
  transform: translateY(5px);
}

.modal-lg-bottom .modal-dialog {
  margin: auto 0 0 0;
}

.modal-lg-bottom .modal-content {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

/*large modal behaviour end*/
/*simple modal css*/
.cm-simple-content {
  border-radius: 10px;
}

.cm-simple-header {
  padding: 35px 15px 20px;
  border: none;
  position: relative;
}

.cm-simple-body {
  padding: 0 15px 10px;
}

.cm-simple-header .btn-close {
  position: absolute;
  right: 25px;
}

/* simple modal css end */

/*button css*/
.theme-btn {
  height: 44px;
  /* padding: 0; */
  font-size: 15px
}

.sm-fc-black {
  color: #000000;
}

/*button css end*/
.input-withlable {
  display: flex;
  align-items: center;
  box-shadow: 5.5px 3.5px 16px rgba(149, 157, 165, 0.2);
  border-radius: 16px;
  padding: 0 20px;
  height: 50px;
}

.input-withlable {
  white-space: nowrap;
  color: #000000;
  font-weight: 500;
  font-size: 14px;
}

.input-withlable input {
  border: none;
  outline: none;
}

.input-withlable select {
  border: none;
  outline: none;
}

.input-withlable input:focus {
  outline: none !important;
  border: unset !important;
}

.input-withlable select:focus {
  outline: none !important;
  border: unset !important;
}

.input-withlable select {
  color: #000;
  font-weight: 500;
}

.input-withlable input::placeholder {
  color: #000;
  text-align: center;
}

.dark-grey {
  color: #575757;
}

/* scroll bar */
.bt-grey-scroller ::-webkit-scrollbar {
  width: 8px;
}

.bt-grey-scroller ::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 25px;
}

.bt-grey-scroller ::-webkit-scrollbar-track {
  border-radius: 0;
  background: transparent;
  box-shadow: unset;
}

/* scroll bar end*/





.enable-md-btn.disabled {
  background-color: #8A8A8A;
  cursor: not-allowed;
  pointer-events: none;
}

.enable-md-btn {
  border: none;
}

.cm-bd-footer button {
  height: 44px;
  width: 100%;
  font-size: 14px;
  letter-spacing: .4px;
}

.wishlist-listing {
  padding: 10px 10px 10px 0;
  border-radius: 8px;
  margin-bottom: 10px;
  transition: .3s ease-in-out;
  cursor: pointer;
}

.wishlist-listing:last-child {
  margin-bottom: 0;
}

.wishlist-listing:hover {
  background: #EDEDED;
  padding-left: 10px;
}

.wlisting-inner {
  display: flex;
  align-items: center;
}

.wlisting-inner img {
  width: 55px;
  height: 55px;
  object-fit: cover;
  border-radius: 10px;
  margin-right: 8px;
}

.wlisting-inner h5 {
  flex: 1;
  font-size: 17px;
}


/* new designing css  */
.dubai-ff {
  font-family: 'dubai-font';
}

.pg-main-title {
  font-weight: 500;
  margin-bottom: 25px;
}

.no-wishlist-content h4 {
  color: #181818;
  margin-bottom: 18px
}

.no-data p {
  font-size: 18px;
  color: #8A8A8A;
}

.wh-board-height {
  height: 235px;
}

.wishlist-board {
  display: block;
  border-radius: 15px;
  overflow: hidden;
}

.wb-left {
  height: 100%;
  width: 100%;
}

.wb-left img {
  height: 100%;
  width: 100%;
  object-fit: cover;
}

.wb-right {
  width: 100%;
  height: 117.5px;
}

.wb-right img {
  width: 100%;
  height: 100%;
}

.create-wb {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  background-image: url("data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100%25' height='100%25' fill='none' rx='15' ry='15' stroke='%23909090FF' stroke-width='3' stroke-dasharray='6%2c 14' stroke-dashoffset='23' stroke-linecap='square'/%3e%3c/svg%3e");
  border-radius: 15px;
  width: 100%;
}

.create-wb img {
  width: 75px;
}

.create-wb p {
  margin-top: 18px;
  font-size: 27px;
  font-weight: 500;
  color: #909090;
}

.input-mx-content {
  color: #8A8A8A;
  margin-top: 10px;
  display: block;
  font-size: 14px;
}

.wl-left-btn img {
  width: 14px;
}

.wl-right-btn button:nth-child(1) img {
  width: 17px;
}

.wl-right-btn button:nth-child(2) img {
  width: 22px;
  margin-left: 8px;
}

.sl-btn {
  display: flex;
  align-items: center;
  border-radius: 10px;
  border: 1px solid #8A8A8A;
  width: 100%;
  padding: 20px;
  transform: scale(1);
  transition: .3s all;
  color: #000000;
}

.sl-btn:hover {
  border-color: #000;
}

.sl-btn img {
  width: 40px;
  margin-right: 20px;
}

.sl-btn .sl-content {
  text-align: start;
}

.share-si h4 {
  margin: 25px 0 20px;
}

.share-si ul {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 0;
}

.share-si ul li {
  /* margin-right: 30px; */
}

.share-si ul li a img {
  width: 48px;
}

.modal-delete-btn {
  font-size: 18px;
  font-weight: 500;
  text-decoration: underline;
  color: #000000;
}

.dwc-inner {
  position: relative;
}

.dwc-inner label {
  position: absolute;
  top: 11px;
  left: 20px;
  color: #8A8A8A;
}

.dwc-inner input {
  padding-top: 28px;
  height: 75px;
}

/* .wishlist-listing-sec {
  height: calc(100vh - 72px);
  overflow: hidden;
} */

.wishlist-listing-sec {
  margin: 20px 0 0;
}

.wishlist-map {
  height: 100%;
}

.wl-property .pg-main-title {
  font-weight: 500;
  margin-bottom: 25px;
  padding: 0 16px;
}

/* .wl-property {
  overflow-y: auto;
  overflow-x: hidden;
  height: 92vh;
  padding: 0;
} */

.wl-property::-webkit-scrollbar {
  display: none;
}

.guest-btn {
  background-color: #D1D1D1;
  border: none;
  color: #fff;
  font-size: 17px;
  height: 47px;
  border-radius: 7px;
}

.guest-btn:hover {
  color: #fff;
}

.cancel-addcard img {
  width: 12px;
  position: absolute;
  left: 30px;
  top: 59%;
  transform: translateY(-50%);
}

.wallet-btn {
  padding: 0 45px;
}

.cd-darent h6 {
  color: #23BC4C;
  font-size: 18px;
  margin: 35px 0 10px;
  font-weight: 500;
}

.cd-darent p {
  font-size: 17px;
  color: #575757;
}

.card-num p {
  color: #575757;
}

.property-notify {
  display: flex;
  align-items: self-start;
  border-bottom: 1px solid #E2E2E2;
  flex-flow: wrap;
}

.property-nt-img {
  width: 100%;
  height: 150px;
  margin: 0 0 10px;
}

.property-nt-img img {
  height: 100%;
  width: 100%;
  border-radius: 10px;
  object-fit: cover;
}

.property-nt-content {
  width: 70%;
}

.pt-nt-userdetail {
  display: flex;
  align-items: center;
}

.mid-content {
  font-size: 18px;
  display: block;
  margin: 0 8px;
  color: #575757;
}

.nt-mini-userimg {
  width: 27px !important;
  margin-right: 8px !important;
}

.pt-nt-date {
  width: 30%;
  display: flex;
  height: 80px;
  justify-content: end;
}

.notification-user:last-child {
  border-bottom: 0;
}

.side-inner li a.on-pg i {
  font-weight: 600;
}

.side-inner li a:hover {
  color: #000000;
  font-weight: 500;
}

.side-inner li a:hover i {
  font-weight: 600;
}

.pro-name {
  color: #575757;
}

.change-pro-btn {
  border: 1px solid #575757;
  border-radius: 10px;
  text-decoration: none !important;
}

.ac-dt h5 {
  font-size: 16px;
  margin-bottom: 13px;
}

.ls-icon-main {
  margin: 0 auto;
  border-radius: 50%;
  width: 55px;
  height: 53px;
  display: flex;
  align-items: center;
  position: relative;
}

.ls-icon {
  width: 36px;
}

.ls-icon-mobile {
  width: 19px;
}

.circle-check {
  position: absolute;
  bottom: 7px;
  right: -8%;
  transform: translateX(-50%);
  width: 17px;
  background: #fff;
  border-radius: 50%;
}

.nic-btn {
  height: 40px;
  font-size: 14px;
  margin-bottom: 15px;
  font-weight: 500;
  color: #000000;
}

.theme-user-rate {
  margin-left: 15px;
  font-size: 18px;
  font-weight: 600;
  display: flex;
  align-items: baseline;
  line-height: 1;
}

.theme-user-rate img {
  width: 17px;
  margin-right: 6px;
}

.account-tab .nav-pills {
  border-radius: 0;
  color: #000;
  border-bottom: 1px solid #E2E2E2;
  padding-bottom: 15px;
}

.shadow-tabs .nav-pills .nav-link {
  transition: .3s ease-in;
  margin-left: 5px;
}

.shadow-tabs .nav-pills .nav-link:hover {
  background: #fff;
  color: #000;
  font-weight: 500;
  box-shadow: 2.5px 3.5px 15px rgba(149, 157, 165, 0.2);
}

.shadow-tabs .nav-pills .nav-link.active,
.grey-tabs .nav-pills .show>.nav-link {
  background: #fff;
  color: #000;
  font-weight: 500;
  box-shadow: 2.5px 3.5px 15px rgba(149, 157, 165, 0.2);
}

.ac-radio {
  align-items: end !important;
  margin-bottom: 10px;
}

.ac-radio input {
  border: 1px solid #707070 !important;
}

.ac-radio p {
  color: #575757;
}

.acc-inner ul li .ac-dt input,
textarea {
  color: #575757 !important;
}

.list-right-img img {
  width: 100%;
  height: 130px;
  border-radius: 10px;
  object-fit: cover;
}

.listing-layout-one {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #E2E2E2;
  padding-bottom: 20px;
  margin-bottom: 25px;
  flex-direction: column-reverse;
}

.ll-one-left {
  width: 100%;
  display: flex;
  align-items: flex-start;
  margin-top: 15px;
}

.ll-one-left .pr-img img {
  width: 45px !important;
  height: 45px !important;
}

.ll-one-right {
  width: 100%;
  text-align: end;
}

.ll-one-create {
  color: #181818;
}

.ll-one-smallrate {
  color: #000000;
  font-weight: 500;
  margin: 7x 0 !important;
}

.ll-one-smallrate img {
  margin-right: 8px;
  width: 14px;
}

.listing-layout-two {
  border-top: 0 !important;
  border-left: 0 !important;
  border-right: 0 !important;
  border-bottom: 1px solid #E2E2E2;
  border-radius: 0;
}

.ll-tl-img {
  height: 115px;
  width: 160px;
  margin-right: 20px;
}

.ll-tl-img img {
  width: 100% !important;
  height: 100% !important;
}

.ll-two-left .date-mark {
  color: #7AA826;
}

.ll-two-left .date-mark i {
  color: #181818;
}

.ll-two-left .list-descrip {
  font-size: 17px;
  margin: 5px 0 6px;
}

.ll-two-right .mini-profile {
  display: flex;
  align-items: center;
  /* padding: 10px 0 0; */
  justify-content: start;
}

.ll-two-right .mini-profile img {
  width: 50px;
  height: 50px;
}

.ps-btn {
  height: 34px;
  font-size: 16px;
  margin-right: 15px;
  font-weight: 500;
}

.ll-two-left .list-price {
  font-size: 17px;
}


/* filter modal view blad page end */
.filter-padding {
  padding: 20px 15px;
  border-bottom: 1px solid #D1D1D1;
}

.filter-padding .main-title {
  margin-bottom: 20px;
}

.filter-padding .inner-main-title {
  font-size: 17px;
  margin-bottom: 10px;
}

.type-place {
  padding: 0;
  margin: 0;
  border-radius: 10px;
  display: flex;
  align-items: center;
  max-width: max-content;
}

.input-container {
  position: relative;
  height: 68px;
  width: 180px;
}

.input-container .radio-button {
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  margin: 0;
  cursor: pointer;
}

.input-container .radio-tile {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  border: 1px solid #707070;
  transition: transform 300ms ease;
}

.input-container .radio-tile-label {
  text-align: center;
  font-size: 14px;
  font-weight: 400;
  color: #000000;
}

.input-container .radio-button:checked+.radio-tile {
  background-color: var(--theme-primary);
  border: 2px solid var(--theme-primary);
  color: white;
}

.input-container .radio-button:checked+.radio-tile .radio-tile-label {
  color: white;
}

.type-place .input-container:nth-child(1) .radio-tile {
  border-top-left-radius: 10px;
  border-bottom-left-radius: 10px;
  border-right: 0;
}

.type-place .input-container:nth-child(3) .radio-tile {
  border-top-right-radius: 10px;
  border-bottom-right-radius: 10px;
  border-left: 0;
}

.type-place-main p {
  font-size: 14px;
  color: #000000 !important;
  margin: 10px 0 0;
}

.editable-rang {
  border: none;
  outline: none;
  width: 70%;
}

.editable-rang:focus {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

.price-btn.editable-border {
  border: 1px solid #000000;
}
.recommended-checkbox {
  appearance: none;
  -webkit-appearance: none;
  display: none;
}

.recommended-label {
  border: 1px solid #ddd;
  border-radius: 12px;
  padding: 15px;
  text-align: center;
  cursor: pointer;
  transition: transform 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-weight: 500;
}

.recommended-label img {
  width: 40px;
  height: 40px;
  margin-bottom: 10px;
}
.recommended-content{
    text-align: center;
    margin-top: 5px;
    color: #000000;
}

.recommended-label:hover {
  border-color: #000000;
}

.recommended-checkbox:checked + .recommended-label {
  border: 1px solid black;
  outline: 1px solid black;
  background-color: #FFFBF2;
  animation: zoomEffect 0.3s ease;
}

@keyframes zoomEffect {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.95);
  }
  100% {
    transform: scale(1);
  }
}
.type-place-btn-group {
  border-radius: 12px;
  overflow: hidden;
  padding: 5px;
}

.type-place-toggle-btn input[type="radio"] {
  display: none;
}

.type-place-toggle-btn label {
  padding: 7px;
  margin: 0;
  cursor: pointer;
  background-color: transparent;
  border: none;
  font-weight: 500;
  width: 100%;
  text-align: center;
  border-radius: 9px;
  border: 1px solid #ddd; 
  font-size: 14px;
}

.type-place-toggle-btn input[type="radio"]:checked + label {
  background-color: #FFFBF2;
  border-color: #000000;
  outline: 1px solid black;
}

.type-place-toggle-btn label:hover {
  background-color: #FFFBF2;
}
/*room and bed radio button*/
.rb-input-container {
  position: relative;
  height: 30px;
  width: 52px;
  margin-right: 11px;
}

.rb-input-container:last-child {
  margin-right: 0;
}

.rb-input-container .rb-radio-button {
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  margin: 0;
  cursor: pointer;
}

.rb-input-container .rb-radio-tile {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  border: 1px solid #707070;
  transition: transform 300ms ease;
  border-radius: 25px;
}

.rb-input-container .rb-radio-tile-label {
  text-align: center;
  font-size: 15px;
  font-weight: 400;
  color: #181818;
}

.rb-input-container .rb-radio-button:checked+.rb-radio-tile {
  background-color: var(--theme-primary);
  border: 1px solid var(--theme-primary);
  color: white;
}

.rb-input-container .rb-radio-button:checked+.rb-radio-tile .rb-radio-tile-label {
  color: white;
}

.thm-check.cust-form-check-input:checked[type=checkbox] {
  background-image: url('../icons/theme-check.svg') !important;
  background-size: 10px
}

.thm-check.form-check-input {
  border-radius: 5px !important;
  border: 1px solid #707070 !important;
  margin-top: 0 !important;
}

.thm-check.form-check-input:focus {
  border-color: #707070;
}

.thm-check.cust-form-check-input:focus {
  border-color: #707070 !important;
}

.thm-check.cust-form-check-input:checked {
  border-color: #707070 !important;
}

.sm-price-filter {
  padding: 10px 20px;
  z-index: 9999;
  border: none;
}

.sm-price-filter li a {
  padding: 10px 0;
  color: #000000;
  border-bottom: 1px solid #707070;
  font-size: 17px;
}

.sm-price-filter li a:hover {
  background-color: transparent;
}

.sm-price-filter li:last-child a {
  border-bottom: none;
}

.rprt-btn i {
  margin-right: 8px;
}

.rprt-btn span {
  display: block;
  font-weight: 500;
}

.overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgb(0 0 0 / 23%);
  /* Adjust the opacity as needed */
  z-index: 9999;
  /* Make sure the z-index is higher than the dropdown menu */
  overflow: hidden;
}

.dropdown-menu.show+.overlay {
  display: block;
}

/* filter modal view blad page end */



/* profile book modal*/
#md-profile-book .modal-body::-webkit-scrollbar {
  display: none !important;
}

.profile-modal-main {
  background: #ffff;
}

.md-profile-box {
  background-color: #ffff;
  border-radius: 20px;
  padding: 25px 0;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.md-pb-profile {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  height: 100%;
}

.md-pb-pimg {
  position: relative;
}

.md-pb-profile .up {
  width: 50px;
  border-radius: 50%;
  object-fit: cover;
}

.md-pb-pcontent {
  text-align: center;
  margin: 10px 0;
}

.md-pb-pcontent h4 {
  font-size: 18px;
  line-height: 1;
}

.md-pb-pcontent p {
  color: #000000;
  margin-bottom: 10px;
}

.md-pb-pstatus {
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

.md-pb-pstatus hr {
  margin: 0.5rem 0;
}

.md-pb-pstatus li h6 {
  font-size: 20px;
  line-height: 1;
}

.md-pb-pstatus li p {
  line-height: 1.1;
  font-size: 14px;
}

.md-pb-verfy {
  width: 15px;
  bottom: 4px;
  right: -17%;
}

.md-pb-width {
  width: 400px !important;
}

.md-pb-bio ul {
  margin: 30px 0px 0px;
}

.md-pb-bio ul li {
  margin-bottom: 12px;
  display: flex;
  align-items: flex-start;
  color: #222222;
}

.md-pb-bio ul li span {
  display: block;
}

.md-pb-bio ul li .md-pb-bioicon {
  width: 22px;
  margin-right: 7px;
}

.md-pb-bio ul li span i {
  font-size: 18px;
}

.md-pb-bio ul li .md-pb-biocontent {
  font-size: 14px;
  flex: 1;
}

.md-pb-bio p {
  color: #222222;
  font-size: 14px;
}

.md-rb-usreview img {
  width: 42px;
  height: 42px;
  margin-right: 10px;
}

.md-rb-content {
  padding: 20px;
  border: 1px solid #D1D1D1;
  border-radius: 10px;
}

.md-rb-body button {
  border: 1px solid #000000;
  width: 100%;
  padding: 14px 0;
  border-radius: 10px;
  font-size: 16px;
  font-weight: 500;
  margin: 20px 0;
  color: #000000;
}

.md-rb-rev {
  color: #222222;
}

.md-ul-head h4 {
  font-size: 19px;
}

.md-user-identity ul {
  margin: 0;
}

.md-user-identity ul li {
  display: flex;
  align-items: center;
  color: #222222;
}

.md-user-identity ul li i {
  margin-right: 10px;
  font-size: 23px;
}

.md-user-identity ul li p {
  color: #222222;
  font-size: 18px;
}

.md-user-about ul {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-flow: wrap;
}

.md-user-about ul li {
  border-radius: 25px;
  color: #222222;
  border: 1px solid #D1D1D1;
  padding: 5px 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 10px 10px 0;
}

.md-user-about ul li i {
  margin-right: 10px;
  font-size: 21px;
}

.md-ul-body .product .product-img {
  height: 150px !important;
}

.mdUlSlider .product .product-detail .title h4 {
  flex: 0 0 55%;
}

.mdUlSlider .product .product-detail .title h4 .product-rate {
  align-items: baseline;
}

.headBtn {
  position: relative;
}

.headBtn .swiper-button-next,
.headBtn .swiper-button-prev {
  border: 1px solid #D1D1D1;
  border-radius: 50%;
  width: 25px;
  height: 25px;
  top: var(--swiper-navigation-top-offset, -14%);
}

.headBtn .swiper-button-prev {
  left: auto;
  right: 34px;
}

.headBtn .swiper-button-next {
  left: auto;
  right: 0px;
}

.headBtn .swiper-button-next:after,
.headBtn .swiper-button-prev:after {
  font-size: 11px;
  color: #000000;
  font-weight: 700;
}


.headBtn .swiper-button-next-pro,
.headBtn .swiper-button-prev-pro {
  border: 1px solid #D1D1D1;
  border-radius: 50%;
  width: 25px;
  height: 25px;
  top: var(--swiper-navigation-top-offset, -9%);
}

.headBtn .swiper-button-prev-pro {
  left: auto;
  right: 40px;
}

.headBtn .swiper-button-next-pro {
  left: auto;
  right: 0px;
}

.headBtn .swiper-button-next-pro:after,
.headBtn .swiper-button-prev-pro:after {
  font-size: 11px;
  color: #000000;
  font-weight: 700;
}

.swiper-button-next-pro,
.swiper-button-prev-pro {
  position: absolute;
  top: var(--swiper-navigation-top-offset, 50%);
  width: calc(var(--swiper-navigation-size)/ 44 * 27);
  height: var(--swiper-navigation-size);
  margin-top: calc(0px - (var(--swiper-navigation-size)/ 2));
  z-index: 10;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--swiper-navigation-color, var(--swiper-theme-color))
}

.swiper-button-next-pro.swiper-button-disabled,
.swiper-button-prev-pro.swiper-button-disabled {
  opacity: .35;
  cursor: auto;
  pointer-events: none
}

.swiper-button-next-pro.swiper-button-hidden,
.swiper-button-prev-pro.swiper-button-hidden {
  opacity: 0;
  cursor: auto;
  pointer-events: none
}

.swiper-navigation-disabled .swiper-button-next-pro,
.swiper-navigation-disabled .swiper-button-prev-pro {
  display: none !important
}

.swiper-button-next-pro svg,
.swiper-button-prev-pro svg {
  width: 100%;
  height: 100%;
  object-fit: contain;
  transform-origin: center
}

.swiper-rtl .swiper-button-next-pro svg,
.swiper-rtl .swiper-button-prev-pro svg {
  transform: rotate(180deg)
}

.swiper-button-prev-pro,
.swiper-rtl .swiper-button-next-pro {
  left: var(--swiper-navigation-sides-offset, 10px);
  right: auto
}

.swiper-button-next-pro,
.swiper-rtl .swiper-button-prev-pro {
  right: var(--swiper-navigation-sides-offset, 10px);
  left: auto
}

.swiper-button-lock {
  display: none
}

.swiper-button-next-pro:after,
.swiper-button-prev-pro:after {
  font-family: swiper-icons;
  font-size: var(--swiper-navigation-size);
  text-transform: none !important;
  letter-spacing: 0;
  font-variant: initial;
  line-height: 1
}

.swiper-button-prev-pro:after,
.swiper-rtl .swiper-button-next-pro:after {
  content: 'prev'
}

.swiper-button-next-pro,
.swiper-rtl .swiper-button-prev-pro {
  right: var(--swiper-navigation-sides-offset, 10px);
  left: auto
}

.swiper-button-next-pro:after,
.swiper-rtl .swiper-button-prev-pro:after {
  content: 'next'
}

.md-rb-head h4 {
  margin-right: 62px;
  font-size: 19px;
}

/*profile report modal */
.pr-content ul {
  margin: 35px 0 10px;
}

.pr-content ul li {
  border-bottom: 1px solid #D1D1D1;
  padding-bottom: 20px;
  margin-bottom: 20px;
}

.pr-content ul li:last-child {
  border-bottom: none;
  margin: 0;
  padding: 0;
}

.pr-content .pr-radiobtn {
  justify-content: space-between;
  padding-left: 0;
}

.pr-content .pr-radiobtn .cust-check label {
  margin-left: 0;
}

.pr-content .pr-radiobtn label p {
  color: #000000;
  font-size: 17px;
}

/* profile review modal */
.md-gr-tb {
  padding-bottom: 15px !important;
}

.md-gr-tb .nav-item:first-child .nav-link {
  margin-left: 0 !important;
}

.md-gr-tbContent .reviews {
  border-bottom: 1px solid #D1D1D1;
}

.md-gr-tbContent .reviews:last-child {
  border-bottom: none;
}

.md-gr-tbContent .pr-img img {
  width: 50px;
  height: 50px;
}

.md-rev-tb-img img {
  width: 90px;
  height: 55px;
  border-radius: 10px;
  object-fit: cover;
}

.md-gr-tbContent .pr-mini-detail {
  flex: 1;
}

.md-hr-tbContent .reviews {
  margin-bottom: 19px;
}

.single-review-count {
  display: inline-block;
  font-size: 17px;
  margin-left: 7px;
  position: relative;
}

.single-review-count::before {
  content: "";
  position: absolute;
  top: 48%;
  left: -4px;
  background-color: gray;
  transform: translateY(-50%);
  height: 3px;
  width: 3px;
  border-radius: 50%;
  transition: .3s all;
}

.shadow-tabs .nav-pills .nav-link:hover .single-review-count::before {
  background-color: #000000;
}

.shadow-tabs .nav-pills .nav-link.active .single-review-count::before {
  background-color: #000000;
}

.copiedLink {
  background-color: #EDEDED;
  border: 1px solid #EDEDED;
}

.copiedLink:hover {
  background-color: #EDEDED;
  border: 1px solid #EDEDED;
}

#phoneError {
  width: 100%;
  margin-top: 0.25rem;
  font-size: .875em;
  color: #dc3545;
}

#otpError {
  width: 100%;
  margin-top: 0.25rem;
  font-size: .875em;
  color: #dc3545;
}

/*resevation reviews modal*/
/* .reserv-reviews{
  border-bottom: 1px solid #D1D1D1;
} */
.bg-theme-color {
  background-color: var(--theme-primary);
}

.rev-descrip p {
  border-radius: 5px;
  color: #fff;
  font-size: 14px !important;
}

.reserv-prop-img img {
  height: 130px !important;
}

.reserv-lvl {
  margin: 15px 0;
}

.reserv-input-lvl {
  height: 33px;
  width: 100%;
}

.reserv-lvl-content p {
  font-size: 13px;
  font-weight: 500;
  color: #000000;
}

.md-back-btn img {
  width: 22px;
}

.otp-in-text {
  color: #000000 !important;
}

.mdReviewSlider .swiper-wrapper,
.mdUlSlider .swiper-wrapper {
  height: auto !important;
}




.price-btn span {
  font-weight: 500;
  font-size: 16px;
}

.price-btn p {
  color: #8A8A8A;
  font-size: 14px;
}

.phone-verification {
  padding: 0;
}

.phone-verification p {
  font-size: 14px;
  margin-bottom: 0;
  color: #909090;
  line-height: 1.3
}

.otp-title {
  font-size: 20px;
}

.phone-number {
  border: 1px solid #e2e2e2;
  border-radius: 25px;
  display: flex;
  align-items: center;
  padding: 0 15px;
  height: 44px;
}

.wishlist {
  padding: 20px 0
}

/* inbox */
.chat-mb-screen {
  position: absolute;
  background: #F9FAFC;
  top: 0;
  right: -200%;
  width: 100%;
  height: 100%;
  z-index: 200;
  transition: .4s ease-out;
  overflow: auto;
}

.tab-content {
  height: 100%;
  overflow: auto;
}

/* .input-pf{
  position: fixed;
  bottom: 0;
  width: 100%;
} */
.chat-scroll-hide {
  animation: none !important;
  overflow: hidden !important;
}

/*acount page style */
.acc-bd-bottom {
  border-bottom: 1px solid var(--grey-one);
}

.acc-inner-group {
  padding: 20px 0;
  border-bottom: 1px solid var(--grey-one);
}

/* .acc-inner-group:nth-child(4) .mb-account-layout .mb-al-img{
  width: 18px;
} */
.acc-inner-group .account-inner .mb-account-layout.hs-ac-btn svg {
  margin-right: 14px;
}

.account-inner {
  display: block;
  margin-bottom: 12px;
}

.mb-user-acc {
  display: flex;
  align-items: center;
  position: relative;
}

.mb-user-acc .mb-user-img {
  width: 54px;
  height: 54px;
  margin-right: 20px;
  border-radius: 50%;
  object-fit: cover;
}

.mb-user-content {
  flex: 1;
  margin-right: 25px;
}

.mb-user-content h6 {
  font-size: 18px;
}

.mb-right-arrow {
  position: absolute;
  right: 5px;
}

.mb-right-arrow img {
  width: 12px;
}

.mb-account-layout {
  display: flex;
  align-items: center;
  position: relative;
}

.mb-account-layout .mb-al-img {
  margin-right: 20px;
  width: 25px;
}

.mb-al-icon {
  font-size: 27px;
  color: #000000;
  margin-right: 11px;
}

.mb-al-content p {
  color: #000000;
  margin-bottom: 0;
  font-size: 18px;
  text-transform: capitalize;
}

.md-vp-box {
  border-radius: 12px;
}

.md-vp-box img {
  width: 65px;
  height: 65px;
  object-fit: cover;
  border-radius: 50%;
}

.md-vp-box h2 {
  margin: 10px 0 5px;
}

.profile-view p {
  color: #4E4E4E;
}

.edit-btn {
  font-size: 18px;
  text-decoration: underline;
  color: #000000;
  font-weight: 500;
  cursor: pointer;
  z-index: 9999;
}

.mb-pro-info {
  padding: 30px 0;
  margin-bottom: 50px;
}

.edit-btn:hover {
  color: #000000;
}

.acc-inner ul li .ac-dt p {
  margin: 0 0 15px;
  font-size: 14px !important;
  font-weight: 500;
}

.mb-acc-tick {
  margin-right: 15px;
}

.ac-act a {
  font-weight: 400 !important;
}

.total-credit-detail {
  width: 100%;
}

.card-logo {
  flex: 0 0 17%;
  max-width: 17%
}

.card-detail .payment-btn {
  margin-top: 20px
}

.cd-darent img {
  margin: 7px 18px 0 0;
}

.account-main {
  margin-bottom: 85px;
}

.mb-mtop-inner {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 30px 0;
}

.mb-mtop-inner li a {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  padding: 8px 10px;
  border-radius: 10px;
  color: #000000;
  transition: .3s ease-in-out;
}

.mb-mtop-inner li a:hover {
  box-shadow: 2.5px 3.5px 15px rgba(149, 157, 165, 0.2);
  background: #fff;
  color: #000;
  font-weight: 500;
}

.mb-mtop-inner li:last-child a {
  margin-right: 0;
}

.mb-mtop-inner li a i {
  font-size: 23px;
}

.mb-mtop-inner li a span {
  display: inline-block;
  margin-left: 10px;
}

.on-pg {
  box-shadow: 2.5px 3.5px 15px rgba(149, 157, 165, 0.2);
  background: #fff;
  color: #000;
  font-weight: 500;
}

.tickets_files {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

section.trans-table {
  padding: 0;
}

.account-detail {
  padding-top: 20px;
}

/* .pr-img img{
  width: 52px;
  height: 52px;
} */
.cm-group .chat-property-media img {
  margin: 10px 0 0 55px;
}

.nchat-profile-list .chat-head .chat-logo {
  margin: 0 7px 0 0;
}

/*listing screen */
.new-listing {
  height: 100vh;
  overflow: hidden;
}

.pay-sec {
  margin: 0 4px 20px 0;
  position: relative;
}

.pay-sec p {
  color: #000000;
}

/*contact host page*/
.contact-host-sec {
  padding: 25px 0 0;
  position: relative;
}

.ch-host-profile {
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid var(--grey-one);
  padding-bottom: 20px;
}

.ch-sec-pcontent {
  flex: 1;
}

.ch-sec-pcontent h2 {
  color: #181818;
}

.ch-sec-pcontent p {
  font-size: 18px;
  margin-bottom: 0;
}

.ch-sec-profile img {
  width: 65px;
  margin-left: 20px;
}

.mobile-property-pricing {
  padding: 25px 0 !important;
  border: none;
  margin-top: 0;
}

.ch-host-message {
  margin: 26px 0 20px;
}

.mobile-property-pricing {
  font-size: 18px;
}

.mobile-property-pricing .property-list {
  font-size: 16px !important;
}

.ch-host-message h2 {
  color: #181818;
}

.ch-host-message textarea {
  resize: none;
  height: 200px !important;
  margin: 10px 0 0px !important;
}

.ch-host-message textarea::placeholder {
  font-size: 18px;
}

.ch-host-btns {
  display: flex;
  align-items: center;
  justify-content: end;
}

.ch-hb-btn {
  display: flex;
  align-items: center;
}

.ch-hb-btn img {
  width: 10px;
  margin: 0 8px 2px 0;
}

.ch-host-btns button,
.ch-host-btns a {
  padding: 0 20px;
  height: 52px;
  font-size: 20px;
}

.ch-host-btns a:hover {
  color: #000000;
}

.mobile-property-pricing .custom-subtotal {
  max-height: 166px;
}

.pay-sec .pay-sec-label input[type="radio"] {
  height: 20px;
  width: 20px;
  margin-right: 10px;
  filter: grayscale(100%) !important;

}

.pay-sec .pay-sec-label {
  cursor: pointer;
  width: 100%;
  border: 1px solid #d1d1d1;
  border-radius: 6px;
  display: flex;
  align-items: center;
  padding: 10px 15px;
}

/* .pay-sec .pay-sec-label .pay-sl-content {
  display: flex;
  padding: 6px 5px;
  border: 2px solid #e1e2e7;
  border-radius: 8px;
  position: relative;
  align-items: center;
  justify-content: center;
  transition: .3s ease-in-out;
} */
.pay-sec .pay-sec-label .pay-sl-content img {
  /* margin-right: 5px; */
  height: 20px;
  object-fit: cover;
  width: auto;
  transition: .3s ease-in-out;
}

.pay-sec .pay-sec-label .pay-sl-cdetail span {
  display: block;
  font-size: 15px;
  color: var(--them-secondary);
  font-weight: 500;
  transition: .3s ease-in-out;
}

/* .pay-sec .pay-sec-label .pay-sl-content:hover {
  border: 2px solid #181818;
}
.pay-sec .pay-sec-label .pay-sl-content:hover img{
  transform: scale(1.1);
} */
.pay-sec .pay-sec-label .pay-sl-content:hover .pay-sl-cdetail span {
  color: #181818;
}

.pay-sec .pay-sec-label input[type="radio"]:checked+.pay-sec-label {
  border: 2px solid #181818;
  background: #ededed63;
  -webkit-transition: ease-in 0.3s;
  -o-transition: ease-in 0.3s;
  transition: ease-in 0.3s;
}

/* .pay-sec .pay-sec-label input[type="radio"]:checked + .pay-sl-content img{
  filter: brightness(0);
  transform: scale(1.1);
} */
/* .pay-sec .pay-sec-label input[type="radio"]:checked + .pay-sl-content .pay-sl-cdetail span{
  color: #181818;
} */
.pay-sec.reserv-pay-sec {
  margin: 0 0 15px;
}

/* .pay-sec.reserv-pay-sec .pay-sec-label {
  width: 100%;
} */
.pay-sl-content {
  display: flex;
  align-items: center;
}

.host-page-foot {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px;
}

.host-page-foot p {
  text-align: center;
  font-size: 18px;
  color: #181818;
  font-weight: 500;
  line-height: 1.2;
}

.host-page-foot p span {
  display: block;
}

/*scroll chat screen host message*/
.hst-msg-btn {
  height: 52px;
  font-weight: 500;
  color: #181818;
}

.hst-msg-screen {
  background-color: #fff !important;
  position: fixed;
}

.hm-screen-inner {
  padding: 20px 15px 0;
  overflow: auto;
  height: calc(100vh - 65px);
}

.hst-msg-head {
  padding: 10px 0;
  box-shadow: 6px 4px 16px #0000000D;
}

.hst-msg-body {
  padding-bottom: 80px;
}

.hst-msg-prop {
  display: flex;
}

.hm-prop-content {
  flex: 1;
}

.hm-prop-content h3 span {
  font-weight: 400;
  font-size: 19px;
}

.hm-prop-content p {
  color: #000000;
}

.hm-prop-img img {
  width: 100px;
  border-radius: 5px;
  object-fit: cover;
}

.hm-prop-content ul {
  display: flex;
  align-items: center;
}

.hm-prop-content ul li {
  position: relative;
  padding-right: 10px;
  font-size: 14px;
  display: flex;
  align-items: center;
}

.hm-prop-content ul li img {
  margin: 0 2px 2px 0;
  width: 11px;
  object-fit: cover;
}

.hm-prop-content ul li:nth-child(2) span {
  margin-right: 1px;
  display: inline-block;
}

.hm-prop-content ul li:nth-child(2)::before,
.hm-prop-content ul li:nth-child(2)::after {
  content: '';
  position: absolute;
  top: 51%;
  transform: translateY(-50%);
  width: 2px;
  height: 2px;
  background-color: #000;
  border-radius: 50%;
}

.hm-prop-content ul li:nth-child(2)::before {
  left: -6px;
}

.hm-prop-content ul li:nth-child(2)::after {
  right: 4px;
}

.mt-ask-about {
  /* border-bottom: 1px solid var(--grey-one); */
  padding: 20px 0;
}

.mt-ask-about h3 {
  color: #181818;
}

.mt-ask-content {
  margin-top: 25px;
}

.mt-ask-content h5 {
  font-size: 18px;
  color: #181818;
}

.mt-ask-content p {
  position: relative;
  padding-left: 15px;
  margin: 0 0 0 20px;
}

.mt-ask-content p::before {
  content: "";
  position: absolute;
  left: 0;
  width: 5px;
  height: 5px;
  background-color: var(--them-secondary);
  border-radius: 50%;
  top: 50%;
  transform: translateY(-50%);
}

.conthst-date {
  padding: 20px 0;
}

.conthst-date h6 {
  color: #181818;
}

.conthst-date span {
  color: var(--theme-primary);
}

.footer-contact-host {
  background: #ffff;
  padding: 10px;
  border: 1px solid #00000017;
  z-index: 99;
  text-align: center;
  border-radius: 10px 10px 0px 0px;
  width: 100%;
  left: 0;
  bottom: 0;
  box-shadow: 1px 5px 7px 3px #0000000d;
  position: fixed;
  bottom: 0;
}

@media (max-width: 767px) {

  /*start hosting page height*/
  .sh-mb-left {
    padding: 20px 20px !important;
    height: 65vh !important;
  }

  .sh-mb-right {
    height: 35vh !important;
  }

  /*start hosting page height end*/
  .new-listing .left-side {
    padding: 15px 28px 0;
    height: 20vh;
  }

  .new-listing .right-side {
    padding: 15px 15px 0;
    height: 80vh;
  }

  .new-listing .left-side .left-sec-content h1 {
    font-size: 30px;
  }

  .right-inner-side {
    height: 100%;
  }

  /*
  .right-content {
    padding: 0;
    height: 85%;
    margin-bottom: 10px;
    overflow-y: auto;
    padding: 0 10px;
    overflow-x: hidden !important;
  } */

  .add-lst-content {
    text-align: left !important;
  }

  .new-listing .right-side .add-lst-content h1 {
    font-size: 30px;
    line-height: 1.3;
  }

  /* .listing-footer {
    left: 0;
    padding: 15px;
    height: auto;
  } */

  .listing-footer .theme-btn {
    padding: 0 35px;
  }

  .listing-category .category {
    margin-bottom: 18px;
  }

  .listing-category .category .category-content {
    padding: 22px 20px;
    border-radius: 20px;
  }

  .category-content {
    transition: .3s ease-in-out;
  }

  .listing-category .category .category-content img {
    transition: .3s ease-in-out;
  }

  .listing-category .category input[type=radio]:checked+.category-content {
    background-color: #fff;
  }

  .listing-footer .black-btn img {
    width: 9px;
    margin-right: 4px;
  }

  .listing-footer .black-btn a {
    display: flex;
    align-items: center;
  }

  .left-inner-side {
    justify-content: start;
  }

  .listing-checkbox {
    width: calc(100% + 10px);
    margin: 0 -12px 30px 0;
  }

  .listing-checkbox-wrapper {
    width: 88px;
    height: 84px;
    margin: 5px 12px 10px 0;
  }

  .lc-inner {
    display: flex;
    flex-flow: wrap;
    justify-content: space-evenly;
  }

  .upload-image-half-main {
    flex-flow: wrap;
  }

  .upload-image-half {
    margin-bottom: 10px;
  }

  .upload-image-half-main {
    margin-left: -10px;
  }

  .md-back-btn img {
    width: 11px;
  }

  .host-slider .swiper-button-next,
  .host-slider .swiper-button-prev {
    width: 40px;
    height: 40px;
    background: #ffff;
    border-radius: 80px;
    box-shadow: 1px 3px 5px 1px #0000002b;
    padding: 5px;
    align-items: center;
  }

  .host-slider .swiper-button-next:after,
  .host-slider .swiper-button-prev:after {
    font-size: 18px;
    color: #000000;
  }

  /*product slider btn prev next*/
  .swiper-button-hp-next,
  .swiper-button-hp-prev {
    position: absolute;
    top: var(--swiper-navigation-top-offset, 50%);
    width: 40px;
    height: 40px;
    background: #ffff;
    border-radius: 80px;
    box-shadow: 1px 3px 5px 1px #0000002b;
    padding: 5px;
    align-items: center;
    z-index: 10;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .swiper-button-hp-next.swiper-button-disabled,
  .swiper-button-hp-prev.swiper-button-disabled {
    opacity: .35;
    cursor: auto;
    pointer-events: none
  }

  .swiper-button-hp-next.swiper-button-hidden,
  .swiper-button-hp-prev.swiper-button-hidden {
    opacity: 0;
    cursor: auto;
    pointer-events: none
  }

  .swiper-navigation-disabled .swiper-button-hp-next,
  .swiper-navigation-disabled .swiper-button-hp-prev {
    display: none !important
  }

  .swiper-button-hp-next svg,
  .swiper-button-hp-prev svg {
    width: 100%;
    height: 100%;
    object-fit: contain;
    transform-origin: center
  }

  .swiper-rtl .swiper-button-hp-next svg,
  .swiper-rtl .swiper-button-hp-prev svg {
    transform: rotate(180deg)
  }

  .swiper-button-hp-prev,
  .swiper-rtl .swiper-button-hp-next {
    left: var(--swiper-navigation-sides-offset, 10px);
    right: auto
  }

  .swiper-button-hp-next,
  .swiper-rtl .swiper-button-hp-prev {
    right: var(--swiper-navigation-sides-offset, 10px);
    left: auto
  }

  .swiper-button-lock {
    display: none
  }

  .swiper-button-hp-next:after,
  .swiper-button-hp-prev:after {
    font-family: swiper-icons;
    text-transform: none !important;
    letter-spacing: 0;
    font-variant: initial;
    line-height: 1;
    font-size: 18px;
    color: #000000;
  }

  .swiper-button-hp-prev:after,
  .swiper-rtl .swiper-button-hp-next:after {
    content: 'prev'
  }

  .swiper-button-hp-next,
  .swiper-rtl .swiper-button-hp-prev {
    right: var(--swiper-navigation-sides-offset, 10px);
    left: auto
  }

  .swiper-button-hp-next:after,
  .swiper-rtl .swiper-button-hp-prev:after {
    content: 'next'
  }


  /*customer services*/
  .customer-ser-sec {
    margin-top: 30px;
  }

  .d-agent {
    display: flex;
    align-items: center;
    border-bottom: 1px solid #e1e1e1;
    padding-bottom: 20px;
    margin-bottom: 28px;
  }

  .d-agent-img {
    width: 75px;
    height: 75px;
    background: var(--theme-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    margin-right: 15px;
  }

  .d-agent-img img {
    width: 50%;
    margin-left: 6px;
  }

  .d-agent-content {
    flex: 1;
  }

  .d-agent-content p {
    line-height: 1.3;
  }

  .cs-chat-main {
    overflow-y: auto;
    padding-right: 5px;
    height: 60vh;
  }

  .cs-chat-field {
    position: fixed;
    bottom: 0;
    width: 95%;
    left: 50%;
    transform: translateX(-50%);
    padding: 10px 0;
    background: #fff;
  }

  .cs-chat-field input {
    width: 100%;
    height: 60px;
    position: relative;
    padding: 12px 55px 12px 12px;
  }

  .cs-chat-field input::placeholder {
    font-size: 16px;
  }

  .cs-cf-btn {
    position: absolute;
    bottom: 20px;
    right: 10px;
  }

  .cs-chat-main::-webkit-scrollbar-thumb {
    background-color: #DBE5ED;
    border-radius: 25px;
  }

  .cs-chat-main::-webkit-scrollbar-track {
    border-radius: 0;
    background-color: transparent;
    box-shadow: none;
    border-radius: none;
  }

  .chat-date span {
    display: inline-block;
  }

  .pdf-title {
    font-size: 13px;
  }

  .pdf-detail {
    font-size: 12px;
  }

  .receipt-main .card-body {
    padding: 10px 5px;
  }

  .receipt-logo {
    width: 45%;
    margin: 10px;
  }

  .receipt-main .card-header {
    padding: 5px;
  }

  .receipt-main table th,
  .receipt-main table td {
    font-size: 14px;
  }

  .receipt-title {
    margin: 20px 0;
    font-size: 18px;
  }

  .rm-mobile-banner img {
    width: 100%;
  }

  .second-home {
    /* background: #f5c33e; */
    /* height: 600px; */
    position: relative;
    /* background-image: url(../images/mb-second-home.png);
    background-size: cover;
    background-position: center; */
    margin: 50px 0;
  }

  .sh-banner-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  /* .sc-home{
    position: absolute;
    top: 50%;
    width: 50%;
    right: 100px;
    transform: translateY(-50%);
  } */
  .back-line {
    font-size: 18px;
    color: #000000;
    font-weight: 500;
    position: relative;
    margin-left: 0;
    max-width: max-content;
    margin-bottom: 10px;
    margin-right: auto;
  }

  .back-line::before {
    content: "";
    position: absolute;
    bottom: 5px;
    right: 0;
    background-color: #fff;
    width: 100%;
    height: 8px;
    z-index: -1;
  }

  .sc-home p {
    font-size: 14px;
    color: #000000;
    margin-bottom: 0;
  }

  .second-home .sc-home {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    padding: 0 12px;
    margin-right: 100px;
    z-index: 99;
  }

  .second-home-img {
    width: 100%;
  }

  .sh-img-ar {
    display: none;
  }

  .download-app {
    background-color: var(--theme-primary);
    margin: 130px 0 50px;
    border-radius: 10px;
  }

  .downApp-inner {
    position: relative;
    z-index: 99;
    text-align: center;
  }

  .downApp .back-line::before {
    bottom: 1px;
  }

  .downApp {
    display: flex;
    align-items: center;
    justify-content: start;
    padding: 15px 0 40px;
  }

  .sh-dlogo {
    width: 100px;
    margin-bottom: 11px;
  }

  .downApp-inner .back-line {
    margin: 0 auto 10px auto;
    font-size: 20px;
  }

  .downApp-inner p {
    color: #000000;
    font-size: 22px;
    font-weight: 500;
  }

  .fea-btn img {
    width: 90%;
  }

  .downApp-img img {
    width: 100%;
    margin-top: -180px;
    object-fit: cover;
  }

  /**** Dashboard Css  ****/
  .review-se {
    display: block;
    margin-left: auto;
    color: #000000;
    height: 44px;
    width: 130px;
    font-size: 17px;
    color: var(--grey-three);
    border: 2px solid var(--dim-gray);
    border-radius: 15px;
    background-color: transparent;
    font-weight: 500;
  }

  /*new reviews start*/
  #review-step-stepper {
    width: 100%;
    position: relative;
    margin: 20px 0 75px;
    overflow: auto;
  }

  #review-step-stepper:before {
    content: "";
    position: fixed;
    left: 0;
    bottom: 68px;
    height: 3px;
    width: 100%;
    z-index: 1;
    background-color: #d8d8d8;
    transition: transform 0.5s ease;
  }

  #review-step-stepper:after {
    content: "";
    position: fixed;
    left: 0;
    bottom: 68px;
    height: 3px;
    z-index: 1;
    width: calc(100% / 6);
    background-color: #333333;
    transform: scaleX(1);
    transform-origin: left center;
    transition: transform 0.5s ease, -webkit-transform 0.5s ease;
  }

  #review-step-stepper.step-2:after {
    transform: scaleX(2);
    -webkit-transform: scaleX(2);
  }

  #review-step-stepper.step-3:after {
    transform: scaleX(3);
    -webkit-transform: scaleX(3);
  }

  #review-step-stepper.step-4:after {
    transform: scaleX(4);
    -webkit-transform: scaleX(4);
  }

  #review-step-stepper.step-5:after {
    transform: scaleX(5);
    -webkit-transform: scaleX(5);
  }

  #review-step-stepper.step-6:after {
    transform: scaleX(6);
    -webkit-transform: scaleX(6);
  }

  .review-step-footer {
    width: 100%;
    position: fixed;
    bottom: 0;
    background: #FAFAFA;
    padding: 10px;
  }

  .review-step-footer-inner {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .review-step-footer .rs-theme-btn {
    display: inline-block;
    background-color: var(--theme-primary);
    color: #fff;
    padding: 10px 35px;
    border-radius: 8px;
    font-weight: 700;
    border: 2px solid var(--theme-primary);
    transition: 0.3s ease;
    cursor: pointer;
    text-align: center;
    font-size: 17px;
    letter-spacing: .5px;
  }

  .review-steps {
    display: none;
    animation: fadeIn 0.5s ease forwards;
    transition: .3s ease-in-out;
    padding: 25px 0 5px;
  }

  /*new start review step 1*/
  .review-step-start {
    /* display: flex;
  flex-direction: column;
  align-items: start;
  justify-content: start; */
    padding-right: 5px;
  }

  .review-step-center {
    justify-content: center;
  }

  .rs-fs-title {
    font-size: 30px;
    color: #181818;
    font-weight: 600;
  }

  .rs-fs-title-child {
    font-size: 24px;
    color: #181818;
    font-weight: 600;
    margin-bottom: 15px;
  }

  .rs-fs-content {
    font-size: 20px;
    color: #A3A3A3;
    font-weight: 500;
  }

  .rs-fs-content-child {
    font-size: 20px;
    color: #181818;
    margin-bottom: 10px;
    line-height: 1.2;
  }

  .rs-fs-cc-another {
    color: #A3A3A3;
    font-size: 18px;
  }

  /*section*/
  .rs-start-sec .rs-start-sec-inner img {
    width: 100px;
    margin-bottom: 30px;
    box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
  }

  .rs-start-sec .rs-start-sec-inner h1 {
    margin-bottom: 25px;
  }

  .rs-start-sec .rs-start-sec-inner p {
    line-height: 1.3;
    margin-bottom: 30px;
    letter-spacing: .5px;
  }

  #start-review-loader {
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background: #fff;
    z-index: 99999;
  }

  #start-review-loader img {
    position: absolute;
    top: 50%;
    transform: translate(-50%, -50%);
    left: 50%;
  }

  .rs-rating-star .star-rating label {
    font-size: 2.2rem;
    padding-right: 10px;
  }

  .rs-second-sec .rs-star-rating img {
    width: 70px;
    margin-bottom: 25px;
  }

  .rs-second-sec .rs-star-rating h1 {
    margin-bottom: 15px;
  }

  .rs-second-sec .rs-star-rating .star-rating {
    margin-bottom: 10px;
  }

  .sr-checks-input .listing-checkbox {
    margin: 0;
    width: auto;
  }

  .sr-checks-input .listing-checkbox-wrapper {
    width: max-content;
    height: max-content;
    margin: 0 8px 12px 0;
  }

  .sr-checks-input .listing-checkbox-wrapper .listing-checkbox-tile {
    padding: 5px 15px;
    border-radius: 25px;
    border: 1px solid #707070;

  }

  .sr-checks-input .listing-checkbox-wrapper .listing-checkbox-tile .listing-checkbox-label {
    height: auto;
    margin: 0;
  }

  .sr-checks-input .listing-checkbox-wrapper .listing-checkbox-tile .listing-checkbox-content {
    color: #8A8A8A;
    font-weight: 500;
    font-size: 16px;
  }

  .sr-checks-input .listing-checkbox-input:checked+.listing-checkbox-tile .listing-checkbox-content {
    color: #181818;
  }

  .sr-checks-input .listing-checkbox-wrapper .listing-checkbox-input:checked+.listing-checkbox-tile {
    border-color: #181818;
    background-color: #FAFAFA;
    box-shadow: none;
  }

  .rs-clean-field {
    margin-top: 8px;
    visibility: hidden;
    opacity: 0;
  }

  .rs-clean-field input,
  .rs-ra-textarea textarea {
    height: 60px;
    border-radius: 13px;
    border: 1px solid #707070;
  }

  .rs-clean-field input::placeholder {
    color: #8A8A8A;
    font-weight: 600;
    font-size: 18px;
  }

  .rs-ra-textarea textarea::placeholder {
    color: #D1D1D1;
    font-size: 18px;
  }

  .rs-clean-field .cc-display,
  .rs-ra-textarea .cc-display {
    color: #181818;
    font-size: 16px;
  }

  .rs-review-add {
    width: 100%;
  }

  .rs-ra-textarea {
    width: 100%;
  }

  .rs-ra-textarea textarea {
    resize: none;
    height: 200px !important;
  }

  .rs-review-add img {
    width: 60px;
    margin-bottom: 25px;
  }

  .rs-or-inner.sr-checks-input .listing-checkbox-wrapper .listing-checkbox-tile {
    padding: 8px 40px;
  }

  .rs-overall-review img {
    width: 60px;
    margin-bottom: 25px;
  }

  .rs-review-add .rs-fs-content-child {
    margin-bottom: 25px;
  }

  .rs-or-inner {
    margin-top: 15px;
  }

  @keyframes fadeIn {
    0% {
      opacity: 0;
    }

    100% {
      opacity: 1;
    }
  }

  /*new reviews start end*/
  /* day */
  .day-reservation-sec {
    padding-top: 40px;
  }

  .dr-main-title {
    color: var(--dr-shade-gray);
    margin-bottom: 25px;
    font-size: 25px;
  }

  .dr-md-title {
    color: var(--dr-shade-gray);
    font-size: 22px;
  }

  .dr-sh-right {
    margin: 0 0 20px;
  }

  .dr-sh-right-cont:hover {
    color: var(--dr-shade-gray);
  }

  .dr-sh-right-cont span {
    font-weight: 500;
    color: var(--dr-shade-gray);
    font-size: 18px;
  }

  .dr-cust-arrow-btn button {
    border: 1px solid #707070;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .dr-cust-prev {
    margin-right: 10px;
  }

  .dr-cust-arrow-btn button i {
    color: var(--dr-shade-gray);
    font-size: 20px;
  }

  .day-reservation-tabbing .dr-tabs {
    border-bottom: 0;
    margin: 25px 0;
  }

  .day-reservation-tabbing .dr-tabs .nav-link {
    border: 1px solid var(--dim-gray);
    border-radius: 25px;
    padding: 7px;
    color: var(--dr-shade-gray);
    font-weight: 500;
    font-size: 16px;
    margin-right: 15px;
    margin: 5px 10px 5px;
    width: 95%;
  }

  .day-reservation-tabbing .dr-tabs .nav-link.active {
    background-color: #FAFAFA;
    border: 2px solid var(--dr-shade-gray);
  }

  .day-reservation-tabbing .tab-pane {
    margin-bottom: 0;
  }

  .day-reserv-box {
    border: 1px solid #D1D1D1;
    padding: 10px 0 0;
    border-radius: 10px;
    margin-bottom: 20px;
  }

  .ht-btn-main button {
    margin: 0 0 0 20px;
  }

  .ht-btn-main button:last-child {
    margin-left: 0;
  }

  .dr-box-head {
    padding: 0 10px;
    justify-content: space-between;
  }

  .dr-box-head h6 {
    color: #FF1111;
    font-size: 16px;
  }

  .dr-box-head img {
    width: 60px;
    margin: 0 0 10px 5px;
    height: 60px;
  }

  .dr-box-content {
    padding: 10px;
  }

  .dr-box-content ul {
    margin: 0;
  }

  .dr-box-content ul li span {
    color: var(--dr-shade-gray);
    font-size: 18px;
    font-weight: 500;
    display: inline-block;
  }

  .dr-box-content ul li:last-child span {
    color: var(--grey-three);
    margin-top: 10px;
    font-size: 16px;
  }

  .dr-box-foot {
    border-top: 1px solid #D1D1D1;
    padding: 10px;
    text-align: center;
  }

  .dr-box-foot a {
    display: block;
    color: var(--dr-shade-gray);
    font-size: 20px;
    font-weight: 500;
  }

  .dr-box-foot .content-skeleton {
    margin: 5px auto;
    padding: 12px 0;
  }

  .avatar-skeleton {
    width: 70px;
    height: 70px;
    background: linear-gradient(90deg, var(--loader-background-color) 25%, var(--loader-highlight-color) 50%, var(--loader-background-color) 75%);
    background-size: 200% 100%;
    animation: loading 2s infinite ease-in-out;
    border-radius: 50%;
    margin-bottom: 20px;
  }

  .content-skeleton {
    width: 150px;
    height: 15px;
    background: linear-gradient(90deg, var(--loader-background-color) 25%, var(--loader-highlight-color) 50%, var(--loader-background-color) 75%);
    background-size: 200% 100%;
    animation: loading 2s infinite ease-in-out;
    margin-bottom: 10px;
  }

  @keyframes loading {
    0% {
      background-position: 200% 0;
    }

    100% {
      background-position: -200% 0;
    }
  }

  .reservation-nodata {
    background-color: rgb(247, 247, 247);
    min-height: 220px;
    max-height: max-content;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    border-radius: 10px;
    margin-bottom: 22px;
  }

  .reserv-nd-inner {
    width: 200px;
    text-align: center;
  }

  .reservation-nodata img {
    width: 50px;
    margin-bottom: 20px;
  }

  .reservation-nodata P {
    color: var(--dr-shade-gray);
    line-height: 1.3;
    font-size: 18px;
    margin-bottom: 0;
  }

  .day-reservation-tabbing .tab-content {
    overflow: inherit;
  }

  .reminder-cards-sec {
    background-color: #F9F7F4;
    padding: 40px 0;
  }

  .reminder-cs-title {
    color: var(--dr-shade-gray);
    padding: 0 0 30px;
  }

  .reminder-card {
    background-color: #fff;
    border-radius: 10px;
    padding: 20px 25px;
    position: relative;
    height: 100%;
    margin-right: 10px;
  }

  .rc-cancel img {
    width: 11px;
  }

  .rc-profile {
    width: 30px;
  }

  .rc-card-content {
    margin: 8px 0 50px 0;
  }

  .rc-card-content h6 {
    color: var(--dr-shade-gray);
    font-size: 20px;
    margin: 15px 0;
  }

  .rc-card-content p {
    color: var(--dr-shade-gray);
    font-weight: 400;
    font-size: 16px;
  }

  .rc-cc-unique {
    color: #A3A3A3;
    font-size: 16px;
    display: inline-block;
    font-weight: 500;
    margin-bottom: 10px;
  }

  .rc-card-foot {
    position: absolute;
    bottom: 20px;
  }

  .rc-card-button {
    border: 1px solid var(--dim-gray) !important;
    color: var(--dim-gra);
    transition: .3s ease-in-out;
    padding: 0 18px;
    height: 40px;
    font-size: 18px;
    border-radius: 7px;
    font-weight: 500;
    margin-right: 10px;
  }

  .rc-card-button:hover {
    background-color: #000000;
    color: #fff;
  }

  .grill-icon {
    width: 22px;
  }

  .all-reservation-sec {
    margin-top: 40px;
  }

  .all-reserv-head {
    margin: 20px 0 60px;
  }

  .pm-back-btn {
    width: 14px;
  }

  .all-reservation-sec .nav-tabs {
    margin: 30px 0;
  }

  .reminder-slider .slick-next {
    right: 0px !important;
  }

  .reminder-slider .slick-prev {
    left: auto !important;
    right: 40px;
  }

  .reminder-slider .slick-prev,
  .reminder-slider .slick-next {
    top: -19%;
    border: 1px solid var(--dim-gray);
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: transparent;
  }

  .reminder-slider .slick-prev:before,
  .reminder-slider .slick-next:before {
    font-size: 12px;
    color: #181818;
  }

  .reminder-slider .slick-prev.slick-disabled,
  .reminder-slider .slick-next.slick-disabled {
    opacity: .5;
  }

  .reminder-slider .slick-prev.slick-disabled:before,
  .reminder-slider .slick-next.slick-disabled:before {
    opacity: .5;
  }

  /* all reservation */
  .hs-line-tabbing .nav-tabs {
    border-bottom: none;
  }

  .hs-line-tabs .nav-link {
    border: none;
    color: black;
    font-family: 'dubai-font';
    font-size: 18px;
    padding: 10px 7px;
    font-weight: 500;
    height: 50px;
    position: relative;
    margin-right: 15px;
    color: #A3A3A3;
    transition: .3s ease-in-out;
  }

  .hs-line-tabs .nav-link::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 2px;
    bottom: 0px !important;
    left: 0;
    background: #A3A3A3;
    visibility: visible;
    opacity: 1;
    transition: .3s ease-in-out;

  }

  .hs-line-tabs .nav-link:hover {
    color: var(--dr-shade-gray);
  }

  .hs-line-tabs .nav-link.active::before {
    background: var(--dr-shade-gray);
    color: var(--dr-shade-gray);
    visibility: visible;
    opacity: 1;
  }

  .all-reservation-sec .hs-line-tabs .nav-link {
    font-size: 16px;
    margin-right: 10px;
  }

  /* host modal */
  /* host detail modal */
  .mdl-hostreserv-width {
    max-width: 600px;
  }

  .hs-custdetail-title-mg {
    margin-bottom: 25px;
  }

  .hs-custdetail-fwc {
    font-weight: 500;
    font-size: 17px;
  }

  .hs-cust-paddingtb-border {
    padding: 20px 0;
    border-bottom: 10px solid #FAFAFA;
  }

  .hs-cust-paddinglr {
    padding-left: 20px;
    padding-right: 20px;
  }

  .host-bg-trans-btn {
    background-color: transparent;
    border: 1px solid var(--dim-gray);
    color: var(--dr-shade-gray);
    transition: .3s ease-in-out;
    border-radius: 5px;
  }

  .host-bg-trans-btn:hover {
    background-color: var(--dr-shade-gray);
    color: #fff;
  }

  .hs-detail-modal-main .cm-bd-header {
    height: auto;
    padding: 0 20px;
  }

  .hs-cd-head {
    margin: 20px 0;
    justify-content: space-between;
  }

  .hs-cd-head h3 {
    color: var(--dr-shade-gray);
  }

  .hs-cd-head button {
    border: 1px solid var(--dim-gray);
    color: var(--dr-shade-gray);
    height: 40px;
    padding: 0 15px;
    border-radius: 5px;
    text-transform: capitalize;
    font-weight: 500;
    font-size: 17px;
    transition: .3s ease-in-out;
  }

  .host-custdetail-main {
    align-items: start;
  }

  .hs-cd-content ul li {
    margin-bottom: 8px;
  }

  .hs-cd-content ul li:last-child {
    margin-bottom: 0;
  }

  .hs-cd-content ul li h5 {
    color: var(--dr-shade-gray);
    line-height: 1.3;
    margin-bottom: 0;
  }

  .hs-cd-content ul li p {
    color: var(--grey-three);
    line-height: 1.3;
    margin-bottom: 0;
  }

  .hs-cd-image img {
    width: 100px;
    box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
    margin-left: 5px;
    height: 100px;
  }

  .host-custabout-content li {
    margin-bottom: 15px;
  }

  .host-custabout-content li:last-child {
    margin-bottom: 0;
  }

  .host-custabout-content img {
    margin-right: 20px;
    width: 23px;
  }

  .host-custabout-content p {
    color: var(--dr-shade-gray);
    margin-bottom: 0;
  }

  .hs-custview-btn {
    color: var(--dr-shade-gray);
    text-decoration: underline;
    margin: 30px 0;
    display: block;
  }

  .hover-transparent:hover {
    color: var(--dr-shade-gray);
  }

  .hs-custbtn-sec button {
    height: 55px;
    margin-bottom: 17px;
    font-weight: 500;
    font-size: 18px;
  }

  .hs-cust-pn span {
    font-size: 13px;
    color: var(--grey-three);
    font-weight: 500;
  }

  .host-cr-detail-content {
    margin: 0;
  }

  .host-cr-detail-content li {
    border-bottom: 1px solid #D1D1D1;
    padding-top: 20px;
    padding-bottom: 20px;
  }

  .host-cr-detail-content li:last-child {
    border-bottom: unset;
  }

  .host-cr-detail-content li h6 {
    line-height: 1.3;
  }

  .hs-guest-offer {
    color: var(--dr-shade-gray);
    text-decoration: underline;
  }

  .hs-guest-offer:hover {
    color: var(--dr-shade-gray);
  }

  .host-cr-detail-content li p {
    color: var(--grey-three);
    margin-bottom: 0;
    line-height: 1.3;
    font-weight: 400;
  }

  .host-custamount-main ul li {
    margin-bottom: 22px;
  }

  .host-custamount-main ul li:last-child {
    margin-bottom: 0;
  }

  .host-custamount-main ul li p {
    color: var(--dr-shade-gray);
    line-height: 1.3;
    margin-bottom: 0;
    font-size: 16px;
  }

  .host-custamount-main ul li:last-child p {
    font-weight: 600;
    font-size: 17px;
  }

  .hs-custarrow-div .hs-ca-div-content {
    border-top: 1px solid #D1D1D1;
    padding-top: 20px;
    padding-bottom: 20px;
  }

  .hs-custarrow-div .hs-ca-div-content p {
    margin-bottom: 0;
    color: var(--dr-shade-gray);
    font-size: 20px;
  }

  .hs-custarrow-div .hs-ca-div-content img {
    rotate: 180deg;
    width: 11px;
  }

  .host-custform-main {
    padding-top: 20px;
    padding-bottom: 85px;
  }

  .host-custform-main p {
    margin: 25px 0 25px 25px;
    color: var(--dr-shade-gray);
  }

  .host-custform-main form textarea {
    border-color: var(--dim-gray);
    resize: none;
    height: 200px;
    margin-bottom: 20px;
  }

  .host-custform-main form textarea::placeholder {
    font-size: 18px;
  }

  .host-custform-main form button {
    height: 55px;
  }

  .confirm-code-reservation .vu-form-control {
    height: 45px;
    margin-right: 5px;
    flex: 1;
  }

  .confirm-code-reservation button {
    width: max-content !important;
  }

  /* inbox */
  .chat-detail-view {
    position: absolute;
    top: 0;
    right: -200%;
    z-index: 99999;
    background: #fff;
    height: 100vh;
    width: 100%;
    overflow: auto;
  }

  .chat-reservation-detail {
    height: 100%;
  }

  .chat-reservation-detail .hs-cd-head {
    border-bottom: 1px solid #D1D1D1;
    margin: 10px 0;
    align-items: center;
    padding: 0 20px 10px
  }

  .chat-reservation-detail .hs-cd-head button {
    border: none;
    padding: 0;
  }

  .chat-reservation-detail .hs-cd-head button img {
    border: none;
    width: 15px;
  }

  .hs-cd-cin p {
    margin-bottom: 6px !important;
  }

  .chat-reservation-detail .hs-cd-image img {
    width: 50px;
    height: 50px;
  }

  .chat-reservation-detail .host-custform-main p {
    margin: 15px 0 15px 0;
    font-size: 16px;
    font-weight: 400;
  }

  .chat-reservation-detail .hs-custdetail-fwc {
    font-size: 16px;
  }

  .chat-reservation-detail .host-custabout-content img {
    margin-right: 10px;
    width: 20px;
  }

  .chat-reservation-detail .hs-custbtn-sec button {
    font-size: 16px;
  }

  .chat-reservation-detail .hs-custarrow-div .hs-ca-div-content p {
    font-size: 17px;
  }

  .chat-reservation-detail .hs-custarrow-div .hs-ca-div-content img {
    width: 9px;
  }

  .chat-reservation-detail .host-custamount-main ul li p {
    font-weight: 400;
  }

  .chat-reservation-detail .host-custamount-main ul li:last-child p {
    font-weight: 600;
  }

  .chat-reservation-detail .hs-custview-btn {
    margin: 10px 0;
  }

  .chat-reservation-detail .host-cr-detail-content li:last-child {
    padding-bottom: 0;
  }

  .chat-reservation-detail .host-custamount-main ul li {
    margin-bottom: 15px;
  }

  .chat-reservation-detail .hs-custdetail-title-mg {
    margin-bottom: 20px;
  }

  .chat-reservation-detail .hs-custview-btn {
    margin: 15px 0;
  }

  .host-custform-main form textarea::placeholder {
    font-size: 14px;
  }

  .chat-reservation-detail .chat-reservation-detail-inner:last-child ul {
    margin-bottom: 50px;
  }

  .bg-black {
    background: #000000;
    height: 200px;
    flex: 0 0 29%;
    margin-left: 10px;
  }

  /**** iqama ****/
  .pro-doc-verify.accordion-flush .accordion-item .accordion-button {
    border-top-left-radius: 15px;
    border-top-right-radius: 15px;
    padding: 10px 15px 10px 0;
    transition: .3s ease-in-out;
  }

  .pro-doc-verify .accordion-button:focus {
    border: none;
    box-shadow: none;
  }

  .pro-doc-verify .accordion-button:not(.collapsed) {
    background-color: var(--grey-two);
    padding: 10px 15px 10px 15px !important;
  }

  .pro-doc-verify .accordion-button:not(.collapsed)::after {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='var%28--bs-body-color%29'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
  }

  .pro-doc-verify .datepicker tbody tr>td.day.selected,
  .datepicker tbody tr>td.day.selected:hover,
  .datepicker tbody tr>td.day.active,
  .datepicker tbody tr>td.day.active:hover {
    background-color: var(--dr-shade-gray);
  }

  .pro-doc-verify .datepicker tbody tr>td.day.today {
    background: var(--dr-shade-gray) !important;
  }

  /**** overlay css ****/
  .bottom-fixed-overlay.p-overlaypanel.p-component.p-ripple-disabled.hs-filter-dropdown {
    left: 0 !important;
    width: 100% !important;
    position: fixed !important;
    bottom: 0 !important;
    height: auto !important;
    top: auto !important;
    display: block !important;
    transform-origin: bottom !important;
    transition: .3s ease-in-out;
  }

  .p-overlaypanel.p-component.p-ripple-disabled.hs-export-dropdown {
    top: 45px !important;
    left: 0 !important;
    width: 250px !important;
  }

  .mobile-modal.p-dialog {
    margin: 0 !important;
  }

  .host-tm-modal .p-dialog-header-icons {
    right: 15px;
    left: auto !important;
  }

  .host-tm-modal .p-dialog-header .hs-cd-head {
    margin: 20px 0 20px 15px;
  }

  .host-listings-filter {
    flex-flow: wrap;
  }

  .host-list-search {
    flex: 0 0 100%;
    margin-bottom: 15px;
  }

  .host-list-table-btn {
    flex-wrap: wrap;
    justify-content: space-between;
    width: 100%;
    margin-left: 0 !important;
  }

  .hst-fil-group {
    margin-right: 0px;
  }

  .list-fltr-amenities-list {
    height: 170px;
    overflow: auto;
  }

  .host-table .p-paginator {
    padding: 0 !important;
  }

  .p-paginator .p-paginator-pages .p-paginator-page {
    min-width: 2rem !important;
    height: 2rem !important;
  }

  .list-tb-listing-main {
    position: relative;
  }

  .list-tb-listing-main:hover {
    color: var(--dr-shade-gray);
  }

  .ht-listing-mb-status i {
    font-size: 12px;
  }

  .upload__btn {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 10px;
  }

  .btn-mini {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    margin-top: 10px;
  }

  .hs-custbtn-sec a {
    height: 55px;
    margin-bottom: 17px;
    font-weight: 500;
    font-size: 18px;
    justify-content: center;
  }

  .hs-custbtn-sec button .p-button-label {
    font-weight: 500;
  }
}

@media (max-width: 380px) {
  .theme-btn {
    font-size: 14px
  }

  .listing-checkbox-wrapper {
    margin: 5px 5px 3px 0;
  }

  .custom-small-modal-width {
    width: 380px;
    margin: 0;
  }
}

.anchor-prnt-btn {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/****** new design guest reservation ******/
.guest-reservation {
  margin: 40px 0 0;
}

.gr-nav-pills.nav-pills {
  background: #EDEDED;
  border-radius: 5px;
  padding: 5px;
  margin: 0;
  height: 48px;
}

.gr-nav-pills.nav-pills .nav-item {
  width: 50%;
}

.gr-nav-pills.nav-pills .nav-link {
  height: 38px;
  padding: 0 20px;
  width: 100%;
  color: #9a9a9c;
  font-weight: 500;
  font-size: 18px;
}

.gr-nav-pills.nav-pills .nav-link.active,
.nav-pills .show>.nav-link {
  color: var(--dr-shade-gray);
  background-color: #fff;
  font-weight: 700;
}

.guest-reservation .gr-body .tab-content {
  overflow: inherit;
}

.gr-card {
  position: relative;
  box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
  border-radius: 10px;
  margin-bottom: 20px;
}

.gr-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 80%;
  border-radius: 10px;
  box-shadow: rgba(100, 100, 111, 0.2) 0px -2px 20px 0px;
}

.gr-cardImage {
  position: relative;
}

.gr-cardImage img {
  width: 100%;
  object-fit: cover;
  border-radius: 10px 10px 0 0;
  height: 180px;
}

.gr-cardImage .gr-tag {
  position: absolute;
  top: 5px;
  left: 5px;
  background-color: #fff;
  border-radius: 5px;
  padding: 0 5px;
}

.gr-cardImage .gr-tag p {
  color: var(--dr-shade-gray);
  font-size: 14px;
  font-weight: 500;
}

.gr-cardBody {
  padding: 0 12px;
  background-color: #fff;
  position: relative;
  border-radius: 0 0 10px 10px;
}

.gr-cardBody p {
  color: var(--dr-shade-gray);
}

.gr-cardBody .gr-cb-title {
  padding: 20px 0;
  color: var(--dr-shade-gray);
}

.gr-cb-content p {
  line-height: 1.2;
}

.gr-cb-content .gr-cb-contentLeft {
  width: 85px;
  position: relative;
}

.gr-cb-content .gr-cb-contentLeft::after {
  content: "";
  position: absolute;
  top: 0;
  right: 25px;
  width: 1px;
  height: 100%;
  background-color: #ecebef;
}

.gr-cb-content .gr-cb-contentRight {
  flex: 1;
}

.gr-cb-content .gr-cb-contentRight .resrv-prop-loc {
  word-break: break-word;
}

.gr-card-bd {
  border-bottom: 1px solid #ecebef;
}

.gr-card-ptb {
  padding: 12px 0;
}

.gr-cb-buttons a span {
  margin-left: 5px;
}

.gr-cb-buttons a i {
  font-size: 18px;
}

.gr-cb-buttons a:hover {
  background-color: #f2f2f2;
  color: var(--dr-shade-gray);
}

.gr-ci-infoBtn a,
.gr-ci-infoBtn .gr-ci-no-phone-num {
  padding: 12px;
  color: var(--dr-shade-gray);
}

.gr-ci-infoBtn a img,
.gr-ci-infoBtn .gr-ci-no-phone-num img {
  width: 35px;
  margin-right: 10px;
}

.gr-ci-infoBtn a i,
.gr-ci-infoBtn .gr-ci-no-phone-num i {
  font-size: 30px;
  margin-right: 5px;
}

.gr-ci-infoBtn a span,
.gr-ci-infoBtn .gr-ci-no-phone-num span {
  color: var(--dr-shade-gray);
  font-size: 16px;
  font-weight: 500;
}

.gr-card-infoMd .cm-bd-body {
  padding: 17px 0 10px;
}

.gr-card-infoMd .cm-bd-footer {
  border-top: none;
  padding: 0;
}

.gr-ci-infoFooter button {
  margin: 0 12px 10px;
}

/****** guest reservation detail page******/
.gr-detail-header {
  padding: 20px 10px;
  background: #f5f5f5;
  display: none;
}

.gr-detail-header-scroll {
  display: block;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1050;
  box-shadow: 0 5px 20px 2px #0000001f;
  transition: .3s;
  animation: slideIn 0.3s linear;
  /* Animation with name 'slideIn' */
}

@keyframes slideIn {
  from {
    transform: translateY(-100%);
  }

  to {
    transform: translateY(0);
  }
}

.gr-detail-header h6 {
  margin: 0 0 0 10px;
}

.gr-floted-btn {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: absolute;
  top: 35px;
  width: 100%;
  padding: 0 15px;
  z-index: 999;
}

.gr-floted-btn .gr-floted-btnInner {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
  background-color: #fff;
  border: none;
}

.gr-floted-btn .gr-floted-btnLeft img {
  width: 14px;
}

.gr-floted-btn .gr-floted-btnRight img {
  width: 17px;
}

.gr-ds-propImages .swiper-pagination-bullet {
  width: var(--swiper-pagination-bullet-width, var(--swiper-pagination-bullet-size, 12px));
  height: var(--swiper-pagination-bullet-height, var(--swiper-pagination-bullet-size, 12px));
  background: #a5a3a4;
  opacity: 1;
  border: 1px solid #fff;
}

.gr-ds-propImages .swiper-pagination-bullet-active {
  background-color: #a5a3a4 !important;
}

.gr-ds-propImages .swiper-slide img {
  width: 100%;
  object-fit: cover;
  height: 330px;
}

.gr-ds-propImages .swiper-slide::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
}

.gr-ds-padding {
  padding: 12px 15px;
}

.gr-ds-margin {
  margin: 0 15px;
}

.gr-ds-checkDate p {
  color: var(--dr-shade-gray);
}

.gr-ds-checkIn {
  position: relative;
}

.gr-ds-checkIn::before {
  content: "";
  position: absolute;
  right: 20px;
  top: 0px;
  width: 1px;
  height: 100%;
  background-color: #f2f2f2;

}

.gr-ds-checkDate .gr-ds-cd-inner {
  width: 50%;
}

.gr-ds-checkDate .gr-ds-cd-inner .check,
.gr-ds-checkDate .gr-ds-cd-inner .date {
  font-weight: 500;
  font-size: 16px;
}

.gr-ds-checkDate .gr-ds-cd-inner .time {
  color: var(--them-secondary);
  font-size: 14px;
}

.gr-ds-hostInfo li i {
  font-size: 35px;
}

.gr-ds-hostInfo li a:hover {
  color: var(--dr-shade-gray);
}

.gr-ds-hostInfo li .gr-ds-hostInfo-inner {
  flex: 1;
  margin-left: 15px;
}

.gr-ds-hostInfo li .gr-ds-hostInfo-inner h6 {
  margin-bottom: 5px;
  color: var(--dr-shade-gray);
}

.gr-ds-hostInfo li .gr-ds-hostInfo-inner p {
  line-height: 1.2;
}

.gr-ds-paddingtb-border {
  padding: 15px;
  border-top: 10px solid #FAFAFA;
}

.gr-ds-dc-title {
  color: var(--dr-shade-gray);
}

.gr-ds-dc-inner {
  padding: 12px 0;
}

.gr-ds-dc-innerTitle {
  margin-bottom: 5px;
}

.gr-ds-dc-innerContent {
  margin-bottom: 0;
  color: var(--dr-shade-gray);
  font-weight: 500;
}

.gr-ds-reservDetail .gr-ds-dc-inner input {
  border: none;
  background: transparent;
  color: var(--dr-shade-gray);
  margin-right: 10px;
  width: 100px;
}

.gr-ds-reservDetail .gr-ds-dc-inner input:focus {
  outline: none;
}

.gr-ds-reservDetail .gr-ds-dc-inner i {
  font-size: 25px;
  color: var(--dr-shade-gray);
}

.gr-ds-termPolicies ul li {
  padding-left: 15px;
  position: relative;
  font-weight: 500;
}

.gr-ds-termPolicies ul li::after {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  width: 10px;
  height: 10px;
  background-color: var(--dr-shade-gray);
  border-radius: 50%;
  transform: translateY(-50%);
}

.gr-ds-checkinInfo .gr-ds-dc-inner {
  border-radius: 10px;
  height: 230px;
  padding: 0;
  margin-top: 30px;
}

.gr-ds-checkinInfo .gr-ds-dc-inner iframe {
  border-radius: 15px;
}

.gr-ds-dc-inner:has(.gr-ds-contIcon) {
  justify-content: space-between;
}

.gr-ds-contIcon .gr-ds-dc-innerContent {
  margin-left: 10px;
  flex: 1;
}

.gr-ds-dc-smIcon {
  font-size: 25px;
  line-height: 1;
}

.gr-ds-dc-lgIcon {
  font-size: 30px;
  line-height: 1;
}

.gr-ds-hr-checkInOut-list li i {
  line-height: 1;
  margin-right: 10px;
  color: var(--dr-shade-gray);
}

.gr-ds-hr-checkInOut .gr-ds-detailContent {
  margin: 15px 0;
}

.gr-ds-hr-checkInOut .gr-ds-dc-innerContent {
  font-weight: 400;
}

.guest-reservation .reservation-nodata {
  height: calc(100vh - 255px);
  margin-bottom: 0;
}

.guest-reservation .tab-pane {
  margin-bottom: 0;
}

.text-copy-btn {
  position: relative;
}

.textCopy-tooltip {
  position: absolute;
  top: 30px;
  left: -30px;
  background: #373737;
  padding: 10px 15px;
  display: flex;
  justify-content: center;
  color: #fff;
  font-size: 14px;
  border-radius: 4px;
  letter-spacing: 1px;
  opacity: 0;
}

.textCopy-tooltip.appear {
  animation: appear 1s ease;
}

@keyframes appear {
  0% {
    opacity: 0;
  }

  20% {
    transform: translateY(10px);
    opacity: 1;
  }

  80% {
    transform: translateY(0px);
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}

/****** guest reservation detail page end******/

.ref-link {
  height: 51px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ref-link i {
  font-size: 21px;
}

.warning-chat {

  padding: 5px 10px;
  color: #664d03;
  background-color: #fff3cd;
  border-color: #ffecb5;
  border-radius: 5px;

}

.warning-chat i {
  position: relative;
  top: 3px;
}

/**** filter dropdown****/
.search-filter-select {
  width: 100% !important;
  background-color: transparent;
}

.search-filter-select .dropdown-toggle {
  background-color: transparent;
  border-radius: 25px;
  border-color: var(--grey-one) !important;
  margin-bottom: 5px;
}

.search-filter-select .dropdown-toggle.show {
  /* border: unset !important; */
}

.search-filter-select .btn.dropdown-toggle:hover,
.search-filter-select .btn.dropdown-toggle:focus {
  background-color: transparent;
  /* border-color: transparent; */
  outline: none !important;
}

.search-filter-select .dropdown-menu.show {
  position: relative !important;
  transform: unset !important;
  inset: unset !important;
  border-radius: 25px;
}

.search-filter-select .bs-searchbox {
  padding: 0 !important;
  position: relative;
}

.search-filter-select.bootstrap-select .bs-searchbox::before {
  content: '\f002';
  font-family: 'Font Awesome 5 Free';
  position: absolute;
  top: 50%;
  left: 15px;
  font-size: 16px;
  color: #000000;
  font-weight: 800;
  transform: translateY(-50%);
}

.search-filter-select .dropdown-menu.show .bs-searchbox .form-control {
  height: 35px;
  border: none;
  border-bottom: 1px solid #d5d5d5;
  border-radius: 0 !important;
  padding-left: 36px !important;
}

.search-filter-select .dropdown-menu.show .bs-searchbox .form-control:focus {
  outline: none !important;
  border-color: var(--grey-one) !important;
}

.search-filter-select .dropdown-menu.inner.show {
  transform: unset !important;
  width: 100%;
  border-radius: unset;

}

.search-filter-select .dropdown-menu.inner.show li {
  width: 100%;
  padding: 0;
  margin-bottom: 2px;
}

.search-filter-select .dropdown-menu.inner.show li .dropdown-item {
  padding: 10px 15px;
  border-radius: 0;
}

.search-filter-select .dropdown-menu.inner.show li .dropdown-item span {
  margin-bottom: 0;
  color: #000000;
  font-size: 14px;
}

.search-filter-select.bootstrap-select.show-tick .dropdown-menu .selected span.check-mark {
  right: 20px;
  top: 45%;
  transform: translateY(-50%);
}

.search-filter-select.bootstrap-select .dropdown-item.active {
  background-color: #FFF3E0 !important;
}

.search-filter-select.bootstrap-select .dropdown-item:hover {
  background-color: #FFF3E0 !important;
}

.search-filter-select.bootstrap-select.show-tick .dropdown-menu li a span.text {
  text-transform: capitalize;
}

/***** ilm yaqeen ******/
/* ilm-e-yakeen */
label.ilq-chk {
  border: 1px solid #dedede;
  width: 100%;
  border-radius: 7px;
  padding: 10px;
  align-items: center;
}

label.ilq-chk .form-check-input[type=radio] {
  margin-top: 2px;
}

.hide-inp {
  display: none;
}

.after-payment-ilmyakeen .modal-body {
  direction: rtl;
}

.im-country-select {
  width: 100% !important;
  background-color: transparent;
  border: 1px solid #d5d5d5;
  border-radius: 17px;
  margin-top: 10px;
}

.im-country-select .dropdown-toggle.show {
  border: unset !important;
}

.im-country-select .dropdown-toggle {
  background-color: transparent;
}

.im-country-select.bootstrap-select>.dropdown-toggle {
  height: 36px;
  border-radius: 25px;
  padding-left: 24px !important;
}

.im-country-select .btn.dropdown-toggle:hover,
.im-country-select .btn.dropdown-toggle:focus {
  background-color: transparent;
  border-color: transparent;
  outline: none !important;
}

.im-country-select .bs-searchbox {
  padding: 0 !important;
  position: relative;
}

.im-country-select.bootstrap-select .bs-searchbox::before {
  content: '\f002';
  font-family: 'Font Awesome 5 Free';
  position: absolute;
  top: 50%;
  left: 15px;
  font-size: 16px;
  color: #000000;
  font-weight: 800;
  transform: translateY(-50%);
}

.im-country-select .dropdown-menu.show .bs-searchbox .form-control {
  height: 40px;
  border: none;
  border-bottom: 1px solid #d5d5d5;
  border-radius: 0 !important;
  padding-left: 36px !important;
}

.im-country-select .dropdown-menu.show .bs-searchbox .form-control:focus {
  outline: none !important;
}

.im-country-select .dropdown-menu.inner.show {
  transform: unset !important;
  width: 100%;
  border-radius: unset;
}

.im-country-select .dropdown-menu.inner.show li {
  width: 100%;
  margin-bottom: 2px;
  padding: 0;
}

.im-country-select .dropdown-menu.inner.show li .dropdown-item {
  padding: 5px 15px;
  border-radius: 0;
}

.im-country-select.bootstrap-select .dropdown-item.active {
  background-color: #FFF3E0 !important;
}

.im-country-select.bootstrap-select.show-tick .dropdown-menu .selected span.check-mark {
  right: 20px;
  top: 45%;
  transform: translateY(-50%);
}

.im-country-select .dropdown-menu.inner.show li .dropdown-item span {
  margin-bottom: 0;
  color: #000000;
  font-size: 16px;
}

.im-country-select.bootstrap-select.show-tick .dropdown-menu li a span.text {
  text-transform: capitalize;
}

.im-country-select.bootstrap-select .dropdown-toggle .filter-option {
  display: flex;
  align-items: center;
}

.im-country-select .dropdown-toggle .filter-option-inner-inner {
  color: var(--them-secondary);
  font-size: 14px;
}

.ilq-chk .hs-ls-item .ilm-chk-title {
  font-size: 16px !important;
  margin-bottom: 0;
}

.hijri-calendar-check {
  display: none;
}

.hijri-calendar-check.active {
  display: block;
}

.after-payment-ilmyakeen .im-country-select.bootstrap-select>.dropdown-toggle {
  padding-left: 0 !important;
  padding-right: 24px !important;
}

.hijri-calendar-check-pay {
  display: none;
}

.hijri-calendar-check-pay.active {
  display: block;
}

.bootstrap-select>.dropdown-toggle:after {
  margin-left: -17px !important;
}

/***** host profile image style *****/
.host-profile-setting {
  max-width: max-content;
  display: flex;
  align-items: center;
  flex-direction: column;
  margin: 0 auto;
}

.hp-setting-img {
  position: relative;
}

.hp-setting-img .host-upload-btn {
  position: absolute;
  right: 1px;
  bottom: 8px;
  background-color: transparent;
  border: none;
  font-size: 22px;
}

.hp-setting-img .host-upload-btn i:hover {
  transition: all .3s cubic-bezier(.175, .885, .32, 1.275);
  color: #999;
}

.hp-setting-img img {
  width: 120px;
  height: 120px;
  object-fit: cover;
  border-radius: 50%;
  box-shadow: 0 1px 10px #0000001f, 0 4px 5px #00000024, 0 2px 4px -1px #0003;
}

.hp-setting-btn {
  margin-top: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hp-setting-btn button {
  background-color: transparent;
  border: 1px solid #181818;
  border-radius: 25px;
  padding: 0 15px;
  font-size: 14px;
  height: 25px;
}

.hp-setting-btn button:first-child {
  margin-right: 5px;
}

.hp-setting-btn button:hover {
  background-color: #181818;
  color: #ffffff;
}

/**** review modal single page ***/
.show-review-sidebar {
  /* padding: 0 20px 0 0; */
}

.sr-sidebar-progress h6 {
  font-size: 14px;
  color: #575757;
}

.sr-sidebar-progress .review-progress {
  line-height: 1;
}

.sr-sidebar-progress .review-progress p {
  margin-right: 10px;
  font-size: 12px;
}

.sr-sidebar-progress .review-progress .progress {
  height: 5px;
}

.sr-sidebar-rate {
  margin: 25px 0 40px;
}

.sr-sidebar-rate-inner {
  justify-content: space-between;
  border-left: 1px solid #bfbbbb8f;
  padding: 0 15px;
  flex-direction: column-reverse;
  align-items: start;
}

.sr-sidebar-rate-inner .sr-sr-inner-left {
  margin-top: 20px;
  line-height: 1;
}

.sr-sidebar-rate-inner .sr-sr-inner-left i {
  font-size: 25px;
  color: #575757;
}

.sr-sidebar-rate-inner p {
  color: #575757;
  font-size: 14px;
}

.sr-sidebar-rate-inner .sr-sr-inner-right {
  align-items: start;
  flex: 1;
  flex-direction: column;
  line-height: 1.2;
}

.sr-sidebar-rate-inner .sr-sr-inner-right span {
  font-size: 14px;
}

.sr-sidebar-rate li:last-child .sr-sidebar-rate-inner {
  border-bottom: none;
}

.sr-content-head {
  justify-content: space-between;
}

.sr-content-head select {
  width: 120px;
  height: 40px;
  padding: 10px;
}

.sr-content-search {
  position: relative;
  margin: 15px 0;
}

.sr-content-search input {
  height: 40px;
  padding-left: 40px;
}

.sr-content-search i {
  position: absolute;
  top: 50%;
  left: 15px;
  transform: translateY(-50%);
  font-size: 20px;
  color: var(--them-secondary)
}

.sr-content-body {
  padding: 0 10px;
}

.sr-comment {
  margin-bottom: 25px;
}

.sr-profile .sr-profile-img img {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  object-fit: cover;
}

.sr-profile .sr-profile-name {
  margin-left: 10px;
}

.sr-profile .sr-profile-name p {
  font-size: 14px;
}

.sr-pc-star-rate {
  display: flex !important;
  align-items: center;
}

.sr-pc-star-rate .star-rating {
  display: flex !important;
  align-items: center;
}

.sr-pc-star-rate .star-rating label {
  cursor: default;
  font-size: 0;
  line-height: 1;
}

.sr-pc-star-rate .star-rating label:hover {
  color: #bbb;
}

.sr-pc-star-rate .star-rating label:hover~label {
  color: #bbb;
}

.sr-pc-star-rate .star-rating label i {
  font-size: 14px;
}

.sr-cb-time {
  margin-left: 10px;
}

.sr-cb-time p {
  color: #575757;
  font-size: 14px;
}

.sr-profile-comment .sr-pc-content {
  line-height: 1.3;
}

.sr-comment-host {
  margin: 0 0 0 25px;
}

.sr-comment-host .sr-profile .sr-profile-img img {
  width: 35px;
  height: 35px;
}

.sr-comment-host .sr-profile-name h6 {
  font-size: 14px;
}

.sr-comment-host .sr-profile-name p {
  font-size: 12px;
}

.sr-comment-host .sr-profile-comment .sr-pc-content {
  font-size: 15px;
}

.sr-sidebar-title {
  display: flex;
  align-items: baseline;
}

.sr-sidebar-title i {
  font-size: 25px;
  margin-right: 10px;
}

.show-all-review-btn {
  width: max-content;
  margin: 20px auto 0;
  display: block;
}

.show-review-content {
  position: relative;
}

.sr-content-head-fixed {
  position: fixed;
  top: 57px;
  width: 98%;
  background: #ffff;
  padding: 0 20px;
  left: 1px;
}

.sr-content-body-fixed {
  padding-top: 120px;
}

/** wallet pay button**/
.ch-wallet-pay {
  margin-top: 35px;
  position: relative;
}

.ch-wallet-pay::before {
  content: "";
  position: absolute;
  top: -19px;
  left: 0;
  width: 100%;
  border-top: 1px solid #d1d1d1;
}

.ch-wallet-pay label {
  position: relative;
  display: block;
  width: 40px;
  height: 22px;
  background: transparent;
  border-radius: 30px;
  transition: all 300ms linear;
  transition: all 300ms linear;
  border: 1px solid #666;
  margin: 0 10px 0 0 !important;
}

.ch-wallet-pay label::before {
  position: relative;
  content: "";
  width: 15px;
  height: 15px;
  background: #666;
  display: block;
  border-radius: 50%;
  top: 3px;
  left: 4px;
  transition: all 300ms linear;
}

.ch-wallet-pay input:checked+label {
  border-color: var(--theme-primary);
}

.ch-wallet-pay input:checked+label::before {
  left: 19px;
  background-color: var(--theme-primary);
}

.ch-wallet-pay input {
  display: none;
}

.ch-wallet-pay img {
  margin-right: 5px;
}

.content-point {
  padding-left: 30px;
  margin-top: 10px;
  list-style: inherit;
}

/**** accept term condition modal ****/
.accept-term .modal-title {
  top: 0;
}

.accept-term .form-check-main {
  margin: 20px 0 25px;
}

.accept-term .form-check {
  display: flex;
  align-items: center;
}

.accept-term .form-check input {
  margin-top: 0;
}

.accept-term .form-check label p {
  margin: 0 0 0 5px;
}

.accept-term button {
  width: 100%;
  padding: 10px 0;
}

/**** insurance page ****/
.if-mb {
  margin-bottom: 30px;
}

.in-box-mb {
  margin-bottom: 20px;
}

.insurance-page {
  margin: 40px 0 0 0;
}

.insurance-page p {
  color: #7D7D7D;
  font-size: 17px;
}

.insur-feature-box {
  border: 2px solid #F2F2F2;
  border-radius: 16px;
  padding: 20px;
  height: 100%;
}

.if-box-icon {
  width: 45px;
  height: 45px;
  border-radius: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #FCB83340;

}

.insur-feature-box img {
  width: 20px;
}

.insur-feature-box h4 {
  margin: 10px 0 20px;
}

.insurance-cover .insurance-cover-list {
  margin: 0;
  padding: 0;
  list-style: none;
  margin-top: 20px;
}

.insurance-cover .insurance-cover-list li {
  display: flex;
  align-items: start;
}

.insurance-cover .insurance-cover-list li .ic-list-icon img {
  width: 26px;
  margin-right: 10px;
}

.inurance-limit .incurance-limit-box {
  border: 2px solid #F2F2F2;
  border-radius: 16px;
  padding: 20px;
  height: 100%;
  display: flex;
  align-items: start;
}

.inurance-limit .ic-box-icon {
  width: 45px;
  height: 45px;
  border-radius: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #FCB83340;
  margin-right: 20px;
}

.inurance-limit .ic-box-icon img {
  width: 24px;
}

.inurance-claim .insurance-claim-list {
  counter-reset: list-counter;
  list-style: none;
}

.inurance-claim .insurance-claim-list li {
  position: relative;
  padding-left: 20px;
  margin-bottom: 25px;
}

.inurance-claim .insurance-claim-list li::before {
  counter-increment: list-counter;
  content: counter(list-counter);
  color: #FCB833;
  position: absolute;
  top: 1px;
  left: 0;
  font-size: 20px;
  font-weight: 700;
  line-height: 1;
}

.incurance-contact-ic {
  display: flex;
  align-items: center;
}

.incurance-contact-ic img {
  width: 30px;
  margin-right: 5px;
}

.incurance-contact-ic h4 {
  line-height: 1;
}

.iqama-support-contact {
  font-size: 14px;
}

.iqama-support-contact a {
  color: #25d366;
  position: relative;
  font-weight: 500;
  margin-left: 2px;
  transition: .3s all;
  font-size: 14px;
}

.iqama-support-contact a i {
  font-size: 17px;
}

.iqama-support-contact a::before {
  position: absolute;
  content: "";
  bottom: 3px;
  left: 0;
  width: 100%;
  transition: .3s all;
  border-bottom: 2px solid #25d366;
}

.iqama-support-contact a:hover::before {
  border-color: #000000;
}

/* annoucement page */
.annoucement {
  padding: 50px 0;
}

.annoucement p {
  color: #000;
}

.list-skip-btn {
  margin-right: 5px;
}

/** payment option update**/
.pay-sec p {
  color: #575757;
  font-size: 16px;
  margin-bottom: 6px;
}

.prop-payment-option {
  display: flex;
  align-items: center;
  flex-flow: wrap;
}

.prop-payment-option .pay-sec-label {
  position: relative;
  width: 48%;
  border: none;
  border-radius: unset;
  padding: 0;
  display: block;
}

.prop-payment-option .pay-sec-label:nth-child(1),
.prop-payment-option .pay-sec-label:nth-child(3) {
  margin-right: 3px;
}

.prop-payment-option .pay-sec-label:nth-child(2),
.prop-payment-option .pay-sec-label:nth-child(4) {
  margin-left: 3px;
}

.prop-payment-option .pay-sec-label input {
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  margin: 0;
  cursor: pointer;
}

.prop-payment-option .pay-sl-content {
  width: 100%;
  color: var(--primary);
  box-shadow: none;
  border: 2px solid transparent;
  border-radius: 10px;
  text-align: center;
  box-shadow: 0 4px 25px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 25px 0;
  cursor: pointer;
}

.prop-payment-option input:checked+.pay-sl-content::after {
  content: "\f058";
  color: var(--theme-primary);
  position: absolute;
  right: 5px;
  top: 6px;
  font-family: "Font Awesome 5 Free";
  font-style: normal;
  font-size: 16px;
  font-weight: 900;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-animation-name: fadeInCheckbox;
  animation-name: fadeInCheckbox;
  -webkit-animation-duration: 0.3s;
  animation-duration: 0.3s;
  -webkit-animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.prop-payment-option input:checked+.pay-sl-content {
  border: 2px solid var(--theme-primary);
  -webkit-transition: border 0.3s;
  -o-transition: border 0.3s;
  transition: border 0.3s;
}

.pay-sec .pay-sec-label .pay-sl-content img {
  object-fit: contain;
  width: 70px;
  transition: .3s ease-in-out;
  height: 20px;
}

@-webkit-keyframes fadeInCheckbox {
  from {
    opacity: 0;
    -webkit-transform: rotateZ(-20deg);
  }

  to {
    opacity: 1;
    -webkit-transform: rotateZ(0deg);
  }
}

/* annoucement page */
.annoucement {
  padding: 50px 0;
}

.annoucement p {
  color: #000;
}

.annoucement .main-title h1 {
  font-weight: 700;
  /* text-align: center; */
  font-size: 20px;
}

.ancmnt-banner-main {
  margin: 30px 0;
}



.ancmnt-banner-main {
  margin: 30px 0;
}

.annoucement .ancmnt-banner {
  height: auto;
  object-fit: cover;
  width: 100%;
}



.ancmnt-banner-main p {
  font-weight: 500;
  margin-top: 15px;
}

.ancmnt-content p {
  font-size: 16px;
  margin-bottom: 25px;
}

.ancmnt-content a {
  color: var(--theme-primary);
  text-decoration: underline;
}

.ancmnt-content a {
  color: var(--theme-primary);
  text-decoration: underline;
}

.dots {
  width: 56px;
  height: 26.9px;
  background: radial-gradient(circle closest-side,#f5c33e 90%,#0000) 0%   50%,
         radial-gradient(circle closest-side,#f5c33e 90%,#0000) 50%  50%,
         radial-gradient(circle closest-side,#f5c33e 90%,#0000) 100% 50%;
  background-size: calc(100%/3) 13.4px;
  background-repeat: no-repeat;
  animation: dots-7ar3yq 1s infinite linear;
}

@keyframes dots-7ar3yq {
  20% {
     background-position: 0%   0%, 50%  50%,100%  50%;
  }

  40% {
     background-position: 0% 100%, 50%   0%,100%  50%;
  }

  60% {
     background-position: 0%  50%, 50% 100%,100%   0%;
  }

  80% {
     background-position: 0%  50%, 50%  50%,100% 100%;
  }
}
#loader-more .dots{
  margin: 0 auto;
}
.dots {
  margin: 0px auto;
}

@media(max-width: 375px) {
  #coupon_code .modal-body {
    margin-bottom: 50px;
  }
}

@keyframes fadeInCheckbox {
  from {
    opacity: 0;
    transform: rotateZ(-20deg);
  }

  to {
    opacity: 1;
    transform: rotateZ(0deg);
  }
}

/** payment option update end**/

@media(max-width: 375px) {
  #coupon_code .modal-body {
    margin-bottom: 50px;
  }
}

/**** instruction page ****/
.instruction{
  margin: 0 0 2px;
}
.instruct-title{
  color: #181818;
  font-weight: 600;
}
.instruct-content{
  color: #181818;
  font-size: 18px;
}
.instruction .get-licence{
  background-color: #f5b133;
  padding: 25px 0 50px;
  position: relative;
}
.get-licence img{
  width: 150px;
  margin-bottom: 10px;
}
.curved-border {
  position: absolute;
  bottom: -11px;
  left: 50%;
  transform: translateX(-50%);
}
.instructSwiper {
  padding-top: 15px !important;
  padding-bottom: 15px !important;
}
.instructSwiper.swiper-horizontal>.swiper-pagination-bullets, .swiper-pagination-bullets.swiper-pagination-horizontal, .swiper-pagination-custom, .swiper-pagination-fraction{
  bottom: 0;
}
.instruct-reg-sec{
  padding: 20px 0;
}
.instruction .instruct-list{
  list-style: none;
  counter-reset: line;
  margin: 15px 0 0 15px;
}
.instruction .instruct-list li{
  position: relative;
  font-size: 16px;
  margin-bottom: 10px;
}
.instruction .instruct-list li a{
  color: var(--theme-primary);
}
.instruction .instruct-list li:before {
  position: absolute;
  left: -18px;
  display: inline-block;
  border-radius: 50%;
  color: #181818;
  text-align: center;
  counter-increment: line;
  content: counter(line) '.';
  font-size: 17px;
}
.instruction-title-style{
  position: relative;
  width: max-content;
}
.instruction-title-style::before{
  content: "";
  position: absolute;
  left: 0;
  bottom: 5px;
  height: 4px;
  width: 100%;
  background-color: var(--theme-primary);
  z-index: -1;
}
.instruct-reg-prop-sec{
  position: relative;
  padding-top: 30px;
}
.instruct-reg-prop-sec .instruct-list{
  margin: 25px 0 0 0;

}
.instruct-reg-prop-sec .instruct-list li{
  flex: 0 0 100%;
  max-width: 100%;
}
.instruct-reg-prop-sec .instruct-list li:before{
  width: 40px;
  height: 40px;
  border-radius: 50%;
  color: #181818;
  background-color: #E7E7E7;
  content: counter(line);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 22px;
  left: 0;
  top: -7px;
}
.instruct-reg-prop-sec .instruct-list li p{
  width: 90%;
  color: #181818;
  font-size: 16px;
  margin-left: 55px;
  margin-bottom: 0;
  white-space: break-spaces;
}
.instruct-reg-prop-sec .instruct-list li img{
  width: 100%;
  height: 290px;
  border: 1px solid #E7E7E7;
  border-radius: 8px;
  margin-top: 25px;
}
.instruction-tabing.nav-pills{
  margin-bottom: 40px;
}
.instruction-tabing.nav-pills .nav-item{
  flex: 0 0 50%;
}
.instruction-tabing.nav-pills .nav-link.active, .nav-pills .show>.nav-link{
  background-color: #181818;
  color: #fff;
  border-color: #E7E7E7;
}
.instruction-tabing.nav-pills .nav-link{
  background-color: #F7F7F7;
  border: 1px solid #E7E7E7;
  color: #181818;
  padding: 12px 15px;
  width: 100%;
}
.instruction-tabing.nav-pills .nav-item:first-child .nav-link{
  border-radius: 25px 0 0 25px;
  border-right: unset;
}
.instruction-tabing.nav-pills .nav-item:last-child .nav-link{
  border-radius: 0 25px 25px 0;
  border-left: unset;
}
.instruct-reg-prop-sec .tab-content{
  overflow-x: hidden;
}
.instruct-reg-prop-sec .instruct-list.mob-instruct-list li img{
  width: 100%;
  height: 400px;
  border: unset;
  border-radius: 0;
}
.need-help-sec{
  position: relative;
}
.need-help-sec .aboutsec-content{
  top: 50%;
}
.instruction .aboutsec-content{
  text-align: start;
}
.aboutsec-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  width: 100%;
}
.instruc-help-sec-main{
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 30px;
  gap: 10px;
  flex-direction: column;
}
.instruc-help-sec{
  width: 100%;
}
.instruc-help-sec a{
  display: block;
}
.instruc-help-sec .instruc-help-sec-icon {
  width: 120px;
  height: 120px;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.instruc-help-sec .instruc-help-sec-icon img {
  width: 85px;
}

.instruc-help-sec-icon .ih-si-two {
  width: 70px !important;
}

.instruc-help-sec h5 {
  font-weight: 600;
  margin: 15px 0;
}

.instruc-help-sec button {
  padding: 0 15px;
  height: 51px;
  font-size: 18px;
  border-radius: 8px;
  width: 100%;
}
.instruc-help-sec .hst-bbg-btn{
  display: flex;
  align-items: center;
  gap: 10px;
  justify-content: center;
}
.instruc-help-sec:first-child .hst-bbg-btn img{
  width: 30px;
}
.instruc-help-sec .hst-bbg-btn img{
  width: 25px;
}
.prop-verify-modal .alert-modal img{
  width: 70px;
}
.prop-verify-modal h3{
  color: #181818;
}
.prop-verify-modal .alert-modal p{
  color: #181818;
  font-weight: 500;
  font-size: 16px;
}
.prop-verify-modal .host-btn-black{
  background: #181818;
  padding: 10px 20px;
  font-size: 16px;
  display: block;
  width: max-content !important;
  margin: 0 auto;
}
.prop-verify-modal a{
  color: #181818;
  font-weight: 500;
  display: inline-block;
  font-size: 18px;
}
.prop-verify-modal a:hover{
  color: #fff;
}

.instruction .registered {
  background-color: #f0f0f0; /* Light yellow background */
}
/*** license modal listing journey ****/
.license-toggle-switch {
  display: flex;
  position: relative;
  width: 200px;
  background-color: #f0f0f0;
  border-radius: 8px;
  overflow: hidden;
  margin: 0 auto;
}

.license-toggle-switch input[type="radio"] {
  display: none;
}

.license-toggle-switch label {
  flex: 1;
  padding: 10px;
  text-align: center;
  cursor: pointer;
  font-weight: bold;
  z-index: 1;
}

.license-toggle-indicator {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 50%;
  background-color: black;
  transition: transform 0.3s ease;
  z-index: 0;
}

.license-toggle-switch input#company:checked ~ .license-toggle-indicator {
  transform: translateX(0%);
}

.license-toggle-switch input#individual:checked ~ .license-toggle-indicator {
  transform: translateX(100%);
}

.license-toggle-switch label#company {
  color: white;
}

.license-toggle-switch input#company:checked + label,
.license-toggle-switch input#individual:checked + label {
  color: white;
}

.license-toggle-switch input#company:checked + label + label {
  color: black;
}
/**** property license alert ****/
.property-license-alert{
  background-color: #FCB8333D;
  padding: 20px 0;
  margin-top: 10px;
  text-align: center;
}
.property-license-alert img{
  width: 50px;
}
.property-license-alert h5{
  color: #181818;
  font-weight: 700;
  font-size: 22px;
  margin-bottom: 5px;
}
.property-license-alert p{
  color: #181818;
}
.property-license-alert p a{
  font-weight: 500;
}

/**** property license modal ****/
.actvate-property-content{
  padding: 0 10px;
}
.ap-content-head{
  margin-bottom: 20px;
}
.ap-content-list li a{
  display: flex;
  align-items: start;
  gap: 10px;
  border: 2px solid #E7E7E7;
  padding: 10px;
  border-radius: 8px;
  margin-bottom: 10px;
}
.ap-content-list li:last-child a{
  margin-bottom: 0;
}
.ap-content-list li a img{
  width: 35px;
  height: 35px;
}
.ap-content-list li a .ap-cl-content h5{
  margin: 5px 0;
  font-size: 18px;
}
.ap-content-list li a .ap-cl-content P{
  line-height: 1.1;
}


/**** all transaction *****/
.all-transaction {
  padding: 50px 0;
}

.al-title {
  font-size: 1.8rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 20px;
  text-align: center;
}

.transaction-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  margin-bottom: 12px;
  border-radius: 8px;
  background-color: #f9fafb;
  gap: 10px;
}

.transaction-details {
  display: flex;
  flex-direction: column;
}

.transaction-title {
  font-weight: 500;
  font-size: 15px;
  color: #333;
}

.transaction-date {
  font-size: 14px;
  color: #888;
  margin-top: 2px;
}

.transaction-amount {
  font-weight: 600;
  font-size: 14px;
  display: flex;
  align-items: end;
  gap: 5px;
  flex-direction: column-reverse;
}
.badge {
  font-size: 12px;
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 4px;
  color: #fff;
}

.credit {
  background-color: #27ae60;
}

.debit {
  background-color: #e74c3c;
}
.not-available-tag {
  display: flex;
  align-items: center;
  background: #fff;
  color: #323232;
  font-size: 12px;
  font-weight: 600;
  padding: 4px 10px 3px;
  border-radius: 25px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  white-space: nowrap;
  position: absolute;
  bottom: 5px;
  left: 5px;
  z-index: 99;
  width: max-content;
  margin-bottom: 0;
}

.not-available-tag::before {
  content: "\f05e";
  margin-right: 4px;
  font-size: 16px;
  font-family: 'Font Awesome 5 Free';
  color: #ff4d4f;
}
.not-available-detail-tag-main {
  text-align: center;
  margin: 15px 0;
}

.not-available-detail-tag {
  display: inline-flex;
  align-items: center;
  background-color: #fff;
  color: #ff4d4f;
  font-size: 14px;
  font-weight: 500;
  padding: 7px 15px;
  border-radius: 25px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid #ff4d4f;
  white-space: nowrap;
}

.not-available-detail-tag::before {
  content: "\f05e";
  font-size: 15px;
  margin-right: 5px;
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
}

.not-available-detail-date {
  color: #4a4a4a;
  font-size: 13px;
  font-weight: 400;
  margin-top: 5px;
}
.display-license-num{
  display: flex;
  align-items: center;
  gap: 5px;
  margin: 5px 0 0 0;
  font-size: 16px !important;
}
.display-license-num img{
  width: 16px;
  height: 16px;;
}

/* inprogress property page  */
.inprogress-listing {
  margin-top: 20px;
}

.inprogress-listing h3 {
  margin-top: 20px;
  color: #333;
}

.listing-item-main{
  max-height: 285px;
  overflow-y: auto;
  padding: 0 10px 0 0;
}

.inprogress-listing .listing-item {
  display: flex;
  align-items: center;
  padding: 15px;
  margin-bottom: 10px;
  border: 2px solid #ddd;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  gap: 15px;
}

.inprogress-listing .listing-item:hover {
  background-color: #f7f7f7;
  border-color: #333;
}

.inprogress-listing .listing-item img {
  width: 50px;
  height: 50px;
  border-radius: 8px;
  object-fit: cover;
}

.inprogress-listing .listing-item .info {
  flex: 1;
}

.inprogress-listing .listing-item .info h4 {
  margin: 0;
  font-size: 16px;
  color: #333;
}

.inprogress-listing .listing-item .info p {
  margin: 5px 0 0;
  font-size: 14px;
  color: #666;
}
.create-new-main{
  margin-top: 20px;
}
.create-new-main h3{
  color: #333;
}
.create-new {
  display: flex;
  align-items: center;
  padding: 15px;
  border: 2px solid transparent;
  gap: 15px;
  border-radius: 8px;
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.create-new:hover {
  border-color: #333;
}

.create-new img {
  width: 35px;
}
.create-new-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex: 1;
}
.create-new-info span {
  font-size: 18px;
  color: #333;
  font-weight: 500;
}
.create-new-info i{
  color: #494949;
  font-size: 30px;
}
.mw-discount-property{
  background-color: #fff3cd;
  padding: 8px;
  font-size: 12px;
  font-weight: 500;
  color: #ffc107;
  display: inline-block;
  text-align: center;
  position: absolute;
  top: 0;
  left: 0;
  border-radius: 8px 0 8px 0;
  padding: 12px 15px;
  font-weight: 700;
  z-index: 99;
}
.mw-discount{
  background-color: #fff3cd;
  border-left: 5px solid #ffc107;
  padding: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #000;
  display: inline-block;
  text-align: center;
  border-radius: 4px;
  width: 100%;
}
.daterangepickers td.in-range{
  background-color: #fbe7b2;
}
.daterangepickers td.available:hover, .daterangepickers th.available:hover{
  background-color: #fbe7b2;
}
.daterangepickers td.off.ends.in-range.available{
  background-color: #fbe7b2 !important;
}
.daterangepickers td.off.ends.active.end-date.in-range.available{
  background-color: var(--theme-primary) !important;
}
.daterangepickers td.off.ends.available{
  color: #000;
}
.listing-footer-btn .black-btn img{
  display: none;
}
#licenseToast .toast-body i{
  color: #ffb02e;
}
.lf-rv-image-ar,
.rt-rv-img-ar{
    display: none;
}

.cm-bd-content .btn:disabled{
  background-color: var(--theme-primary);
  border-color: var(--theme-primary);
}
#per_night_price {
  color: #000000 !important;
}

/* landing page css */
.landing-page-sec {
  direction: rtl;
}
.lp-header {
  display: flex;
  align-items: center;
  gap: 15px;
  background-image: url(../images/lp-header-bg.svg);
  padding: 20px;
  background-position: center;
}
.lp-header-btn {
  padding: 0;
  width: 155px;
  color: #000000;
  font-weight: 500;
}

.lp-header-content {
  font-size: 16px;
  font-weight: 500;
  flex: 1;
  color: #ffffff;
}

.lp-header-content span {
  color: var(--theme-primary);
}
.lp-body{
  padding: 30px 0;
  text-align: center;
}
.lp-body .lp-body-content h1{
  font-size: 24px;
  font-weight: 700;
}
.lp-body .lp-body-content p{
  font-size: 14px;
  color: #000000;
  margin-top: 20px;
}
.lp-body-banner-img{
  width: 100%;
}
.lp-card{
  margin-top: 25px;
  position: relative;
}
.lp-card img{
  width: 100%;
}
.lp-card-inner{
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  transform: translate(-50%, -50%);
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.lp-card h1{
  color: #ffffff;
  font-size: 24px;
  font-weight: 400;
  line-height: 1.6;
}
.lp-card .lp-card-amount{
  color: var(--theme-primary);
  font-weight: 700;
}
.lp-card .lp-card-city{
  font-weight: 700;
}
.lp-card-btn{
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 30px;
  gap: 15px;
}
.lp-card-btn button{
  font-weight: 700;  
  font-size: 14px;
}
.lp-card-themeb{
  color: #000000;
}
.lp-card-transb{
  border: none;
}
.lp-card-transb a{
  color: #ffffff;
}
.lp-footer{
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 20px;
}
.lp-footer img{
  width: 90%;
}
/* checkout page */
.checkout-page{
  padding: 40px 0;
}
.checkout-detail-title {
  margin-bottom: 10px;
}
.checkout-page .cs-book{
  width: 100%;
}
.checkout-payment .prop-payment-option{
  display: block;
}
.checkout-payment .prop-payment-option .pay-sec-label{
  width: 100%;
  margin-right: 0 !important;
}
.checkout-summary table{
  margin-bottom: 0;
}
.wallet-payment-option {
  border: 1px solid var(--grey-one);
  border-radius: 10px;
  padding: 15px;
  background-color: #fff;
  transition: all 0.3s;
}
.wallet-payment-option.checked.checked {
  border-color: #4CAF50;
  background-color: #f8fff8;
}
.wallet-payment-option .form-check {
  display: flex;
  align-items: flex-start;
  gap: 10px;
}
.wallet-payment-option .form-check-input {
  margin-top: 0;
}
.wallet-payment-option .form-check-label {
  flex: 1;
  cursor: pointer;
}
.wallet-payment-option .form-check-label strong {
  font-size: 16px;
  display: block;
  margin-bottom: 5px;
}
.wallet-balance {
  font-size: 14px;
}
.wallet-amount {
  color: #4CAF50;
  font-weight: 600;
}
.cd-property-detail{
  display: flex;
  gap: 15px;
  margin-bottom: 5px;
  flex-direction: column;
  align-items: start;
}
.cd-property-detail img {
  width: 100%;
  height: 200px;
  border-radius: 12px 12px 0 0;
  object-fit: cover;
}
.cd-pd-content{
  flex: 1;
}
.cd-pd-content h5{
  font-weight: 700;
  margin-bottom: 5px;
}
.ct-content{
  padding: 0 7px;
}
.ct-content h6{
  color: #333;
}
.ct-content p{
  font-size: 14px;
}
.checkout-summary-inner{
  padding: 0 7px;
}
.checkout-summary tr,
.checkout-summary td,
.checkout-summary th{
  border: none;
}
.checkout-summary td{
  padding-top: 0;
}
.checkout-summary .summary-total th{
  padding-bottom: 0;
  border-top: 2px solid var(--grey-one);
  color: var(--dr-shade-gray);
}
.checkout-registeration .phone-number input{
  border: none;
}
.property-inner-detail .product-rate{
  max-width: none !important;
}
.nInbox #no-detail-panel h6{
  text-align: center;
  text-transform: capitalize;
}
.nInbox .no-detail-panel-inner{
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  gap: 10px;
  height: calc(100% - 120px);
}
.pg-slide{
  position: relative;
}
.custom-tag{
  display: flex;
  align-items: center;
  font-weight: 600;
  padding: 9px 12px;
  border-radius: 25px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  white-space: nowrap;
  position: absolute;
  z-index: 99;
  width: max-content;
  line-height: 1;
}
.exclusive-tag{
  font-size: 15px;
  color: #323232;
  background: #fff3cd;
  bottom: 25px;
  left: 10px;
}
.pb-exclusive-tag{
  bottom: 5px;
  right: 5px;
  font-size: 14px;
  color: #323232;
  background: #fff3cd;
}
/* booking collapse */
.booking-accordion{
    margin-bottom: 10px;
}
.booking-accordion .booking-accordion-header{
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex: 1;
    padding-right: 10px;
}
.booking-accordion .accordion-button{
    padding: 16px 7px;
}
.accordion-header .accordion-button:focus{
    box-shadow: none;
}
.accordion-header .accordion-button:not(.collapsed){
    background-color: #FFFBF2;
    color: #000000;
}
.accordion-header .accordion-button:not(.collapsed)::after{
    background-image: var(--bs-accordion-btn-icon);
}
.booking-accordion .accordion-body{
    padding: 16px 10px;
    max-height: 168px;
    overflow: auto;
}
.booking-accordion .accordion-body .table{
    margin-bottom: 0;
}
.booking-accordion .accordion-body .table>:not(caption)>*>*{
    padding: .3rem .3rem;
    border: 0;
}
.booking-accordion .accordion-body .table-bordered>:not(caption)>*{
    border: none;
}
.single-property-float-share{
  position: absolute;
  top: 31px;
  right: 60px;
  background: #fff;
  z-index: 9;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 6px 4px 16px #0000000A;
}
.single-property-float-share img{
  width: 16px;
}
.property-preview img{
  width: 100%;
  height: 150px;
  border-radius: 8px;
  object-fit: cover;
}
.ss-property-content p{
  color: #000000;
  margin-top: 10px;
}
.ss-property-btns{
  margin-top: 20px;
}
.ss-property-btns button{
  border: 1px solid var(--grey-one);
  border-radius: 12px;
  display: flex;
  align-items: center;
  gap: 5px;
  width: 100%;
  padding: 8px;
  transition: .3s ease-in-out;
  font-size: 14px;
  color: #000000;
}
.ss-property-btns button:hover{
  background-color: #F7F7F7;
}
.ss-property-btns button i{
  font-size: 20px;
}
#twitter-share-btn img{
  width: 20px;
  height: auto;
}
.mb-card-share-modal .ss-property-btns button{
  padding: 15px !important;
}
.mb-card-share-modal .modal-dialog{
  z-index: 999 !important;
}
/*new property card*/
/* new property card */
.new-property-card {
  overflow: hidden;
  background: #fff;
  border: 1px solid #6A6A6A33;
  border-radius: 16px;
  margin-bottom: 25px;
}

.new-property-card .card-img-top {
  border-radius: 0;
}

.new-property-card .np-image-container {
  position: relative;
}
.np-image-container .not-available-overlay{
  filter: grayscale(100%);
}

.new-property-card .np-image-container .np-property-image{
  height: 320px;
  width: 100%;
}

.new-property-card .np-image-container .np-property-image img {
  object-fit: cover;
  border-radius: 16px;
  height: 100%;
  width: 100%;
}
.swiper-pagination-bullet{
  background: #f5c33e !important;
}
.new-property-card .np-badge-overlay {
  position: absolute;
  top: 10px;
  left: 10px;
  display: flex;
  z-index: 9;
  gap: 8px;
}

.new-property-card .np-badge {
  font-size: 12px;
  padding: 4px 8px;
  gap: 4px;
  display: flex;
  align-items: center;
  font-weight: 500;
  border-radius: 25px;
  line-height: 1;
  backdrop-filter: blur(8px);
}

.new-property-card .badge-view {
  background-color: #00000099;
  color: white;
  border: 1px solid #6A6A6A;
}

.new-property-card .badge-exclusive {
  background-color: #F5C33E99;
  color: #000;
  border: 1px solid #F5C33E;
}
.new-property-card .badge-exclusive img{
  width: 16px;
}
.new-property-card .badge-not-available {
  background-color: #FFFFFF80;
  color: #D1372E;
  border: 1px solid #ffffff;
}

.new-property-card .np-action-icons {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  z-index: 9;
}

.new-property-card .np-action-icons .icon-btn {
  background-color: #D9D9D9CC;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.new-property-card .np-action-icons .icon-btn .fav-in{
  line-height: 1;
  top: unset;
  left: unset;
  right: unset;
  z-index: unset;
}
.new-property-card .np-action-icons .icon-btn .fav-in img{
  width: auto;
}

.property-features-badges{
  position: absolute;
  bottom: 10px;
  left: 10px;
  z-index: 9;
}

.property-features-badges .pf-badge{
  display: flex;
  align-items: center;
  gap: 3px;
  border-image-slice: 1;
  border-radius: 6px;
  padding: 2px 8px 2px 2px;
  font-size: 12px;
  font-weight: 700;
  line-height: 1;
}
.pf-badge-high-demand{
  background: #E86629;
}
.pf-badge-top-feature{
  background-color: #B3A5E9;
}

.new-property-card .np-card-body {
  padding: 20px 12px 12px 12px;
}

.new-property-card .np-features {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.new-property-card .np-features span {
  display: flex;
  align-items: center;
  padding: 4px;
  border-radius: 4px;
  gap: 4px;
  font-size: 14px;
  background-color: #E9ECEF;
  color: #4F4F4F;
}

.new-property-card .np-card-title-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
}

.new-property-card .np-card-title {
  font-size: 16px;
  margin-bottom: 0;
}

.new-property-card .np-location {
  font-size: 12px;
  margin-bottom: 0;
  margin-top: 4px;
  display: flex;
  align-items: center;
  gap: 4px;
}
.new-property-card .np-location a{
  color: #777;
}
.new-property-card .np-location span {
  text-decoration: underline;
}

.new-property-card .np-rating {
  font-size: 12px;
  font-weight: 400;
  display: flex;
  gap: 3px;
  align-items: center;
}

.new-property-card .np-total-rating {
  font-weight: 700;
}

.new-property-card .np-prices {
  margin-top: 10px;
  display: flex;
  gap: 10px;
  font-size: 14px;
  color: #6A6A6A;
  justify-content: space-between;
}
.np-price-per-night{
  display: flex;
  align-items: center;
  gap: 4px;
}
.np-price-left{
  flex: 1;
}
.np-price-discount-per{
  display: flex;
  background: #6AB988;
  color: #0A3219;
  font-weight: 700;
  padding: 1px 4px 0 4px;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
}

.new-property-card .np-price-per-night .np-total-price {
  font-weight: 700;
  font-size: 16px;
  color: #000000;
}

.new-property-card .total-price-link {
  color: #6c757d;
  text-decoration: underline;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 2px;
  width: max-content;
}
.new-property-card .tp-link-amount {
  font-weight: 500;
}


.total-price-link {
  color: #6A6A6A;
  text-decoration: underline;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 2px;
}

.price-after-discount {
  display: inline-flex;
  align-items: center;
  gap: 2px;
  border: 1px solid #23BC4C;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 500;
  background-color: #fff;
  overflow: hidden;
}
.pa-discount-icon{
  background: #2A9B54;
  height: 100%;
  display: flex;
  align-items: center;
  padding: 0 8px;
}
.price-after-discount img {
  width: 15px;
  height: 16px;
}
.pa-discount-text{
  padding: 0 5px;
}
.pa-discount-text p{
  color: #23BC4C;
  font-weight: 500;
}
.pa-dt-text span:nth-child(2){
  padding-left: 6px;
  position: relative;
}
.pa-dt-text span:nth-child(2)::after{
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  width: 3px;
  height: 3px;
  background: #2A9B54;
  border-radius: 50%;
  transform: translateY(-50%);
}
.total-price-main-desktop{
  display: none;
}
.total-price-main-mobile{
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
}
/* Price Details Offcanvas */
.offcanvas.price-details-offcanvas{
  --bs-offcanvas-height: auto !important;
}
.price-details-offcanvas {
  border-top-left-radius: 24px;
  border-top-right-radius: 24px;
  padding: 16px;
  max-height: 50vh;
}

.price-details-offcanvas .offcanvas-header {
  border-bottom: none;
  justify-content: end;
  position: relative;
  padding: 0;
  margin-bottom: 25px;
}

.price-details-offcanvas .offcanvas-title {
  font-weight: 700;
  font-size: 20px;
  margin-bottom: 20px;
}

.price-details-offcanvas .btn-close {
  background: none;
  border: none;
  padding: 0 !important;
  position: absolute;
  top: 15px;
  right: 25px;
}
.price-details-offcanvas .btn-close img{
  width: 25px;
}
.price-details-offcanvas .offcanvas-body {
  padding: 16px 0 0 0;
  overflow-y: unset;
}

.price-details-offcanvas .price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;
  font-size: 16px;
  color: #4F4F4F;
}

.price-details-offcanvas .price-row span:last-child {
  font-weight: 500;
  color: #000;
}

.price-details-offcanvas .price-row .riyal-symbol {
  width: 12px;
  height: 12px;
  margin-left: 2px;
  vertical-align: baseline;
}

.price-details-offcanvas .discount {
  color: #198754 !important;
}

.price-details-offcanvas .total-row {
  font-weight: 700;
  font-size: 18px;
  color: #000;
  border-top: 1px solid #E9ECEF;
  padding-bottom: 0;
  padding-top: 15px;
  margin-top: 10px;
}
@media (max-width: 475px){
  .checkout-summary td{
    font-size: 14px;
  }
  .checkout-summary .summary-total{
    padding: 0 7px;
  }
  .checkout-summary .summary-total th{
    font-size: 14px;
  }
  .cd-pd-content h5{
    font-size: 18px;
  }
  .checkout-detail-title{
    font-size: 18px;
  }
}
@media(max-width: 374px){
  .lp-header-content{
    font-size: 14px;
  }
  .lp-header-btn{
    font-size: 13px;
    width: 150px;
  }
  .lp-card-btn button{
    padding: 8px 10px;
    font-size: 13px;
  }
  .lp-card h1{
    font-size: 20px;
  }
  .lp-body .lp-body-content h1{
    font-size: 20px;
  }
  .lp-footer{
    margin: 20px 0;
  }
  .cd-property-detail img{
    height: 150px;
  }
}