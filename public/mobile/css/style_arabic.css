.feature .bg,
.floated-img,
.mb-slid.slick-initialized.slick-slider {
    direction: ltr;
}

.floated-img,
a.dropdown-item,
img.fl-1,
img.fl-2,
img.fl-3,
img.fl-4 {
    direction: rtl !important;
}

.acc-inner ul li .ac-act,
.rcpt-btn::before,
header .search-reservation li::before {
    left: 0;
    right: unset;
}

img.fl-1 {
    right: -3%;
}

img.fl-2 {
    right: 10%;
}

img.fl-3 {
    right: 16%;
}

img.fl-4 {
    right: 33%;
}

img.fl-5 {
    right: 47%;
    z-index: 8;
}

img.fl-6 {
    right: 56%;
}

img.fl-7 {
    right: 70%;
    z-index: 4;
}

img.fl-8 {
    right: 78%;
}

header .nav .user .nav-menu {
    padding: 5px 5px 4px 7px;
}

.chat-text,
header .nav .nav-menu .user-profile {
    margin-right: 0;
    margin-left: 10px;
}

.notification-drop a {
    position: relative;
    margin-right: 10px;
    margin-left: 10px;
}

.navmenu ul.text-list li a {
    margin-right: 0px;
    margin-left: 20px;
}

.feature-content {
    text-align: right;
    justify-content: flex-end;
}

.feature .bg .d img {
    right: 29px;
    left: 70px;
}

footer .social-icon li {
    padding-right: 0;
    padding-left: 10px;
}

.social-signup-btn a button,
.total-guest .content,
[dir="rtl"] .pr-mini-detail h4,
[dir="rtl"] .textright-rtl,
a.dropdown-item,
footer .foot-nav li:nth-child(2n),
p.uncomp {
    text-align: right;
}

.custom-dropdown-menu li a {
    padding: 5px 15px 7px 5px;
    direction: rtl !important;
    text-align: right;
}

[dir="rtl"] .user-name {
    padding: 5px 0 7px 5px;
    text-align: right;
}

.social-signup-btn {
    justify-content: space-between !important;
    direction: rtl;
}

.notification-userimg,
.social-signup-btn .al-account {
    margin-right: 0;
    margin-left: 15px;
}

.social-signup-btn button img {
    margin-right: 0;
    width: 22px;
    margin-left: 10px;
}

.social-signup-btn button h6 {
    margin-bottom: -4px;
}

.mr-14 {
    margin-right: 0;
    margin-left: 14px;
}

.prev-btn img,
.social-signup-btn button i {
    margin-right: 0;
    margin-left: 12px;
}

.phone-number input {
    border-left: none;
    border-right: 1px solid #e2e2e2;
    padding-right: 8px;
}

.phone-number input:focus {
    border-left: unset;
    border-right: 1px solid #e2e2e2;
}

.phone-number select {
    margin-right: 0;
    direction: ltr;
    padding-left: 20px;
    text-align: right;
}

.product-category-2 li:first-child .listing-checkbox-wrapper.fsrch,
.social-btn-login a button img {
    margin-right: 0;
}

select {
    background-position: 4% 55% !important;
}

.side-menu-inner {
    border-right: none;
    border-left: 1px solid #bfbbbb8f;
}

ul.side-inner li a {
    direction: rtl;
    display: flex;
    align-items: center;
}

ul.side-inner li a i {
    top: 2px;
    margin-right: 0;
    margin-left: 10px;
}

[dir="rtl"] .host-sc img.fl-9 {
    right: 47px;
    left: unset;
}

[dir="rtl"] .host-sc img.fl-10 {
    right: 277px;
    left: unset;
}

[dir="rtl"] .host-sc img.fl-11 {
    left: 68px;
    right: unset;
}

[dir="rtl"] .host-sc img.fl-12 {
    left: -230px;
    right: unset;
}

[dir="rtl"] .host-sc img.fl-13 {
    left: -320px;
    right: unset;
}

[dir="rtl"] .host-help::before {
    right: 0;
    left: unset;
}

[dir="rtl"] .host-help .help-content li img {
    margin-right: 0;
    margin-left: 40px;
}

[dir="rtl"] .listing-category .category .category-content img {
    right: 40px;
    left: unset;
}

[dir="rtl"] .black-btn a {
    position: relative;
}

[dir="rtl"] .black-btn img {
    top: 55%;
}

[dir="rtl"] .listing-position {
    right: 200%;
}

[dir="rtl"] .cust-accordion .accordion-button::after {
    margin-left: 0;
    margin-right: auto;
}

[dir="rtl"] .listing-place-name .listing-input {
    padding: 10px 12px 0 0;
}

[dir="rtl"] .cust-check label {
    margin-right: 20px;
    margin-left: 0;
}

[dir="rtl"] .filter-check .filter-check-inner p {
    text-align: start;
}

.ls-actions,
[dir="rtl"] .textleft-rtl,
p.comp {
    text-align: left;
}

[dir="rtl"] .wait-approval i {
    margin-left: 8px;
    margin-right: 0;
}

[dir="rtl"] .pr-img {
    margin-left: 5px;
    margin-right: 5px;
}

[dir="rtl"] .property-detail .property-feature-list li img {
    margin-left: 10px;
    margin-right: 0;
}

[dir="rtl"] .custom-subtotal {
    padding: 0 14px;
}

.property-detail .property-inner-detail .popup-user img {
    width: 45px;
    margin-right: 0;
    margin-left: 15px !important;
}

.popup-font {
    text-align: right !important;
}

.popup-check img {
    transform: rotate(179deg) !important;
}

.right-content.pr-cn {
    flex-wrap: wrap !important;
}

header .nav .header-btn {
    padding-right: 0;
    padding-left: 10px;
}

.product-category-2 li:last-child .listing-checkbox-wrapper.fsrch {
    margin-right: 10px;
}

.prev-btn,
.services .services-btn .filter-btn .inner-btn {
    margin-right: 15px;
    margin-left: 0px;
}

.clndr-btn,
.services .services-btn .filter-btn .inner-btn:nth-child(3),
.wait-approval {
    margin-left: 0;
}

.services .services-btn .filter-btn .inner-btn img {
    padding-right: 0;
    padding-left: 10px;
}

.services .services-btn .filter-btn .inner-btn:first-child img {
    padding-left: 0;
}

.ntf-main {
    transform: translate(128px, 35px) !important;
}

.side-list {
    padding: 0 0 0 25px;
}

.wait-approval {
    margin-right: auto;
}

.resrvbd-left {
    border-right: none !important;
    border-left: 1px solid var(--grey-one);
}

.clndr-btn {
    margin-right: 15px;
}

.clndr-btn img,
.next-btn img {
    margin-left: 0;
    margin-right: 12px;
}

.text-left {
    text-align: left !important;
}

.reservation-calend {
    left: -100%;
    right: unset;
}

.list-rate img {
    margin-right: 0;
    margin-left: 4px;
}

.slide-btn img {
    transform: rotate(180deg);
}

.chat-name p {
    margin-right: 7px;
}

.chat-profile-list {
    padding-right: 0;
    padding-left: 20px;
}

.product-rate img {
    margin-left: 5px;
    margin-right: 0 !important;
}

.inbox-property-img {
    margin-left: 10px;
    margin-right: 0;
}

.bk-pn-btn {
    display: flex;
    flex-flow: row-reverse;
    margin-left: 20px;
}

.calendar-month .month-nav-next {
    margin-right: 0;
}

.d-agent-img {
    margin: 0 0 0 15px;
}

.cs-chat-main {
    padding: 0 0 0 5px;
}

.cs-cf-btn {
    right: auto;
    left: 10px;
}

.cs-chat-field input {
    padding: 12px 12px 12px 55px;
}

.acc-inner-group .account-inner .mb-account-layout.hs-ac-btn svg {
    margin: 0 0 0 14px;
}

.pay-sec {
    margin: 20px 14px 20px 15px;
}

.chat-property-media img {
    margin: 10px 48px 0 0;
}

.ch-sec-profile img {
    margin: 0 0 0 20px;
}

.hm-prop-content ul li img {
    margin: 0 0 2px 2px;
}

.contact-hs-inner {
    padding: 0 0 0 30px;
}

.ch-hb-btn img {
    margin: 0 0 2px 8px;
}

.mt-ask-content p {
    padding: 0 15px 0 0;
    margin: 0 20px 0 0;
}

.mt-ask-content p::before {
    left: auto;
    right: 0;
}

@media (max-width: 1680px) {
    .ntf-main {
        transform: translate(-129px, 35px) !important;
    }
}

@media (max-width: 1366px) {
    header .search-reservation li {
        width: 26%;
    }

    header .search-reservation li:nth-child(2) {
        width: 38%;
    }

    [dir="rtl"] .host-sc img.fl-10 {
        right: 207px;
    }

    [dir="rtl"] .host-sc img.fl-11 {
        left: 138px;
    }

    [dir="rtl"] .host-sc img.fl-12 {
        left: -90px;
    }

    [dir="rtl"] .host-sc img.fl-13 {
        left: -190px;
    }

    .services .services-btn .filter-btn .inner-btn {
        margin-right: 0;
        margin-left: 15px;
    }

    .ntf-main {
        transform: translate(114px, 62px) !important;
    }
}

@media (max-width: 1024px) {
    [dir="rtl"] .host-sc img.fl-13 {
        left: -100px;
    }

    [dir="rtl"] .host-sc img.fl-12 {
        left: -60px;
    }

    .services .services-btn .filter-btn .inner-btn:first-child {
        margin-right: 0;
        margin-left: 45px;
    }

    .services .services-btn .filter-btn .inner-btn:nth-child(2) {
        margin-left: 0;
    }

    .ntf-main {
        width: 240px !important;
        transform: translate(-100px, 35px) !important;
    }


    .form-control,
    .services .services-btn .discover-btn button {
        height: 43px;
    }



}

@media (max-width: 600px) {

    header .popup-main .popup.guest-popup {
        left: 29px !important;
        right: 23px;
    }

    header .popup h3 {
        text-align: right;
    }

    header .search-reservation li:last-child {
        padding-right: 0 !important;
    }

    header .nav .nav-menu .user-profile {
        margin-right: 0;
    }

    .popup-font {
        text-align: right !important;
    }

    .fl-on-mb {
        position: absolute;
        top: 15px;
        right: 0;
    }

    .services .services-btn .filter-btn .inner-btn:first-child {
        margin-right: 0;
        margin-left: 0;
    }

    .filter-btn .custom-dropdown-menu {
        left: auto !important;
    }

    .filter-btn .custom-dropdown-menu {
        right: auto !important;
    }
}


.am-icon {
    margin-right: 0px;
    margin-left: 8px;
}

.promo-bx {
    direction: ltr;
}

button.gr-promo-btn {
    right: auto;
    left: 0;
    border-radius: 18px 0px 0px 18px;
}

.md-clndr-icon {
    right: auto;
    left: 20px;
}

.bootstrap-select .dropdown-toggle .filter-option-inner-inner {
    text-align: right;
}

.multi-slt i {
    right: auto;
    left: 13px;
}

.show-tick.cstm-slt-pick button {
    padding-left: 44px;
    padding-right: 25px;
}

.alert.alert-danger ul li {
    direction: ltr;
}

.alert.alert-danger ul li i {
    float: right;
    padding-left: 3px;
}

button.pd-promo-btn {
    left: 0;
    right: auto;
    height: 43px;
    border-radius: 18px 0px 0px 16px;
}

.form-check .form-check-input {
    margin-left: 0.5em;
}

span.close-report {
    float: left;
    font-size: 24px;
    position: relative;
    bottom: 22px;
    left: -16px;
}

.ticket-table .list-content {
    float: right;
}

.ticket-table .dataTables_wrapper .dataTables_filter {
    /* float: left !important; */
    float: right !important;
}

a.report-pr {
    right: auto;
    left: 6px;
    align-items: center;
    display: flex;
}

a.report-pr i {
    top: 0px;
    padding-left: 3px;
}

.mini-profile [dir="rtl"] .pr-img {
    margin-right: 14px;
}

a.sign-in-btn {
    margin-right: 0;
    margin-left: 20px;
}


.filter-box {
    left: auto;
    right: 19%;
}

.search-reservation li span {
    text-align: right !important;
}

.popup1 {
    left: auto;
    right: 0;
}

.fd-icon {
    margin-right: 0;
    margin-left: 15px;
}

.popup h3 {
    text-align: right;
}

.downApp h2 {
    display: flex;
}

.store-link {
    text-align: left;
}

.for-ar {
    display: inherit;
}

.for-eng {
    display: none;
}

[dir="rtl"] .slick-next::before {
    content: '\f053' !important;
}

[dir="rtl"] .slick-prev::before {
    content: '\f054' !important;
}

[dir='rtl'] .slick-next {
    right: auto;
    left: 50px;
}

[dir='rtl'] .slick-prev {
    right: 50px;
    left: auto;
}


header .nav .header-btn {
    padding-right: 20px;
    padding-left: 30px;
}

/* new design style */
.md-pb-pstatus {
    padding: 0 0 0 20px;
}

.md-pb-bio ul li .md-pb-bioicon {
    margin-right: 0px;
    margin-left: 7px;
}

.md-user-identity ul li i {
    margin-left: 10px;
    margin-right: 0;
}

.md-user-about ul li i {
    margin-right: 0;
    margin-left: 10px;
}

.rprt-btn i {
    margin-left: 8px;
    margin-right: 0;
}

.headBtn .swiper-button-prev {
    left: 0;
    right: auto;
}

.headBtn .swiper-button-next {
    left: 40px;
    right: auto;
}

.md-rb-usreview img {
    margin: 0 0 0 10px;
}

.sl-btn img {
    margin: 0 0 0 20px;
}

/* .share-si ul li {
    margin: 0 0 0 30px;
} */

.dwc-inner label {
    left: auto;
    right: 20px;
}

.wl-property .pg-head h2 {
    margin: 0 24px 0 0;
}

.wl-right-btn button:nth-child(2) img {
    margin: 0 10px 0 0;
}

.wb-right {
    margin-left: 0;
    margin-right: 4px;
}

.wlisting-inner img {
    margin: 0 0 0 8px;
}

.rtl-leftArrowRotat {
    rotate: 180deg;
}

.cm-simple-header .btn-close {
    right: auto;
    left: 20px;
}

.type-place .input-container:nth-child(1) .radio-tile {
    border-top-right-radius: 10px;
    border-bottom-right-radius: 10px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-left: 0;
    border-right: 1px solid;
}

.type-place .input-container:nth-child(3) .radio-tile {
    border-top-left-radius: 10px;
    border-bottom-left-radius: 10px;
    border-right: 0;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-left: 1px solid;
}

.rb-input-container:first-child {
    margin-right: 0;
}

.main-title {
    text-align: right !important;
}

.inner-main-title {
    text-align: right;
}

.gr-btn button img {
    margin: 0 0 0 10px;
}

.ll-one-smallrate img {
    margin: 0 0 0 8px;
}

.ll-tl-img {
    margin: 0 0 0 20px;
}

.date-mark span {
    margin: 0 0 0 9px;
}

.ps-btn {
    margin: 0 0 0 15px;
}

.gr-inner img {
    margin: 0 0 0 5px;
}

.theme-user-rate img {
    margin: 0 0 0 6px;
}

.navmenu ul.text-list li {
    padding-right: 9px;
}

.popup2 {
    right: auto;
    left: 0;
}

.pr-list li a i {
    margin-right: 0;
    margin-left: 9px;
}

.currancy-dropdown {
    margin-right: 0px;
    margin-left: 20px;
}

.row-rev {
    flex-direction: row-reverse;
}

.rb-input-container:last-child {
    margin-right: 11px;
}

.mb-right-arrow {
    left: 5px;
    right: auto;
    rotate: 180deg;
}

.mb-account-layout .mb-al-img {
    margin: 0 0 0 20px;
}

.mb-al-icon {
    margin: 0 0 0 11px;
}

.acc-inner-group:nth-child(4) .account-inner:nth-child(3) .mb-account-layout svg {
    margin: 0 0 0 14px;
}

.notification-main {
    padding: 10px 0px 0 12px;
}

.nt-userdetail img {
    margin: 0 0 0 10px;
}

.notification-userinner {
    margin: 0 0 0 7px;
}

.nt-recive-content {
    margin: 0 10px 0 10px;
}

.mb-mtop-inner li a span {
    margin: 0 10px 0 0;
}

.mb-mtop-inner li a {
    margin: 0 0 0 20px;
}

.mb-mtop-inner li:last-child a {
    margin: 0;
}

.support-detail {
    margin: 0 15px 0 0;
}

.support-detail:before {
    right: 47px;
    left: auto;
    rotate: 180deg;
}

.support-box:after {
    right: 19px;
    left: 0;
}

.ticket-dbody {
    padding: 0 0 0 5px;
}

.chat-name {
    margin: 0 15px 0 0;
}

.nInbox .chat-field .chat-field-btn {
    left: 13px;
    right: auto;
}

.nInbox .chat-field .atach-icon img {
    margin: 0 0 0 10px;
}

.chat-view-main .chat-head h5 {
    margin: 0 10px 0 0;
}

.nInbox .chat-field input {
    padding: 10px 15px 12px 0px;
}

.chat-profile h6 {
    margin: 0 55px 0 0 !important;
}

.cm-group .chat-property-text p {
    margin: 0 55px 0 0 !important;
}

.cm-group .chat-property-media {
    margin: 0 55px 0 0;
}

.chat-up-status {
    padding: 0px 15px 0 0;
    background-position: bottom 2px right -2px !important;
}

.chat-up-icon {
    right: auto;
    left: 5px;
}

.mb-user-acc .mb-user-img {
    margin: 0;
}

.mb-acc-tick {
    margin: 0 0 0 15px;
}

.total-list li {
    padding: 0;
}

.cd-darent img {
    margin: 7px 0 0 18px;
}

.br-cont {
    padding: 0 10px 0 0;
}

.listing-footer .black-btn img {
    width: 9px;
    margin-left: 4px;
    margin: 0 0 0 4px;
}

.rb-input-container {
    margin: 0 0 0 11px;
}

.rb-input-container:last-child {
    margin: 0 0 0 11px;
}

.sh-img-ar {
    display: block;
}

.sh-img-en {
    display: none;
}

.second-home .sc-home {
    margin: 0 0 0 100px;
}

.back-line {
    margin: 0 0 10px auto;
}

.en-position {
    flex-direction: row-reverse;
}




@media(max-width: 991px) {
    .navmenu ul.text-list li .tick-icon {
        left: 37px;
    }

    .navmenu ul.text-list li a {
        text-align: center;
    }

    .fl-on-mb {
        left: auto;
        right: 0;
    }

    header .nav .header-btn {
        padding-left: 0;
    }
}

.ui-calendar i {
    right: auto;
    left: 15px;
    top: 7px;
}

.review-progress-count {
    margin-left: auto;
    margin-right: 20px;
}

.downApp h2 {
    display: flex;
    flex-direction: row-reverse;
    font-size: 45px;
    justify-content: center;
}

span.fc-white:before {
    content: '';
    position: absolute;
    background: black;
    height: 90px;
    width: 100%;
    top: -30px;
    border-radius: 0px 0px 10px 10px;
    left: 0;
    z-index: 1;
}

.blk-bar {
    display: none;
}

.downApp .fc-white span {
    position: relative;
    z-index: 2;
    text-transform: uppercase;
}

.accordion-button::after {
    margin-right: auto;
    margin-left: 0px;
}

/* .property-inner-detail .product-rate i {
    padding-right: 0px !important;
    padding-left: 6px;
} */

.ui-fl {
    font-size: 9px;
}

[dir="rtl"] .cust-check label {
    margin-right: 0;
    margin-left: 1px;
    text-align: right;
}

.views i {
    margin-right: 0px;
    margin-left: 5px;
}

.accordion-button::after {
    margin-left: 0;
}

.show-calendar-btn img {
    margin-left: 8px;

}

label.listed {
    margin-right: 0;
    margin-left: 10px;
}

.ls-edits a {
    font-size: 17px;
    margin-left: 0;
    margin-top: 10px;
}

.edit-sc .tabs-sc button.slick-next {
    right: 97% !important;
}

.edit-sc .tabs-sc button.slick-prev {
    left: auto !important;
    top: 21px;
    background: transparent !important;
    right: -30px !important;
}


.verification-code--inputs {
    direction: ltr;
}

button.btn-fliter i {
    transform: rotateZ(271deg);
    display: flex;
    align-items: center;
    justify-content: center;
}

.carrent-loc-btn {
    color: #ffff;
}

.otp-input-wrapper input {
    direction: ltr;
    display: flex;
}


/* host journey arabic */
.hs-property img {
    margin-right: 0px !important;
    margin-left: 10px !important;
}

.list-tb-listing img {
    margin-right: 0;
    margin-left: 10px;
}

.host-navbar li:last-child {
    margin-right: 40px !important;
}


.cl-ar .ri-arrow-right-s-line:before {
    content: "\ea64";
}

.p-tabview-nav {
    flex-wrap: wrap-reverse !important;
    flex: 1 1 auto;
    direction: ltr;
    justify-content: end;
}

.hs-property p {
    margin-right: 10px;
}

/* .p-icon,.dr-cust-arrow-btn button i {
    transform: scaleX(-1);
} */
.overlay-group .p-overlaypanel {
    right: auto !important;
    left: 0 !important;
}

.p-button .p-button-icon-right {
    margin-left: auto;
    margin-right: 10px;
}

.hs-export-dropdown ul li a {
    display: flex;
    width: 100%;
    justify-content: space-between;
}

.hs-fil-body .hs-fil-input-right {
    border-radius: 8px 0px 0px 8px;
}

.hs-fil-body .hs-fil-input-left {
    border-right: 1px solid !important;
    border-left: none !important;
    border-radius: 0px 8px 8px 0px;
}

.p-datatable .p-datatable-tbody>tr>td {
    text-align: right !important;
}

.hs-custarrow-div .hs-ca-div-content img {
    rotate: 0deg;
}

.morepotos {
    right: auto;
    left: -2px;
    background: linear-gradient(to left, rgb(255 255 255 / 0%), rgb(255 255 255 / 89%) 59%, rgb(255, 255, 255)) !important
}

label.hs-ls-it input {
    margin: 4px 0 0 10px;
}

.hostdash-total-guest .content {
    margin: 0 0 0 30px;
}

.hs-tb-actions {
    text-align: left;
}

.dr-cust-arrow-btn {
    flex-direction: row-reverse;
}

.dr-cust-prev {
    margin: 0 10px 0 0;
}

.pr-mini-detail {
    margin: 0 5px;
}

.container-host .ntf-main {
    transform: translate(-90px, 35px) !important;
}

.upload__box .col-6.text-right {
    text-align: left !important;
}

.cover-photo .text-right {
    text-align: left;
}

.pay-sec .pay-sec-label input[type="radio"] {
    margin-right: 0;
    margin-left: 10px;
}

.district-slt-picker-main .dropdown-toggle::after {
    right: auto;
    left: 0;
}

.district-slt-picker-main .bootstrap-select.show-tick .dropdown-menu .selected span.check-mark {
    right: auto;
    left: 15px;
}

.district-slt-picker-main .dropdown-menu .dropdown-menu.inner .dropdown-item .text {
    margin: 0 0 0 34px;
}

.district-slt-picker-main .btn-light.dropdown-toggle {
    padding: 9px 0 6px 15px;
}

.text-right-dir {
    text-align: left;
}

.gr-cb-buttons a span {
    margin: 0 5px 0 0;
}

.gr-cb-content .gr-cb-contentLeft::after {
    right: auto;
    left: 25px;
}

.gr-ds-checkIn::before {
    right: auto;
    left: 20px;
}

.gr-ds-hostInfo li .gr-ds-hostInfo-inner {
    margin: 0 15px 0 0;
}

.gr-ds-termPolicies ul li {
    padding: 0 15px 0 0;
}

.gr-ds-termPolicies ul li::after {
    left: auto;
    right: 0;
}

.gr-ds-contIcon .gr-ds-dc-innerContent {
    margin: 0 10px 0 0;
}

.gr-detail-header h6 {
    margin: 0 10px 0 0;
}

.gr-ci-infoBtn a img {
    margin: 0 0 0 10px;
}

.gr-ci-infoBtn a i {
    margin: 0 0 0 10px;
}

.gr-ds-hr-checkInOut-list li i {
    margin: 0 0 0 10px;
}

.select-op h3 {
    margin-left: 0px;
    margin-right: 8px;
}

ul.cs-amt li i {
    margin-right: 0;
    margin-left: 7px;
}

.sr-sidebar-title i {
    margin-right: 0;
    margin-left: 10px;
}

.sr-sidebar-progress .review-progress p {
    margin-right: 0;
    margin-left: 10px;
}

.sr-sidebar-rate-inner {
    border-left: none;
    border-right: 1px solid #bfbbbb8f;
}

.sr-profile .sr-profile-name {
    margin-left: 0;
    margin-right: 10px;
}

.sr-cb-time {
    margin-left: 0;
    margin-right: 10px;
}

.sr-comment-host {
    margin: 0 25px 0 0;
}

.sr-content-search input {
    padding-left: 0;
    padding-right: 40px;
}

.sr-content-search i {
    left: unset;
    right: 15px;
}

.tb-how {
    text-align: right !important;
    /* margin-top: 25px; */
}

.tb-card {
    text-align: left;
}

span.tb-num {
    margin-right: 0px;
    margin-left: 8px;
}

.pay-sec .pay-sec-label .pay-sl-content img {
    margin-right: 0;
    margin-left: 5px;
}

.ch-wallet-pay label {
    margin: 0 0 0 10px !important;
}

.ch-wallet-pay label::before {
    left: auto;
    right: 4px;
}

.ch-wallet-pay input:checked+label::before {
    left: auto;
    right: 19px;
}

.content-point {
    padding: 0 30px 0 0;
}

.insurance-cover .insurance-cover-list li .ic-list-icon img {
    margin-right: 0;
    margin-left: 10px;
}

.inurance-limit .ic-box-icon {
    margin-right: 0;
    margin-left: 20px;
}

.inurance-claim .insurance-claim-list li {
    padding-left: 0;
    padding-right: 20px;
}

.inurance-claim .insurance-claim-list li::before {
    left: auto;
    right: 0;
}

.incurance-contact-ic img {
    margin-right: 0;
    margin-left: 5px;
}

.list-skip-btn {
    margin-right: 0;
    margin-left: 5px;
}

.instruction .instruct-list {
    margin: 15px 15px 0 0;
}

.instruction .instruct-list li:before {
    left: unset;
    right: -18px;
}

.instruct-reg-prop-sec .instruct-list {
    margin: 25px 0 0 0;
}

.instruct-reg-prop-sec .instruct-list li:before {
    left: unset;
    right: 0;
}

.instruction-tabing.nav-pills .nav-item:first-child .nav-link {
    border-radius: 0 25px 25px 0;
}

.instruction-tabing.nav-pills .nav-item:last-child .nav-link {
    border-radius: 25px 0 0 25px;
    border-left: inherit;
    border-right: unset;
}

.instruct-reg-prop-sec .instruct-list li p {
    margin-left: unset;
    margin-right: 55px;
}

.listing-item-main {
    padding: 0 0 0 10px;
}

.create-new-info i {
    transform: rotate(180deg);
}

.property .product .product-detail .title h4 {
    margin-right: 0 !important;
    margin-left: 10px;
}

.lf-rv-img-en,
.rt-rv-img-en {
    display: none;
}

.lf-rv-image-ar,
.rt-rv-img-ar {
    display: block;
}

span.rv-num {
    margin-right: 0;
    margin-left: 10px;
}

span.amn-img {
    margin-right: 0;
    margin-left: 10px;
}

h6.rev-date.rev-dt {
    right: unset;
    left: 16px;
}

.rev-search i {
    left: unset;
    right: 14px;
}

.rev-search input.form-control {
    padding-left: 0;
    padding-right: 50px;
}

.rev-views-ppl {
    padding-right: 0;
    padding-left: 6px;
}
@font-face {
    font-family: 'DIN-NEXT-ARABIC';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url('fonts/din-next-arabic/DINNextLTArabic-Regular.ttf')
}

.property-pricing h4,
.property-pricing span {
    font-family: 'DIN-NEXT-ARABIC' !important;
}

.product-detail .sar-pr {
    font-size: 21px;

}
.mw-discount-property{
    right: 0;
    left: unset;
    border-radius: 0 10px 0 10px;
  }
.filter-btn button{
    margin-left: 0;
    margin-right: 7px;
}
.fav-icon{
    right: unset;
    left: 0;
}
.fav-in{
    right: unset;
    left: 7px;
}
.mw-discount-property{
    left: unset;
    right: 0;
    border-radius: 0 8px 0 8px;
}
.not-available-tag{
    left: unset;
    right: 5px;
}
.not-available-tag::before{
  margin-right: 0;
  margin-left: 4px;
}
.exclusive-tag{
  left: unset;
  right: 10px;
}
span.pagingInfo{
    right: unset;
    left: 15px;
}
.pb-exclusive-tag{
    left: 5px;
    right: unset;
}
.booking-accordion .booking-accordion-header{
  padding-right: unset;
  padding-left: 10px;
}
.booking-accordion .append_date .text-right{
  text-align: left !important;
}
.new-property-card .np-badge-overlay{
    left: unset;
    right: 10px;
}
.new-property-card .np-action-icons{
    right: unset;
    left: 10px;
}
.price-details-offcanvas .btn-close{
    right: unset;
    left: 25px;
}