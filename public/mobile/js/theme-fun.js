$('.mobile_header_strip').on('click', function() {
    gtag('event', 'click_download_app', {
        event_category: 'download_mobile_app',
        click_location: 'mobile_header_strip',
        event_label: 'Download Mobile App'
    });
});

// $('.mobile_footer_banner').on('click', function() {
//     gtag('event', 'click_download_app', {
//         event_category: 'download_mobile_app',
//         click_location: 'mobile_footer_banner',
//         event_label: 'Download Mobile App'
//     });
// });



$('.rst-form').click(function () {
    $('#float-form')[0].reset();
    $('.popularcity').removeClass('location-act');
    $('.date-ui').val('');
    $('#front-search-field').val('');
});
$(function () {



    $(document).ready(function () {
        $('.onsearch').on('input', function () {
            if ($(this).val().trim() !== '') {

                $('.popup1').removeClass('show');
            } else {
                $('.popup1').addClass('show');
            }
        });

        $(document).on('wheel', function () {

            $('.blkd').click();
            // $('body').removeClass('no-scroll');
            $('.popup-main').removeClass('show');
            $('.pac-logo').remove();
            $('.daterangepicker').css('display', 'none');

        });
    });

    var swiper = new Swiper(".vrslide", {
        direction: "vertical",
        slidesPerView: 10,
        spaceBetween: 30,
        centeredSlides: true,
        loop: true,

    });

    // $(window).on('load', function () {
    //   $('.loadskull').removeClass('loadskull');

    // });



    // BOOK MODAL TEMPORAY OFF (INNER PAGES)
    //   setTimeout(() => {

    //       $(document).on('click', '.user-book', function(e) {
    //         var hostid = $(this).data('hostid');
    //         getprofiledata(hostid);
    //     });
    //     function getprofiledata(hostid) {

    //         $.ajax({
    //           data:{
    //               'hostid':hostid,
    //           },

    //           url: "/getprofiledata",
    //           type: 'get',
    //           dataType: 'json',
    //           beforeSend: function() {
    //               show_loader();
    //           },
    //           success: function(res) {
    //               $('.hostName').text(res.hostName);
    //               $('.hostImage').attr("src", res.hostimage);
    //               $('.hostRating').text(res.hostRating);
    //               $('.hostReviewsCount').text(res.hostReviewsCount);
    //               $('.duration').text(res.duration);
    //               $('.reviewsHeading').text(`${res.hostName}'s reviews`);
    //               $('.reviewsContent').append(res.hostreviews);
    //               hide_loader();
    //               $('#md-profile-book').modal('show');

    //           },
    //           error: function(request, error) {
    //               hide_loader();
    //               alert(error);

    //           },
    //           complete: function() {
    //               hide_loader();
    //           }
    //       });

    //     }

    //     function show_loader() {
    //         $('#book-loader').removeClass('d-none');
    //      //    $('.property').removeClass('d-none');
    //     }

    //     function hide_loader() {
    //         $('#book-loader').addClass('d-none');
    //     }
    // }, 1000);









    // $(document).ready(function() {
    //     $('.daterangepicker').addClass('for-filter');
    //   });

    $('.date-modal').click(function () {
        // $('.daterangepicker').addClass('for-filter');
        $('.daterangepicker').css('display', 'block');
    });


    if ($('.accordion-collapse').hasClass("show")) {
        $("#coupon").prop("required", true);
    } else {
        $("#coupon").prop("required", false);
    }


    $(".mb-slid").slick({
        dots: 1,
        arrows: 1,
        autoplay: true,
        infinite: false,
        slidesToShow: 1,
        slidesToScroll: 1,
        pauseOnHover: 1,
        draggable: 0
    });
    var i = $(".pagingInfo");
    $(".mb-slid").on("init reInit afterChange", function (t, n, a, e) {
        i.text((a || 0) + 1 + "/" + n.slideCount);
    }),
        // $(document).mouseup(function (i) {
        //   let t = $(".dialog.show .popup"),
        //     n = $(".daterangepicker.show-calendar");
        //   t.is(i.target) || 0 != t.has(i.target).length || n.is(i.target) || 0 != n.has(i.target).length || ($(t).parent().removeClass("show"), $("body").removeClass("no-scroll"));
        // }),

        // $(document).ready(function() {
        //     $(".daterangepicker").addClass('for-filter');
        //     // Check if the current page is the home page
        //     if (window.location.pathname === '/') {
        //       // Add the class to the div on the home page
        //       $('daterangepicker').removeClass('for-filter');
        //     } else {
        //       // Remove the class from the div on other pages
        //       $('daterangepicker').addClass('for-filter');
        //     }
        //   });



        // $(".on-show").click(function () {
        //     $(".dialog").removeClass("show");
        //     // $(".daterangepicker").addClass('');
        //     let i = $(this).data("target");
        //     $(i).addClass("show"), $("body").addClass("no-scroll");
        //     $(".show").css({"opacity":"0" }).show().animate({opacity:1});
        //     // $(".blkd").css({"opacity":"0" }).show().animate({opacity:1});

        // }),


        $(".on-show").click(function () {
            //             var scrollDistance = -350; // Distance from the bottom you want to scroll

            //   // Calculate scroll position based on screen size
            //   var windowHeight = $(window).height();
            //   var documentHeight = $(document).height();
            //   var scrollPosition = documentHeight - windowHeight - scrollDistance;

            //   // Scroll to the calculated position
            //   $('html, body').animate({ scrollTop: scrollPosition }, 'fast');

            // $('html, body').animate({ scrollTop: 500 }, 'slow');
            // $(".daterangepicker").addClass('for-filter');
            // $(".dialog").removeClass("show");
            // let i = $(this).data("target");
            // $(i).addClass("show"), $("body").addClass("no-scroll");
            // $(".show").css({ "opacity": "0" }).show().animate({ opacity: 1 });


        }),
        $('.pac-target-input').keyup(function () {

            $('.popup1').removeClass('show');
        });
    // $(".search-reservation li").click(function () {
    //     $(".dialog");
    //     let i = $(this).data("target");
    //     $(i).fadeToggle(), $("body").addClass("no-scroll");
    // }),










    $(".menubtn").on("click", function (i) {
        i.stopPropagation(), $(".navmenu").toggleClass("opened");
    }),
        $(document).on("click", function () {
            $(".navmenu").removeClass("opened");
        }),
        $(document).ready(function () {
            $(".minus").click(function () {
                var i = $(this).parent().find("input"),
                    t = parseInt(i.val());
                (t -= 1) >= 0 && (i.val(t), i.change());
            }),
                $(".plus").click(function () {
                    var i = $(this).parent().find("input"),
                        t = $(this).data("limit"),
                        n = parseInt(i.val());
                    (n += 1) <= t && (i.val(n), $("#number_of_guests").val(""), $("#number_of_guests").val(n), i.change());
                });
        });
}),
    $(function () {
        $("[dir=ltr] .cat_shared_room").click(function () {
            $(".shared-room-info").animate({ right: "0" });
        }),
            $("[dir=ltr] .back_icon").click(function () {
                $(".shared-room-info").animate({ right: "-200%" });
            }),
            $("[dir=rtl] .cat_shared_room").click(function () {
                $(".shared-room-info").animate({ right: "0%" });
            }),
            $("[dir=rtl] .back_icon").click(function () {
                $(".shared-room-info").animate({ right: "200%" });
            });
    }),
    $(window).scroll(function () {
        $(window).scrollTop() >= 90 ? $(".nav-header").addClass("nav-scrolled") : $(".nav-header").removeClass("nav-scrolled");
    }),
    $(".open-album").click(function (i) {
        var t,
            n = $(this).data("open-id");
        n && ((t = $(".image-show[rel=" + n + "]:eq(0)")), i.preventDefault(), t.click());
    }),
    $(".loc").click(function () {
        $(".applyBtn").addClass("nxt-app");

    }),
    $(".dt-md").click(function () {
        $(".applyBtn").addClass("nxt-app");
    }),
    $(".loc").click(function () {
        $(".dt-md").trigger("click");
    }),
    $("body").delegate(".nxt-app", "click", function () {
        $(".input-field-gst").trigger("click");
    }),
    $(".pac-item").click(function () {
        $(".heade-date.date-modal").trigger("click");
    }),
    // $("#front-search-field").blur(function () {
    //     if ($(this).val() != "") {
    //         $(".dt-md").trigger("click");
    //     }
    // })
    // $("label.check-lb").on("click", function () {
    //   $(".heade-date input").trigger("click");
    // }),
    // $(function () {
    //   $("[dir=ltr] #view-cl").click(function () {
    //     $(".show-calendar").animate({ right: "0" });
    //   }),
    //     $("[dir=ltr] .back-bt").click(function () {
    //       $(".show-calendar").animate({ right: "-200%" });
    //     }),
    //     $("[dir=rtl] #view-cl").click(function () {
    //       $(".show-calendar").animate({ left: "0" });
    //     }),
    //     $("[dir=rtl] .back-bt").click(function () {
    //       $(".show-calendar").animate({ left: "-200%" });
    //     });
    // });

    // To restrict an input field to allow only numeric input
    $(document).ready(function () {
        $('.numberInput').on('keypress', function (event) {
            // Allow only numeric characters
            if (event.which < 48 || event.which > 57) {
                event.preventDefault();
            }
        });
    });



$('.pd-date').blur(function () {
    $('.applyBtn').removeClass("nxt-app")
});

$('.heade-date').blur(function () {
    $('.applyBtn').addClass("nxt-app")
});


// payment card year Option
var minOffset = 0, maxOffset = 10; // Change to whatever you want // minOffset = 0 for current year
var thisYear = (new Date()).getFullYear();
for (var i = minOffset; i <= maxOffset; i++) { var year = thisYear + i; $('<option>', { value: year.toString().substr(-2), text: year }).appendTo(".year"); }


// only enter alphabet validation
$(document).ready(function () {
    $("input.only-character-valid").on('input', function (event) {
        var regex = /^[a-zA-Z\s]+$/;
        var inputValue = $(this).val();
        if (!regex.test(inputValue)) {
            $(this).val(inputValue.replace(/[^a-zA-Z\s]/g, ''));
        }
    });
});
// only enter alphabet validation end



$(function () {
    $('input[name=promo_code]').on('keypress', function (e) {
        if (e.which == 32)
            return false;
    });
});

// on modal closed

// $(document).ready(function () {
//     $('.modal').on('hidden.bs.modal', function () {
//         $(this).find('input[type="text"], input[type="email"], textarea, input[type="checkbox"], input[type="radio"], input[type="file"], input[type="number"],input[type="url"],input[type="password"]').val('').prop('checked', false);
//         $(this).find('.is-invalid').removeClass('is-invalid');
//         $(this).find('.invalid-phone').removeClass('invalid-phone');
//         // $(this).find('.error').remove();
//     });
// });

$('#slider-ar').slick({
    dots: true,
    infinite: true,
    speed: 600,
    slidesToShow: 1, infinite: true,
    speed: 600,
    slidesToShow: 1,
    adaptiveHeight: true,
    fade: true,
    autoplay: true,
    autoplaySpeed: 6000,
    string: 'ease',
    rtl: true,

});
$('#slider-en').slick({
    dots: true,
    infinite: true,
    speed: 600,
    slidesToShow: 1, infinite: true,
    speed: 600,
    slidesToShow: 1,
    adaptiveHeight: true,
    fade: true,
    autoplay: true,
    autoplaySpeed: 6000,
    string: 'ease',
    // rtl:true,

});

$(document).ready(function () {
    var currentURL = window.location.pathname;
    var urlPart = currentURL.substr(currentURL.indexOf('/') + 1);

    if (urlPart) {
        $('body').addClass('sp-ontop');
        $('.nav-header').addClass('nav-inerpage');
    }
});





// for mobile ui



$(".fl-menu").click(function () {
    $(".float-menu").slideToggle();
    $("body").addClass("no-scroll");
    $('.slick-track').css('transform', 'translate3d(0, 0, 0)');


});
$(".cls-men").click(function () {
    $("body").removeClass("no-scroll");
});

$('.bx-ar').slick({
    dots: false,
    rtl: true,
    nav: false,
    infinite: false,
    speed: 300,
    slidesToShow: 2,
    centerMode: false,
    variableWidth: true,
    arrows: false,

});

$('.bx-en').slick({
    dots: false,
    nav: false,
    infinite: false,
    speed: 300,
    slidesToShow: 2,
    centerMode: false,
    variableWidth: true,
    arrows: false,
    // rtl: false
});

$('.ed-ar').slick({
    dots: false,
    rtl: true,
    nav: false,
    infinite: false,
    speed: 300,
    slidesToShow: 2,
    slidesToScroll: 2,
    centerMode: false,
    variableWidth: true,
    arrows: false,
    rtl: true
});

$('.ed-en').slick({
    dots: false,
    nav: false,
    infinite: false,
    speed: 300,
    slidesToShow: 2,
    centerMode: false,
    variableWidth: true,
    arrows: false,
});


$('.res-sl').slick({
    infinite: false,
    slidesToShow: 3,
    slidesToScroll: 3,
    dots: false,
    nav: false,
    arrows: false,

});

$(function () {
    $('.date-ui').daterangepickers({
        opens: 'center',
        minDate: moment().format('MM-DD-YYYY'),
    }, function (start, end, label) {
        console.log("A new date selection was made: " + start.format('DD-MM-YYYY') + ' to ' + end
            .format('DD-MM-YYYY'));
    });
});

$(".show-calendar-btn").click(function () {
    $(".show-calendar").animate({ right: "-0%" });
});

$(".cls-cld").click(function () {
    $(".show-calendar").animate({ right: "-110%" });
});


$(".rq-booking").click(function () {
    $('.rq-booking').addClass('d-none');
    $(".request-loader").removeClass('d-none');


});




// mobile host journey
$('.host-slide').slick({
    slidesToShow: 2,
    slidesToScroll: 1,
    dots: false,
    arrows: false,
    centerMode: false,
    focusOnSelect: true,
    loop: false,
    draggable: true,
    centerPadding: '10px'
});

$('.hs-line-tabs-slide').slick({
    dots: false,
    infinite: true,
    centerMode: false,
    slidesToShow: 3,
    focusOnSelect: true,
    slidesToScroll: 1,
    variableWidth: true,
    arrows: false,
});



// for web host managment journey

$(document).ready(function () {
    $('.hs-edit').click(function () {
        const $container = $(this).closest('.cont-host');
        $container.find('.hs-view').hide();
        $container.find('.hs-view-edit').show();
        $(this).hide();
    });

    $('.hs-save').click(function () {
        const $container = $(this).closest('.cont-host');
        $('.dotss', $container).removeClass('d-none');
        $('.hs-txt', $container).addClass('d-none');

        setTimeout(() => {
            //   $container.find('.hs-view').show();
            //   $container.find('.hs-view-edit').hide();
            //   $('.hs-edit').show();
            $('.hs-txt', $container).removeClass('d-none');
            $('.dotss', $container).addClass('d-none');
        }, 500);
    });

    $('.hs-cancel').click(function () {
        const $container = $(this).closest('.cont-host');
        $container.find('.hs-view').show();
        $container.find('.hs-view-edit').hide();
        $('.hs-edit').show();
    });
});

//  download add remove
$(".dap-closed").click(function () {
    $('.download-app-top').remove();
});

let currentLocaleValue = document.querySelector('html').lang;
let language = currentLocaleValue;
function getMonthAndDayNames(language) {
    const monthNames = {
        'ar': [
            "يناير",
            "فبراير",
            "مارس",
            "أبريل",
            "مايو",
            "يونيو",
            "يوليو",
            "أغسطس",
            "سبتمبر",
            "أكتوبر",
            "نوفمبر",
            "ديسمبر"
        ],
        'en': [
            "January",
            "February",
            "March",
            "April",
            "May",
            "June",
            "July",
            "August",
            "September",
            "October",
            "November",
            "December"
        ],
    };

    const daysOfWeek = {
        'ar': [
            "الأحد",
            "الاثنين",
            "الثلاثاء",
            "الأربعاء",
            "الخميس",
            "الجمعة",
            "السبت"
        ],
        'en': [
            "Su",
            "Mo",
            "Tu",
            "We",
            "Th",
            "Fr",
            "Sa"
        ],
    };

    const labels = {
        'ar': {
            applyLabel: 'اختار',
            cancelLabel: 'إلغاء'
        },
        'en': {
            applyLabel: 'Apply',
            cancelLabel: 'Cancel'
        }
    };

    return {
        monthNames: monthNames[language] || monthNames['ar'],
        daysOfWeek: daysOfWeek[language] || daysOfWeek['ar'],
        labels: labels[language] || labels['ar']
    };
};
let calendarContent = getMonthAndDayNames(language);
let calendarLabels = calendarContent.labels;


$('#blockUser').click(function () {
    $(".blockUser").prop("checked", false);
    $('#block_user').modal('show');
});
$('.yes-block').click(function () {
    $(".blockUser").prop("checked", true);
    $('#block_user').modal('hide');
});

$('#city_select').on('change', function () {
    var selectedCity = $(this).val();
    $('#front-search-field').val(selectedCity);
});

