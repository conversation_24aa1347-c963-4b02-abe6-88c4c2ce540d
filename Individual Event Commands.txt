# Individual Event Commands
php artisan moengage:generate-historical-events --event-type=booking.started --export-csv --expand-attributes --csv-only --timeout=0
php artisan moengage:generate-historical-events --event-type=booking.started --export-csv --expand-attributes --csv-only --timeout=0
php artisan moengage:generate-historical-events --event-type=booking.accepted --export-csv --expand-attributes --csv-only --timeout=0
php artisan moengage:generate-historical-events --event-type=booking.completed --export-csv --expand-attributes --csv-only --timeout=0
php artisan moengage:generate-historical-events --event-type=booking.cancelled_by_guest --export-csv --expand-attributes --csv-only --timeout=0
php artisan moengage:generate-historical-events --event-type=start.booking --export-csv --expand-attributes --csv-only --timeout=0
php artisan moengage:generate-historical-events --event-type=payment.initiated --export-csv --expand-attributes --csv-only --timeout=0
php artisan moengage:generate-historical-events --event-type=host.property_listing_started --export-csv --expand-attributes --csv-only --timeout=0
php artisan moengage:generate-historical-events --event-type=host.property_listing_completed --export-csv --expand-attributes --csv-only --timeout=0

php artisan moengage:generate-historical-events --event-type=host.property_approved --export-csv --expand-attributes --csv-only --timeout=0
php artisan moengage:generate-historical-events --event-type=guest.registered --export-csv --expand-attributes --csv-only --timeout=0
php artisan moengage:generate-historical-events --event-type=review.published --export-csv --expand-attributes --csv-only --timeout=0
[No Event found!]php artisan moengage:generate-historical-events --event-type=wishlist.add --export-csv --expand-attributes --csv-only --timeout=0
php artisan moengage:generate-historical-events --event-type=guest.property_viewed --export-csv --expand-attributes --csv-only --timeout=0
[No Event found!]php artisan moengage:generate-historical-events --event-type=guest.referral_success --export-csv --expand-attributes --csv-only --timeout=0

# Category Event Commands
php artisan moengage:generate-historical-events --event-type=booking --export-csv --expand-attributes
php artisan moengage:generate-historical-events --event-type=start_booking --export-csv --expand-attributes
php artisan moengage:generate-historical-events --event-type=payment --export-csv --expand-attributes
php artisan moengage:generate-historical-events --event-type=property --export-csv --expand-attributes
php artisan moengage:generate-historical-events --event-type=user --export-csv --expand-attributes
php artisan moengage:generate-historical-events --event-type=review --export-csv --expand-attributes
php artisan moengage:generate-historical-events --event-type=wishlist --export-csv --expand-attributes
php artisan moengage:generate-historical-events --event-type=property_view --export-csv --expand-attributes
php artisan moengage:generate-historical-events --event-type=referral --export-csv --expand-attributes

# With Date Filtering
php artisan moengage:generate-historical-events --event-type=booking.started --start-date=2024-01-01 --end-date=2024-12-31 --export-csv --expand-attributes
php artisan moengage:generate-historical-events --event-type=guest.registered --start-date=2024-01-01 --export-csv --expand-attributes
php artisan moengage:generate-historical-events --event-type=booking --start-date=2024-01-01 --end-date=2024-03-31 --export-csv --expand-attributes

# With Custom Chunk Size
php artisan moengage:generate-historical-events --event-type=booking.started --chunk-size=500 --export-csv --expand-attributes
php artisan moengage:generate-historical-events --event-type=guest.property_viewed --chunk-size=2000 --export-csv --expand-attributes

# Generate All Events
php artisan moengage:generate-historical-events --export-csv --expand-attributes
php artisan moengage:generate-historical-events --start-date=2024-01-01 --end-date=2024-12-31 --export-csv --expand-attributes

--csv-only



# Optimized for 500k+ records
   php artisan moengage:generate-historical-events --event-type=guest.property_viewed --chunk-size=50 --max-retries=5 --force-gc-frequency=5 --memory-threshold=75

# Resume from failure
php artisan moengage:generate-historical-events \
    --event-type=guest.property_viewed \
    --resume-from-id=250000 \
    --chunk-size=50