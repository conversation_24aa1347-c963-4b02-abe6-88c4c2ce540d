@extends('admin.template')

@section('main')
    <link rel="stylesheet" href="{{ asset('admin/css/bootstrap-select.min.css') }}">

    <!-- Content Wrapper. Contains page content -->
    <div class="content-wrapper">
        <!-- Content Header (Page header) -->
        <section class="content-header">
            <h1>
                Properties
                <small>Control panel</small>
            </h1>
            @include('admin.common.breadcrumb')
        </section>

        <!-- Main content -->
        <section class="content">
            <!--Filtering Box Start -->
            <div class="row">
                <div class="col-xs-12">
                    <div class="box">
                        <div class="box-body">
                            {{-- @php
                                Request::segment(2) == 'approval-changes-list' : "url('admin/approval-changes-list')" ? "url('admin/properties')";
                            @endphp --}}
                            {{-- <form class="form-horizontal" enctype='multipart/form-data' action="{{ url('admin/properties') }}"
                                method="GET" accept-charset="UTF-8"> --}}
                            <form class="form-horizontal" enctype='multipart/form-data'
                                action="{{ Request::segment(2) == 'approval-changes-list' ? url('admin/approval-changes-list') : url('admin/properties') }}"
                                method="GET">
                                {{ csrf_field() }}
                                <div class="co;-md-12  d-none">
                                    <input class="form-control" type="text" id="startDate" name="from"
                                        value="<?= isset($from) ? $from : '' ?>" hidden>
                                    <input class="form-control" type="text" id="endDate" name="to"
                                        value="<?= isset($to) ? $to : '' ?>" hidden>
                                </div>

                                <div class="col-md-12">
                                    <div class="">
                                        <div class="row">
                                            <div class="col-md-3 col-sm-3 col-xs-12">
                                                <label>Date Range</label>
                                                <div class="input-group col-xs-12">
                                                    <button type="button" class="form-control" id="daterange-btn">
                                                        <span class="pull-left">
                                                            <i class="fa fa-calendar"></i> Pick a date range
                                                        </span>
                                                        <i class="fa fa-caret-down pull-right"></i>
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="col-md-3 col-sm-3 col-xs-12">
                                                <label>Status</label>
                                                <select class="form-control" name="status" id="status">
                                                    <option value="">All</option>
                                                    <option value="Listed"
                                                        {{ $allstatus == 'Listed' ? ' selected="selected"' : '' }}>Listed
                                                    </option>
                                                    <option value="Unlisted"
                                                        {{ $allstatus == 'Unlisted' ? ' selected="selected"' : '' }}>
                                                        Unlisted
                                                    </option>
                                                    <option value="In Progress"
                                                        {{ $allstatus == 'In Progress' ? ' selected="selected"' : '' }}>
                                                        In Progress
                                                    </option>
                                                </select>
                                            </div>
                                            <div class="col-md-3 col-sm-3 col-xs-12">
                                                <label>Property Type</label>
                                                <select class="form-control" name="property_type" id="property_type">
                                                    <option value="">All</option>
                                                    @if ($property_type_all)
                                                        @foreach ($property_type_all as $data)
                                                            <option value="{{ $data->id }}"
                                                                {{ $data->id == $allPropertyType ? 'selected' : '' }}>
                                                                {{ $data->name }}
                                                            </option>
                                                        @endforeach
                                                    @endif
                                                </select>
                                            </div>
                                            <div class="col-md-3 col-sm-3 col-xs-12">
                                                <label>Space Type</label>
                                                <select class="form-control" name="space_type" id="space_type">
                                                    <option value="">All</option>
                                                    @if ($space_type_all)
                                                        @foreach ($space_type_all as $data)
                                                            <option value="{{ $data->id }}"
                                                                {{ $data->id == $allSpaceType ? 'selected' : '' }}>
                                                                {{ $data->name }}
                                                            </option>
                                                        @endforeach
                                                    @endif
                                                </select>
                                            </div>
                                            {{-- <div class="col-md-3 col-sm-3 col-xs-12">
                                                <label>Property Name</label>
                                                <select class="form-control" name="property_name" id="property_name">
                                                    <option value="">All</option>
                                                    @if ($property_name_all)
                                                        @foreach ($property_name_all as $data)
                                                            <option value="{{ $data->name }}"
                                                                {{ $data->name == $PropertyName ? 'selected' : '' }}>
                                                                {{ $data->name }}
                                                            </option>
                                                        @endforeach
                                                    @endif
                                                </select>
                                            </div> --}}
                                            <div class="col-md-3 col-sm-3 col-xs-12">
                                                <label>Ranking</label>
                                                <select class="form-control" name="ranking" id="ranking">
                                                    <option value="">All</option>
                                                    <option value="most_rated"
                                                        {{ $Ranking == 'most_rated' ? 'selected' : '' }}>Most Rated
                                                    </option>
                                                    <option value="low_price"
                                                        {{ $Ranking == 'low_price' ? 'selected' : '' }}>Lowest Price
                                                    </option>
                                                    <option value="high_price"
                                                        {{ $Ranking == 'high_price' ? 'selected' : '' }}>Highest Price
                                                    </option>
                                                    <option value="most_viewed"
                                                        {{ $Ranking == 'most_viewed' ? 'selected' : '' }}>Most Viewed
                                                    </option>
                                                </select>
                                            </div>
                                            <div class="col-md-3 col-sm-3 col-xs-12">
                                                <label>Capacity</label>
                                                <select class="form-control" name="capacity" id="capacity">
                                                    <option value="">All</option>
                                                    <option value="2-5" {{ $Capacity == '2-5' ? 'selected' : '' }}>2-5
                                                    </option>
                                                    <option value="5-10" {{ $Capacity == '5-10' ? 'selected' : '' }}>5-10
                                                    </option>
                                                    <option value="10-15" {{ $Capacity == '10-15' ? 'selected' : '' }}>
                                                        10-15</option>
                                                    <option value="15-20" {{ $Capacity == '15-20' ? 'selected' : '' }}>
                                                        15-20</option>
                                                    <option value="20-25" {{ $Capacity == '20-25' ? 'selected' : '' }}>
                                                        20-25</option>
                                                    <option value="30+" {{ $Capacity == '30+' ? 'selected' : '' }}>30+
                                                    </option>
                                                </select>
                                            </div>
                                            <div class="col-md-3 col-sm-3 col-xs-12">
                                                <label>Amenities</label>

                                                <div class="multiple-main">
                                                    <select
                                                        class="selectpicker admn-custm-select amenities-multiple-select pt-multiple-select"
                                                        multiple id="amenities" data-actions-box="true" name="amenities[]"
                                                        data-size="8">
                                                        <option value="">All</option>
                                                        @if ($amenities_all)
                                                            @foreach ($amenities_all as $data)
                                                                <option value="{{ $data->id }}"
                                                                    {{ $Amenities && in_array($data->id, $Amenities) ? 'selected' : '' }}>
                                                                    {{ $data->title }}
                                                                </option>
                                                            @endforeach
                                                        @endif
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-3 col-sm-3 col-xs-12">
                                                <label>District</label>

                                                <div class="input-group multiple-main col-xs-12 d-flex">
                                                    <select
                                                        class="selectpicker admn-custm-select districts-multiple-select pt-multiple-select"
                                                        multiple id="districts" data-actions-box="true" name="districts[]"
                                                        data-size="8">
                                                        {{-- <option value="">All</option> --}}
                                                        @if ($districts)
                                                            @foreach ($districts as $data)
                                                                <option value="{{ $data->district }}"
                                                                    {{ in_array($data->district, $set_district) ? 'selected' : '' }}>
                                                                    {{ $data->district }}
                                                                </option>
                                                            @endforeach
                                                        @endif
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-3 col-sm-3 col-xs-12">
                                                <label>City</label>

                                                <div class="multiple-main">
                                                    <select
                                                        class="selectpicker admn-custm-select cities-multiple-select pt-multiple-select"
                                                        multiple id="cities" data-actions-box="true" name="cities[]"
                                                        data-size="8">
                                                        {{-- <option value="">All</option> --}}
                                                        @if ($cities)
                                                            @foreach ($cities as $data)
                                                                <option value="{{ $data->city }}"
                                                                    {{ in_array($data->city, $set_cities) ? 'selected' : '' }}>
                                                                    {{ $data->city }}
                                                                </option>
                                                            @endforeach
                                                        @endif
                                                    </select>
                                                </div>
                                            </div>
                                            @php
                                                $property_link = isset($_GET['property_link'])
                                                    ? (int) $_GET['property_link']
                                                    : null;
                                            @endphp
                                            <div class="col-md-3 col-sm-3 col-xs-12">
                                                <label>Links</label>
                                                <select class="form-control" name="property_link" id="property_link">
                                                    <option value="" selected>0</option>
                                                    <option value="1" {{ $property_link == 1 ? 'selected' : '' }}>1
                                                    </option>
                                                    <option value="2" {{ $property_link == 2 ? 'selected' : '' }}>2
                                                    </option>
                                                    <option value="3" {{ $property_link == 3 ? 'selected' : '' }}>3
                                                    </option>
                                                    <option value="4" {{ $property_link == 4 ? 'selected' : '' }}>4
                                                    </option>
                                                    <option value="5" {{ $property_link == 5 ? 'selected' : '' }}>5
                                                    </option>
                                                    <option value="6" {{ $property_link >= 6 ? 'selected' : '' }}>5+
                                                    </option>
                                                </select>
                                            </div>
                                            <div class="col-md-3 col-sm-3 col-xs-12">
                                                <label>Unit Code</label>
                                                <input class="form-control" type="text"
                                                    @if ($property_code != null) value="{{ $property_code }}" @endif
                                                    name="property_code" maxlength="7"
                                                    onKeyDown="if(this.value.length==7 && event.keyCode!=8) return false;">
                                            </div>
                                            <div class="col-md-3 col-sm-3 col-xs-12">
                                                <label>Phone Number</label>
                                                <input class="form-control" type="text"
                                                    @if ($phone_number != null) value="{{ $phone_number }}" @endif
                                                    name="phone_number" maxlength="13"
                                                    onKeyDown="if(this.value.length==13 && event.keyCode!=8) return false;">
                                            </div>


                                            {{-- <div class="col-md-3 col-sm-3 col-xs-12">
                                                <label>Priority</label>

                                                <div class="input-group multiple-main col-xs-12 d-flex">
                                                    <select class="selectpicker admn-custm-select" id="priority"
                                                        data-actions-box="true" name="priority">
                                                        <option value="">Select</option>
                                                        <option value="asc"
                                                            @if ($priority == 'asc') selected @endif>Ascending
                                                        </option>
                                                        <option value="desc"
                                                            @if ($priority == 'desc') selected @endif>Descending
                                                        </option>
                                                    </select>
                                                </div>
                                            </div> --}}

                                            <div class="col-md-3 col-sm-3 col-xs-12">
                                                <label>Booking Type</label>

                                                <div class="input-group multiple-main col-xs-12 d-flex">
                                                    <select class="selectpicker admn-custm-select" id="booking_type"
                                                        data-actions-box="true" name="booking_type">
                                                        <option value="">Select</option>
                                                        <option value="instant"
                                                            @if ($booking_type == 'instant') selected @endif>Instant
                                                        </option>
                                                        <option value="request"
                                                            @if ($booking_type == 'request') selected @endif>Request
                                                        </option>
                                                    </select>
                                                </div>
                                            </div>

                                            <div class="col-md-3 col-sm-3 col-xs-12">
                                                <label>Platform</label>
                                                <select class="form-control" name="platform" id="platform">
                                                    <option value="">All</option>
                                                    @if ($platforms)
                                                        @foreach ($platforms as $data)
                                                            <option value="{{ $data->id }}"
                                                                {{ $data->id == $platform ? 'selected' : '' }}>
                                                                {{ $data->name }}
                                                            </option>
                                                        @endforeach
                                                    @endif
                                                </select>
                                            </div>

                                            <div class="col-md-3 col-sm-3 col-xs-12">
                                                <label>License</label>
                                                <select class="form-control" name="license" id="license">
                                                    <option value="">All</option>
                                                    <option value="verified"
                                                        @if ($license == 'verified') selected @endif>Verified
                                                    </option>
                                                    <option value="unverified"
                                                        @if ($license == 'unverified') selected @endif>Unverified
                                                    </option>
                                                </select>
                                            </div>


                                            <div class="col-md-3 col-sm-3 col-xs-12">
                                                <br>
                                                <label>
                                                    <input class="form-check-input" type="checkbox"
                                                        name="approval_pending" value="1"
                                                        {{ $approval_pending !== null ? 'checked' : '' }}>
                                                    Properties Pending for approval
                                                </label>
                                            </div>


                                            <div class="col-md-1 col-sm-2 col-xs-4 ">
                                                <br>
                                                <button type="submit" name="btn"
                                                    class="btn btn-primary btn-flat">Filter</button>
                                            </div>
                                            <div class="col-md-1 col-sm-2 col-xs-4 ">
                                                <br>
                                                <button type="submit" name="reset_btn"
                                                    class="btn btn-primary btn-flat">Reset</button>
                                            </div>

                                        </div>
                                    </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            <!--Filtering Box End -->
            <div class="row">
                <div class="col-xs-12">
                    <div class="box">
                        <div class="box-header">
                            <h3 class="box-title">
                                {{ Request::segment(2) == 'approval-changes-list' ? 'Approval Required' : 'Properties Management' }}
                            </h3>
                            @if (Helpers::has_permission(Auth::guard('admin')->user()->id, 'add_properties'))
                                <div class="pull-right">
                                    <a href="{{ url('admin/add-properties') }}" class="btn btn-success">Add
                                        Properties</a>
                                </div>
                            @endif
                        </div>
                        <!-- /.box-header -->
                        <div class="box-body">
                            <div class="table-responsive">
                                {!! $dataTable->table([
                                    'class' => 'table table-striped table-hover dt-responsive',
                                    'width' => '100%',
                                    'cellspacing' => '0',
                                ]) !!}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
@endsection

@push('scripts')
    <!-- Block User Confirmation Modal -->
    <div class="modal fade z-index-medium" id="statusReasonModal" role="dialog">
        <div class="modal-dialog">
            <!-- Modal content-->
            <form id="statusReasonForm" method="POST" class="modal-content w-100-p">
                @csrf
                <input type="hidden" name="property_id" />
                <input type="hidden" name="visibility" />

                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">Reason</h4>
                </div>

                <div class="modal-body">
                    <input type="text" name="reason" maxlength="500" class="form-control" required name="reason"
                        placeholder="Enter Reason">
                    <p class="m-0 mt-1 text-danger" id="reason_error"></p>
                </div>

                <div class="modal-footer">
                    <buton type="submit" class="btn btn-danger" id="statusReasonModalSubmitBtn">Submit</buton>
                    <button type="reset" class="btn btn-default" data-dismiss="modal">No</button>
                </div>
            </form>
        </div>
    </div>

    <script src="{{ asset('DataTables-1.10.18/js/jquery.dataTables.min.js') }}"></script>
    <script src="{{ asset('Responsive-2.2.2/js/dataTables.responsive.min.js') }}"></script>
    <script type="text/javascript" src="{{ asset('admin/js/bootstrap-select.min.js') }}"></script>
    <script>
        function copyToClipboard(element) {
            let textToCopy = $(element).prev().text();
            // Select the text to be copied
            // var textToCopy = $("#textToCopy").text();

            // Create a temporary textarea to hold the text
            var tempTextarea = $("<textarea>");
            tempTextarea.css({
                position: "absolute",
                left: "-9999px"
            });
            tempTextarea.val(textToCopy);
            $("body").append(tempTextarea);

            // Select the text inside the temporary textarea
            tempTextarea.select();

            try {
                // Execute the copy command
                document.execCommand("copy");
                alert("Text copied to clipboard!");
            } catch (err) {
                // If the copy command fails, handle the error
                alert("Oops, unable to copy!");
            }

            // Remove the temporary textarea from the DOM
            tempTextarea.remove();
        }

        function openReasonModal(property_id, visibility) {
            $("#statusReasonModal").find('input[name="property_id"]').val(property_id);
            $("#statusReasonModal").find('input[name="visibility"]').val(visibility);

            $("#statusReasonModal").modal('show');
        }

        $("#statusReasonModalSubmitBtn").click(function(e) {
            e.preventDefault();
            let submitBtn = $(this).find('button[type="submit"]');

            $.ajax({
                type: "POST",
                url: `{{ route('admin.property.unlist') }}`,
                data: $("#statusReasonForm").serialize(),
                dataType: "json",
                beforeSend: function() {
                    submitBtn.prop('disabled', true);
                    $("#reason_error").text("");
                },
                success: function(response) {
                    if (response.status == 200) {
                        $("#statusReasonModal").modal('hide');
                        $("#statusReasonModal").find('input[name="property_id"]').val("");
                        $("#statusReasonModal").find('input[name="visibility"]').val("");
                        $("#statusReasonModal").find('input[name="reason"]').val("");

                        location.reload();
                    } else {
                        $("#reason_error").text(xhr.responseJSON.error);
                    }

                },
                complete: function() {
                    submitBtn.prop('disabled', false);
                }
            });
        });
    </script>
    {!! $dataTable->scripts() !!}
@endpush

<style>
    .unlisted-row {
        background-color: #9ce9cf !important;
    }
</style>

@section('validate_script')
    <script>
        $(document).ready(function() {
            getSpaceTypes();
            $('#property_type').on('change', function() {
                getSpaceTypes()
            });

            $(document).on('change', '.status-dropdown', function () {
            const dropdown = $(this);
            const propertyId = dropdown.data('id');
            const newStatus = dropdown.val();
            const newStatusText = dropdown.text();


            // Get the previous value to revert if cancelled
            const previousStatus = dropdown.find('option').filter(':selected').val();

            Swal.fire({
                title: 'Are you sure?',
                text: `Change booking badge to "${newStatusText}"?`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, change it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    $.ajax({
                        url: "{{ route('admin.property.badge.update') }}",
                        method: 'POST',
                        data: {
                            _token: $('meta[name="csrf-token"]').attr('content'),
                            property_id: propertyId,
                            badge: newStatus
                        },
                        success: function (response) {
                            if(response.status == "error"){
                                Swal.fire('Error', 'Failed to update badge', 'error');
                                // Optionally revert dropdown to previous value
                                dropdown.val(previousStatus);
                            } else {
                                Swal.fire('Updated!', 'Badge has been changed.', 'success');
                            }
                        },
                        error: function () {
                            Swal.fire('Error', 'Failed to update badge', 'error');
                            // Optionally revert dropdown to previous value
                            dropdown.val(previousStatus);
                        }
                    });
                } else {
                    // Revert to previous status if user cancels
                    dropdown.val(previousStatus);
                }
            });
        });
        });

        let allSpaceType = "{{ $allSpaceType }}";

        function getSpaceTypes() {
            $('#space_type').prop('disabled', true);
            var property_type = $('#property_type').val();
            $.ajax({
                url: "{{ route('get.space.types.by.property.type', ['property_type_id' => ':id']) }}".replace(
                    ':id', property_type),
                method: "GET",
                success: function(result) {
                    if (result.success == "true") {
                        let spaceTypes = result.spaceTypes;
                        if (spaceTypes.length > 0) {
                            let spaceType = $("#space_type");
                            let options = '<option value="">Select</option>';

                            $.each(spaceTypes, function(index, value) {
                                options += `<option value="${value.id}"`;
                                if (value.id == allSpaceType != "" ?? 0) options += ` selected`;
                                options += `>${value.name}</option>`;
                            });

                            spaceType.html(options);
                        }
                    }
                    $('#space_type').prop('disabled', false);

                }
            })
        }

        // Date Time range picker for filter
        $(function() {
            var startDate = $('#startDate').val();
            var endDate = $('#endDate').val();
            dateRangeBtn(startDate, endDate, dt = 1);
            formDate(startDate, endDate);
            // <li><a href="" title="PDF" id="pdf">PDF</a></li>
            $(document).ready(function() {
                // <li><a href="" title="PDF" id="pdf">PDF</a></li>
                $('#dataTableBuilder_length').after(
                    '<div id="exportArea" class="col-md-2 col-sm-2 "><div class="row mt-m-2"><div class="btn-group btn-margin col-6"><button type="button" class="form-control dropdown-toggle w-80" data-toggle="dropdown" aria-haspopup="true">Export</button><ul class="dropdown-menu d-menu-min-w"><li><a href="" title="CSV" id="csv">CSV</a></li></ul></div><div class="btn btn-group btn-refresh col-6"><a href="" id="tablereload" class="form-control"><span><i class="fa fa-refresh"></i></span></a></div></div></div>'
                );
            });
            $('.amenities-multiple-select').selectpicker();
            //csv convert
            $(document).on("click", "#csv", function(event) {
                event.preventDefault();
                var space_type = $('#space_type').val();
                var property_name = $('#property_name').val();
                var property_type = $('#property_type').val();
                var ranking = $('#ranking').val();
                var capacity = $('#capacity').val();
                var status = $('#status').val();
                var to = $('#endDate').val();
                var from = $('#startDate').val();
                window.location = "properties/property_list_csv?to=" + to + "&from=" + from +
                    "&space_type=" + space_type + "&status=" + status + "&property_name=" + property_name +
                    "&ranking" + ranking + "&property_type=" + property_type + "&capacity" + capacity;
            });
            //pdf convert
            $(document).on("click", "#pdf", function(event) {
                event.preventDefault();
                var space_type = $('#space_type').val();
                var property_name = $('#property_name').val();
                var property_type = $('#property_type').val();
                var ranking = $('#ranking').val();
                var capacity = $('#capacity').val();
                var status = $('#status').val();
                var to = $('#endDate').val();
                var from = $('#startDate').val();
                window.location = "properties/property_list_pdf?to=" + to + "&from=" + from +
                    "&space_type=" + space_type + "&status=" + status + "&property_name=" + property_name +
                    "&ranking" + ranking + "&property_type" + property_type + "&capacity" + capacity;
            });
            //reload Datatable
            $(document).on("click", "#tablereload", function(event) {
                event.preventDefault();
                $("#dataTableBuilder").DataTable().ajax.reload();
            });
        });

        webengage.user.logout();
    </script>
@endsection
