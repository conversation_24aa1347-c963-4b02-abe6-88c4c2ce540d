@extends('admin.template')

@section('main')
    <div class="content-wrapper">
        <section class="content">
            <div class="row">
                <div class="col-md-8 col-sm-offset-2">
                    <div class="box box-info box_info">
                        <div class="box-header with-border">
                            <h3 class="box-title">Booking Details</h3>
                        </div>

                        <form action="{{ url('admin/bookings/detail/' . $result->id) }}" method="post"
                              class='form-horizontal'>
                            {{ csrf_field() }}
                            <div class="box-body">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">
                                        Property name
                                    </label>
                                    <div class="col-sm-6 col-sm-offset-1">
                                        {{ $result->properties->name }}
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-sm-3 control-label">
                                        Host name
                                    </label>
                                    <div class="col-sm-6 col-sm-offset-1">
                                        {{ ucfirst($result->properties->users->first_name) }}
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-sm-3 control-label">
                                        Guest name
                                    </label>
                                    <div class="col-sm-6 col-sm-offset-1">
                                        {{ ucfirst($result->users->first_name) }}
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-sm-3 control-label">
                                        Checkin
                                    </label>
                                    <div class="col-sm-6 col-sm-offset-1" id="">
                                        {{ onlyFormat($result->start_date) }}
                                    </div>
                                    <input type="hidden" name="checkin" id="prev-checkin"
                                           value="{{ $result->start_date }}">
                                </div>

                                <div class="form-group">
                                    <label class="col-sm-3 control-label">
                                        Checkout
                                    </label>
                                    <div class="col-sm-6 col-sm-offset-1" id="">
                                        {{ onlyFormat($result->end_date) }}
                                    </div>
                                    <input type="hidden" id="prev-checkout" value="{{ $result->end_date }}">
                                    <input type="hidden" id="booking_id" value="{{ $result->id }}">

                                </div>

                                <div class="form-group d-none" id="extended_checkin">
                                    <label class="col-sm-3 control-label">
                                        Extended Checkin
                                    </label>
                                    <div class="col-sm-6 col-sm-offset-1" id="check_in">
                                        {{ onlyFormat($result->start_date) }}
                                    </div>
                                </div>
                                <div class="form-group d-none" id="extended_checkout">
                                    <label class="col-sm-3 control-label">
                                        Extended Checkout
                                    </label>
                                    <div class="col-sm-6 col-sm-offset-1" id="check_out">
                                        {{ onlyFormat($result->end_date) }}
                                    </div>
                                </div>
                                @php

                                    $checkin = Carbon\Carbon::parse($result->start_date)->format('m/d/Y');
                                    $checkout = Carbon\Carbon::parse($result->end_date)->format('m/d/Y');

                                    // $checkout = adDaysinDate($checkin, $checkout, 1); //if checkin and checkout same then add days(3rd parameter) in checkout

                                @endphp
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">
                                        Extended Dates
                                    </label>
                                    <div class="col-sm-4 col-sm-offset-1">
                                        <input type="text" name="daterange" placeholder="Date"
                                               class="date-modal form-control text-center"
                                               value="{{ $checkin . '-' . $checkout }}" />
                                    </div>

                                    <div class="col-sm-4 ">
                                        <div class="d-none" id="confirmationBox">
                                            <!-- The button used to copy the text -->
                                            <label class="control-label pd-0">
                                                <button type="button" class="btn-success btn" id="confirmationBtn">
                                                    Booking Confirmation
                                                </button>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-sm-4 ">
                                        <div class="d-none" id="saveExtendedBooking">
                                            <!-- The button used to copy the text -->
                                            <label class="control-label pd-0">
                                                <button type="button" class="btn-success btn" id="extendedBookingSaveBtn">
                                                    Save
                                                </button>
                                            </label>
                                        </div>
                                    </div>
                                    <input type="hidden" name="url_checkin" id="url_checkin" value="{{ $checkin }}">
                                    <input type="hidden" name="url_checkout" id="url_checkout"
                                           value="{{ $checkout }}">
                                    <span class="text-danger d-none" id="price_error"></span>

                                </div>


                                <div class="form-group">
                                    <div class="d-none" id="paymentUrlDiv">
                                        <!-- The button used to copy the text -->
                                        <label class="col-sm-3 control-label">
                                            <button onclick="copyFunction(event)">Copy text</button>
                                        </label>
                                        <div class="col-sm-3 col-sm-offset-1">
                                            <input class="form-control" type="text" value="" id="payment_url">
                                        </div>
                                        <label class="col-sm-3 control-label">
                                            <button onclick="updateDetails(event,'{{ $result->id }}')">Update Details</button>
                                        </label>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">
                                        Number of guests
                                    </label>
                                    <div class="col-sm-6 col-sm-offset-1">
                                        {{ $result->guest }}
                                    </div>
                                </div>


                                <div class="form-group">
                                    <label class="col-sm-3 control-label">
                                        Total nights
                                    </label>
                                    <div class="col-sm-6 col-sm-offset-1" id="">
                                        {{ $result->total_night }}
                                    </div>
                                </div>
                                <div class="form-group d-none" id="extended_nights">
                                    <label class="col-sm-3 control-label">
                                        Extended Total nights
                                    </label>
                                    <div class="col-sm-6 col-sm-offset-1" id="total_night">
                                        {{ $result->total_night }}
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-sm-3 control-label">
                                        Per nights fee
                                    </label>
                                    <div class="col-sm-6 col-sm-offset-1">
                                        {!! moneyFormat($result->currency->org_symbol, $result->original_per_night) !!}
                                    </div>
                                </div>

                                @if (isset($date_price))
                                    @foreach ($date_price as $datePrice)
                                        <div class="form-group">
                                            <label class="col-sm-3 control-label">
                                                {{ $datePrice->date }}
                                            </label>
                                            <div class="col-sm-6 col-sm-offset-1">
                                                {!! moneyFormat($result->currency->org_symbol, $datePrice->price) !!}
                                            </div>
                                        </div>
                                    @endforeach
                                @endif


                                <div class="form-group">
                                    <label class="col-sm-3 control-label">
                                        Sub Total amount
                                    </label>
                                    <div class="col-sm-6 col-sm-offset-1" id="">
                                        {!! moneyFormat($result->currency->org_symbol, $result->original_base_price) !!}
                                    </div>
                                </div>
                                <div class="form-group d-none" id="extended_subtotal">
                                    <label class="col-sm-3 control-label">
                                        Extended Sub Total amount
                                    </label>
                                    <div class="col-sm-6 col-sm-offset-1" id="sub_total_amount">
                                        {!! moneyFormat($result->currency->org_symbol, $result->original_base_price) !!}
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-sm-3 control-label">
                                        Cleaning fee
                                    </label>
                                    <div class="col-sm-6 col-sm-offset-1">
                                        {!! moneyFormat($result->currency->org_symbol, $result->original_cleaning_charge) !!}
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-sm-3 control-label">
                                        I.V.A Tax
                                    </label>
                                    <div class="col-sm-6 col-sm-offset-1">
                                        {!! moneyFormat($result->currency->org_symbol, $result->iva_tax) !!}

                                    </div>
                                </div>
                                <div class="form-group d-none" id="extended_iva_tax">
                                    <label class="col-sm-3 control-label">
                                        Extended I.V.A Tax
                                    </label>
                                    <div class="col-sm-6 col-sm-offset-1" id="iva_tax">


                                    </div>

                                </div>


                                <div class="col-sm-6 col-sm-offset-1 d-none" id="property_price">


                                </div>


                                <div class="form-group">
                                    <label class="col-sm-3 control-label">
                                        Accomodation Tax
                                    </label>
                                    <div class="col-sm-6 col-sm-offset-1">
                                        {!! moneyFormat($result->currency->org_symbol, $result->accomodation_tax) !!}

                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-sm-3 control-label">
                                        Additional guest fee
                                    </label>
                                    <div class="col-sm-6 col-sm-offset-1">
                                        {!! moneyFormat($result->currency->org_symbol, $result->original_guest_charge) !!}
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-sm-3 control-label">
                                        Security fee
                                    </label>
                                    <div class="col-sm-6 col-sm-offset-1">
                                        {!! moneyFormat($result->currency->org_symbol, $result->original_security_money) !!}
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-sm-3 control-label">
                                        Service fee
                                    </label>
                                    <div class="col-sm-6 col-sm-offset-1">
                                        {!! moneyFormat($result->currency->org_symbol, $result->original_service_charge) !!}
                                    </div>
                                </div>
                                <div class="form-group d-none" id="extended_servicefee">
                                    <label class="col-sm-3 control-label">
                                        Extended Service fee
                                    </label>
                                    <div class="col-sm-6 col-sm-offset-1" id="service_fee">

                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-sm-3 control-label">
                                        Host fee
                                    </label>
                                    <div class="col-sm-6 col-sm-offset-1">
                                        {!! moneyFormat($result->currency->org_symbol, $result?->paymentDetails?->host_pay??0) !!}
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-sm-3 control-label">
                                        Total amount
                                    </label>
                                    <div class="col-sm-6 col-sm-offset-1" id="original_total">
                                        {!! moneyFormat($result->currency->org_symbol, $result->original_total) !!}
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-3 control-label" >
                                        Darent Discount
                                    </label>
                                    <div class="col-sm-6 col-sm-offset-1" id="darent_discount">
                                        {!! moneyFormat($result->currency->org_symbol, $result->promoCodeUsage?->discount_value ?? 0) !!}
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">
                                        Guest Paid
                                    </label>
                                    <div class="col-sm-6 col-sm-offset-1">
                                        <span style="color: red; font-weight: bold; font-size: 18px">
                                            {!! moneyFormat($result->currency->org_symbol, $result?->paymentDetails?->guest_paid??0) !!}
                                        </span>

                                    </div>
                                </div>

                                <div class="form-group d-none" id="extended_total">
                                    <label class="col-sm-3 control-label">
                                        Extended Total amount
                                    </label>
                                    <div class="col-sm-6 col-sm-offset-1" id="total_amount">
                                        {!! moneyFormat($result->currency->org_symbol, $result->original_total) !!}
                                    </div>
                                </div>
                               
                                    @if(!empty($result->extended) && ( ($result->use_forced_total ? $result->forced_total: $result->total) != $result->extended?->amount_due))
                                        <div class="form-group " id="forced_total_div">
                                            <label class="col-sm-3 control-label">
                                                Update Total amount
                                            </label>
                                            <div class="col-sm-6 col-sm-offset-1" id="forced_total">

                                                <div class="input-group">

                                                    <input type="number" name="forced_total" step="any" min="0"
                                                        value="{{$result->use_forced_total ? $result->forced_total: $result->total}}"
                                                        class="form-control"
                                                        required=""
                                                    >
                                                </div>

                                            </div>
                                        </div>
                                    @endif
                                    @if(!empty($result->extended) && ( ($result->use_forced_total ? $result->forced_total: $result->old_total) > $result->extended?->amount_due))
                                        <?php $refund_amount = 0; $refund_amount = ($result->use_forced_total ? $result->forced_total: $result->old_total) - abs($result->extended?->amount_due);   ?>
                                        <div class="form-group " id="refunded_total_div">
                                            <label class="col-sm-3 control-label">
                                                Refund Amount
                                            </label>
                                            <div class="col-sm-6 col-sm-offset-1">
                                                <span class="text-success">The difference is {{ $refund_amount }}</span>
                                                <div class="input-group d-flex">
                                                    <input type="number" name="refund_amount" id="refund_amount" min="1" step="any" min="0"
                                                        value="{{ $refund_amount }}"
                                                        class="form-control"
                                                        required
                                                    >
                                                        <a role="button" id="verify-refund-amount" class="btn btn-primary" href="javascript:void(0)">Verify</a>
                                                </div>
                                                @if($result->extended?->refunded_amount > 0)
                                                    <span class="text-warning mb-2">Refunded Amount : {{ $result->extended?->refunded_amount}}</span>
                                                @endif
                                                <span class="text-danger d-none" id="error-message-refund">Entered amount differs from the calculated value. Are you sure you want to proceed?</span>

                                            </div>
                                        </div>
                                    @endif
                              
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">
                                        Currency
                                    </label>
                                    <div class="col-sm-6 col-sm-offset-1">
                                        {!! $result->currency_code !!}
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-sm-3 control-label">
                                        Paymode
                                    </label>
                                    <div class="col-sm-6 col-sm-offset-1">
                                        {{ @$result->payment_methods->name }}
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-sm-3 control-label">
                                        Status
                                    </label>
                                    <div class="col-sm-6 col-sm-offset-1">
                                        {{ $result->status }}
                                    </div>
                                </div>

                                @if ($result->status == 'Cancelled')
                                    <div class="form-group">
                                        <label class="col-sm-3 control-label">
                                            Cancelled By
                                        </label>
                                        <div class="col-sm-6 col-sm-offset-1">
                                            {{ $result->cancelled_by }}
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="col-sm-3 control-label">
                                            Cancelled Date
                                        </label>
                                        <div class="col-sm-6 col-sm-offset-1">
                                            {{ dateFormat($result->cancelled_at) }}
                                        </div>
                                    </div>
                                @endif

                                @if ($result->transaction_id != '' && $result->transaction_id != ' ')
                                    <div class="form-group">
                                        <label class="col-sm-3 control-label">
                                            Transaction ID
                                        </label>
                                        <div class="col-sm-6 col-sm-offset-1">
                                            {{ $result->transaction_id }}
                                        </div>
                                    </div>
                                @endif


                                @if ($result->bank_id)

                                    <div class="form-group">
                                        <label class="col-sm-3 control-label">
                                            Bank Account Name
                                        </label>
                                        <div class="col-sm-6 col-sm-offset-1">
                                            {{ $result->bank && $result->bank->account_name ? $result->bank->account_name : 'Not Found' }}
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="col-sm-3 control-label">
                                            Bank Account No.
                                        </label>
                                        <div class="col-sm-6 col-sm-offset-1">
                                            {{ $result->bank && $result->bank->iban ? $result->bank->iban : 'Not Found' }}
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="col-sm-3 control-label">
                                            {{ customTrans('account_preference.bank_name') }}
                                        </label>
                                        <div class="col-sm-6 col-sm-offset-1">
                                            {{ $result->bank && $result->bank->bank_name
                                                ? $result->bank->bank_name
                                                : 'Not
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    									Found' }}
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="col-sm-3 control-label">
                                            Attached
                                        </label>
                                        <div class="col-sm-6 col-sm-offset-1">
                                            <a href="{{ $result->attachment }}" target="_blank"
                                               class="btn btn-outline-info">
                                                <i class="fa fa-cloud-download" aria-hidden="true"></i>
                                                Download
                                            </a>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="col-sm-3 control-label">
                                            Payment Note
                                        </label>
                                        <div class="col-sm-6 col-sm-offset-1">
                                            {{ $result->bank && $result->note ? $result->note : '' }}
                                        </div>
                                    </div>
                                    @if (($result->status == 'Pending' && $result->booking_type == 'instant') || $result->status == 'Processing')
                                        <div class="form-group">
                                            <label class="col-sm-3 control-label">
                                                Confirm Booking?
                                            </label>
                                            <div class="col-sm-6 col-sm-offset-1">
                                                <div class="d-flex">
                                                    <a href="{{ url('/admin/bookings/edit/confirm/' . $result->id) }}"
                                                       class="btn btn-sm btn-info btn-outline-info">Accept</a>
                                                    <a href="{{ url('/admin/bookings/edit/decline/' . $result->id) }}"
                                                       class="btn text-danger btn-outline-danger">Decline</a>
                                                </div>
                                            </div>
                                        </div>
                                    @endif
                                @endif

                                @if ($result->paymode == 'Credit Card')
                                    <div class="form-group">
                                        <label class="col-sm-3 control-label">
                                            First name
                                        </label>
                                        <div class="col-sm-6 col-sm-offset-1">
                                            {{ $result->first_name }}
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="col-sm-3 control-label">
                                            Last name
                                        </label>
                                        <div class="col-sm-6 col-sm-offset-1">
                                            {{ $result->last_name }}
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="col-sm-3 control-label">
                                            Postal code
                                        </label>
                                        <div class="col-sm-6 col-sm-offset-1">
                                            {{ $result->postal_code }}
                                        </div>
                                    </div>
                                @endif

                                @if ($result->host_account != '')
                                    <div class="form-group">
                                        <label class="col-sm-3 control-label">
                                            Host Paypal account
                                        </label>
                                        <div class="col-sm-6 col-sm-offset-1">
                                            {{ $result->host_account }}
                                        </div>
                                    </div>
                                @endif

                                @if (@$bank->account_number != '')
                                    <div class="form-group">
                                        <label class="col-sm-3 control-label">
                                            Host Bank account
                                        </label>
                                        <div class="col-sm-6 col-sm-offset-1">
                                            {{ $bank->account_number }}
                                        </div>
                                    </div>
                                @endif

                                @if ($result->guest_account != '')
                                    <div class="form-group">
                                        <label class="col-sm-3 control-label">
                                            Guest Paypal account
                                        </label>
                                        <div class="col-sm-6 col-sm-offset-1">
                                            {{ $result->guest_account }}
                                        </div>
                                    </div>
                                @endif

                                @if ($result->host_penalty_amount != 0)
                                    <div class="form-group">
                                        <label class="col-sm-3 control-label">
                                            Applied Penalty Amount
                                        </label>
                                        <div class="col-sm-6 col-sm-offset-1">
                                            {!! $result->currency->org_symbol !!}{{ $result->host_penalty_amount }}
                                        </div>
                                    </div>
                                @endif

                                @if (@$penalty_amount != 0)
                                    <div class="form-group">
                                        <label class="col-sm-3 control-label">
                                            Subtracted Penalty Amount
                                        </label>
                                        <div class="col-sm-6 col-sm-offset-1">
                                            {!! $result->currency->org_symbol !!}{{ @$penalty_amount }}
                                        </div>
                                    </div>
                                @endif
                            </div>
                        </form>


                        <div class="box-footer text-center">
                            <a class="btn btn-default" href="{{ url('admin/bookings') }}">Back</a>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
    @push('scripts')
        <script type="text/javascript">
            $(document).ready(function() {
                let TRY = false;
                const pathname = window.location.pathname
                console.log(pathname)
                $('#verify-refund-amount').on('click', function(){
                        var dataURL = "{{ route('verifyRefund') }}"
                        $('#verify-refund-amount').prop("disabled", true);
                    $.ajax({
                        url: dataURL,
                        data: {
                            '_token': "{{ csrf_token() }}",
                            'balance': $('#refund_amount').val(),
                            'id': "{{$result->id}}",
                            'try' : TRY
                        },
                        type: 'post',
                        dataType: 'json',
                        success: function(result) {
                            if (result.status == true) {
                                $('#error-message-refund').addClass('d-none');
                                TRY = false;
                                $('#verify-refund-amount').prop("disabled", false);
                                alert('success');
                                location.reload();
                            } else if(result.status == false && result.try == '1'){
                                TRY = true;
                                $('#error-message-refund').removeClass('d-none');
                                $('#verify-refund-amount').prop("disabled", false);
                            } else{
                                alert('Something went wrong');
                            }
                        }
                    });

                });
            })
            $(function() {
                var disabledDates = []
                var Checkinelement = document.getElementById('prev-checkin').value
                var Checkoutelement = document.getElementById('prev-checkout').value
                var currentDate = moment(Checkinelement, 'YYYY-MM-DD')
                var endDate = moment(Checkoutelement, 'YYYY-MM-DD')
                // console.log(currentDate.format('YYYY-MM-DD'));
                // console.log(endDate);
                // if(Checkinelement <= Checkoutelement){
                //     console.log("yes less than");
                // }
                while (currentDate <= endDate) {
                    disabledDates.push(currentDate.format('YYYY-MM-DD'))
                    currentDate = currentDate.add(1, 'days')
                }
                var unavailableDates = {!! json_encode($propertyDates) !!};
                // console.log(disabledDates);
                $('input[name="daterange"]').daterangepicker({
                    opens: 'center',
                    startDate: $('#url_checkin').val(),
                    endDate: $('#url_checkout').val(),
                    minDate: moment().format('MM-DD-YYYY'),

                    isInvalidDate: function(date) {
                        var formattedDate = date.format('YYYY-MM-DD')
                        return unavailableDates.includes(formattedDate)
                    }

                    // isInvalidDate: function(date) {

                    //     // var disabledDates = ['2023-05-15', '2023-05-16'];
                    //     var disabledDates = [];
                    //     return disabledDates.indexOf(date.format('YYYY-MM-DD')) != -1;
                    //     // var disabledStartDate = moment(2023-04-15);
                    //     // var disabledEndDate = moment(2023-04-16);
                    //     // return date.isBetween(disabledStartDate, disabledEndDate, null, '[]');
                    // }
                }, function(start, end, label) {


                })

                $('input[name="daterange"]').on('apply.daterangepicker', function(ev, picker) {
                    var startDate = picker.startDate.format('MM/DD/YYYY')
                    var endDate = picker.endDate.format('MM/DD/YYYY')
                    $('#url_checkin').val(startDate)
                    $('#url_checkout').val(endDate)

                    document.getElementById('saveExtendedBooking').classList.remove('d-none');
                    document.getElementById('saveExtendedBooking').classList.add('d-block');
                    

                })

                $('#extendedBookingSaveBtn').on('click', function(){
                    bookingExtend()
                    
                })


            })

            function bookingExtend(){
                var get_checkin = $('#url_checkin').val()
                    var get_checkout = $('#url_checkout').val()
                    var booking_id = $('#booking_id').val()

                    var guest = $('#number_of_guests').val()


                    var dataURL = "{{ route('bookingExtend') }}"
                    $.ajax({
                        url: dataURL,
                        data: {
                            '_token': "{{ csrf_token() }}",
                            'checkin': get_checkin,
                            'checkout': get_checkout,
                            'guest_count': guest,
                            'booking_id': booking_id
                        },
                        type: 'post',
                        dataType: 'json',
                        beforeSend: function() {
                            // $('.price_table').addClass('d-none');
                            // show_loader();
                        },
                        success: function(result) {
                            if (result.message == 'Success') {
                                document.getElementById('extended_iva_tax').classList.remove(
                                    'd-none')
                                document.getElementById('extended_iva_tax').classList.add(
                                    'd-block')
                                document.getElementById('extended_servicefee').classList.remove(
                                    'd-none')
                                document.getElementById('extended_servicefee').classList.add(
                                    'd-block')
                                document.getElementById('extended_nights').classList.remove(
                                    'd-none')
                                document.getElementById('extended_nights').classList.add(
                                    'd-block')
                                document.getElementById('extended_subtotal').classList.remove(
                                    'd-none')
                                document.getElementById('extended_subtotal').classList.add(
                                    'd-block')
                                document.getElementById('extended_total').classList.remove(
                                    'd-none')
                                document.getElementById('extended_total').classList.add(
                                    'd-block')
                                document.getElementById('extended_checkin').classList.remove(
                                    'd-none')
                                document.getElementById('extended_checkin').classList.add(
                                    'd-block')
                                document.getElementById('extended_checkout').classList.remove(
                                    'd-none')
                                document.getElementById('extended_checkout').classList.add(
                                    'd-block')
                                document.getElementById('total_night').innerHTML = result.data
                                    .total_nights
                                document.getElementById('check_in').innerHTML = moment(result.data
                                    .checkin).format('MM-DD-YYYY')
                                document.getElementById('check_out').innerHTML = moment(result.data
                                    .checkout).format('MM-DD-YYYY')
                                document.getElementById('total_amount').innerHTML = result.data
                                    .total
                                document.getElementById('sub_total_amount').innerHTML = result.data
                                    .sub_total

                                document.getElementById('service_fee').innerHTML = result.data
                                    .service_charge
                                document.getElementById('iva_tax').innerHTML = result.data
                                    .iva_tax
                                    document.getElementById('property_price').innerHTML = result.data
                                    .property_price
                                    document.getElementById('darent_discount').innerHTML = result.data
                                    .discount

                                document.getElementById('confirmationBox').classList.remove(
                                    'd-none')
                                document.getElementById('confirmationBox').classList.add('d-block')
                                document.getElementById('price_error').classList.remove('d-block')
                                document.getElementById('price_error').classList.add('d-none')
                                document.getElementById('saveExtendedBooking').classList.remove('d-block')
                                document.getElementById('saveExtendedBooking').classList.add('d-none')

                            }
                            if (result.message == 'price_error') {
                                document.getElementById('price_error').classList.remove('d-none')
                                document.getElementById('price_error').classList.add('d-block')
                                if (result.status == 'Not available') {
                                    document.getElementById('price_error').innerHTML = 'Dates Not Available'

                                }
                                if (result.status == 'nights become min') {
                                    document.getElementById('price_error').innerHTML = 'This Property Allow Min Nights ' + result.min_nights

                                }
                                if (result.status == 'nights become max') {
                                    document.getElementById('price_error').innerHTML = 'This Property Allow Max Nights ' + result.max_nights

                                }
                                document.getElementById('confirmationBox').classList.remove(
                                    'd-block')
                                document.getElementById('confirmationBox').classList.add('d-none')
                                document.getElementById('paymentUrlDiv').classList.remove('d-block')
                                document.getElementById('paymentUrlDiv').classList.add('d-none')
                            }
                            if (result.message == 'BookingUpdated') {
                                alert('Booking Updated Successfully')
                                
                                document.getElementById('price_error').classList.remove('d-block')
                                document.getElementById('price_error').classList.add('d-none')
                                document.getElementById('paymentUrlDiv').classList.remove('d-block')
                                document.getElementById('paymentUrlDiv').classList.add('d-none')
                                document.getElementById('saveExtendedBooking').classList.remove('d-block')
                                document.getElementById('saveExtendedBooking').classList.add('d-none')

                            }

                        },
                        error: function(request, error) {
                            if (request.responseJSON.message == 'Failure') {
                                alert('your can not select few days than previous booking')
                            }
                            // if(request.responseJSON.message == "price_error"){
                            //     alert("your can not select few days than previous booking");
                            // }
                            // console.log(request.responseJSON.message);
                            // console.log(error);
                        },
                        complete: function() {
                            // hide_loader();
                        }
                    })
            }

            function copyFunction(event) {
                event.preventDefault()
                // Get the text field
                var copyText = document.getElementById('payment_url')

                // Select the text field
                copyText.select()
                copyText.setSelectionRange(0, 99999) // For mobile devices

                // Copy the text inside the text field
                navigator.clipboard.writeText(copyText.value)

            }

            $('#confirmationBtn').on('click', function() {
                // event.preventDefault();
                var total_night = document.getElementById('total_night').innerHTML
                // alert(total_night);
                var check_in = document.getElementById('check_in').innerHTML
                var check_out = document.getElementById('check_out').innerHTML
                var total_amount = document.getElementById('total_amount').innerHTML
                var sub_total_amount = document.getElementById('sub_total_amount').innerHTML
                // let total_night = document.getElementById("payment_url").value;
                var service_fee = document.getElementById('service_fee').innerHTML
                var iva_tax = document.getElementById('iva_tax').innerHTML
                var discount = document.getElementById('darent_discount').innerHTML
                var property_price = document.getElementById('property_price').innerHTML
                var booking_id = $('#booking_id').val()

                var guest = $('#number_of_guests').val()
                var dataURL = "{{ route('extendBookingConfirm') }}"
                $.ajax({
                    url: dataURL,
                    data: {
                        '_token': "{{ csrf_token() }}",
                        'total_night': total_night,
                        'checkin': check_in,
                        'checkout': check_out,
                        'total': total_amount,
                        'sub_total': sub_total_amount,
                        'service_fee': service_fee,
                        'iva_tax': iva_tax,
                        'property_price': property_price,
                        'booking_id': booking_id,
                        'guest': guest,
                        'discount': discount
                    },
                    type: 'post',
                    dataType: 'json',
                    beforeSend: function() {
                        // $('.price_table').addClass('d-none');
                        // show_loader();
                    },
                    success: function(result) {
                        if (result.message == 'Success') {

                            document.getElementById('confirmationBox').classList.remove(
                                'd-block')
                            document.getElementById('confirmationBox').classList.add('d-none')
                            if(result.data.darent_url){
                                document.getElementById('paymentUrlDiv').classList.remove('d-none')
                                document.getElementById('paymentUrlDiv').classList.add('d-block')
                                document.getElementById('payment_url').value = result.data
                                    .darent_url
                            } else{
                                location.reload();
                            }
                        }
                        if (result.message == 'BookingUpdated') {
                            alert('Booking Updated Successfully')
                            document.getElementById('price_error').classList.remove('d-block')
                            document.getElementById('price_error').classList.add('d-none')
                            document.getElementById('confirmationBox').classList.remove(
                                'd-block')
                            document.getElementById('confirmationBox').classList.add('d-none')
                            if(result.data.darent_url){
                                document.getElementById('paymentUrlDiv').classList.remove('d-block')
                                document.getElementById('paymentUrlDiv').classList.add('d-none')
                                document.getElementById('refunded_total_div').classList.add('d-none')

                            } else{
                                location.reload();
                            }
                        }

                    },
                    error: function(request, error) {
                        if (request.responseJSON.message == 'Failure') {
                            alert('your can not select few days than previous booking')
                            document.getElementById('paymentUrlDiv').classList.remove('d-block')
                            document.getElementById('paymentUrlDiv').classList.add('d-none')
                                document.getElementById('refunded_total_div').classList.add('d-none')
                        }
                        // if(request.responseJSON.message == "price_error"){
                        //     alert("your can not select few days than previous booking");
                        // }
                        // console.log(request.responseJSON.message);
                        // console.log(error);
                    },
                    complete: function() {
                        // hide_loader();
                    }

                })

            })
            // $('#input_dob').datepicker({ 'format': 'dd-mm-yyyy'});

            let forcedTotalInput = document.querySelector('input[name="forced_total"]')

            if (forcedTotalInput) {
                forcedTotalInput.addEventListener('input', function(e) {
                    debouncedUpdateForcedTotal(e.target.value)
                })
            }

            function debounce(func, wait) {
                let timeout;
                return function(...args) {
                    clearTimeout(timeout);
                    timeout = setTimeout(() => func.apply(this, args), wait);
                };
            }

            // Wrap updateForcedTotal with debounce (e.g., 500ms delay)
            const debouncedUpdateForcedTotal = debounce(updateForcedTotal, 1000);


            function updateDetails(event,id){
                event.preventDefault();
                var sub_total_amount = document.getElementById('sub_total_amount').innerHTML
                $.ajax({
                    url: "{{ route('admin.update.booking.details') }}",
                    data: {
                        '_token': "{{ csrf_token() }}",
                        'booking_id': id,
                        'sub_total' : sub_total_amount,
                        'discount' : document.getElementById('darent_discount').innerHTML

                    },
                    type: 'post',
                    dataType: 'json',
                    success: function(result) {
                        if (result.message == 'BookingUpdated') {
                            alert('Booking updated successfully')
                        } else {
                            alert('Something went wrong')
                        }
                    },
                    error: function(request, error) {
                       alert('Something went wrong')
                    }
                })
            }

            function updateForcedTotal(total) {
                $.ajax({
                    url: '/admin/bookings/update-forced-total',
                    data: {
                        '_token': "{{ csrf_token() }}",
                        'booking_id': "{{$result->id}}",
                        'total': total
                    },
                    type: 'post',
                    dataType: 'json',
                    success: function(result) {
                        if (result.status == 400) {
                            alert('You don\'t have enough balance to update the booking total');
                        }else{
                            if (result.data.original_total) {
                            document.getElementById('original_total').innerHTML = result.data.original_total
                            }
                        }


                    },
                    error: function(request, error) {
                        if (request.responseJSON.message == 'Failure') {
                            alert('your can not select few days than previous booking')
                        }

                    }
                })
            }

        </script>
    @endpush
@stop
