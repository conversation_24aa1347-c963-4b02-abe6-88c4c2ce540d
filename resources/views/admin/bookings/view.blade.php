 @php
 @endphp
 @extends('admin.template')
 @section('main')
     <div class="content-wrapper">
         <section class="content-header">
             <h1>Bookings<small>Control panel</small></h1>
             @include('admin.common.breadcrumb')
         </section>

         <section class="content">
             <div class="row">
                 <div class="col-xs-12">
                     <div class="box">
                         <div class="box-body">
                             <form class="form-horizontal" enctype='multipart/form-data' action="{{ url('admin/bookings') }}"
                                 method="GET" accept-charset="UTF-8">
                                 {{ csrf_field() }}
                                 <div class="col-md-12  d-none">
                                     <input class="form-control" type="text" id="startDate" name="from"
                                         value="<?= isset($from) ? $from : '' ?>" hidden>
                                     <input class="form-control" type="text" id="endDate" name="to"
                                         value="<?= isset($to) ? $to : '' ?>" hidden>
                                 </div>
                                 <div class="col-md-12">
                                     <div class="">
                                         <div class="row">
                                             <div class="col-md-3 col-sm-4 col-xs-12">
                                                 <label>Date Range</label>
                                                 <div class="input-group  col-xs-12">
                                                     <button type="button" class="form-control" id="daterange-btn">
                                                         <span class="pull-left">
                                                             <i class="fa fa-calendar"></i> Pick a date range
                                                         </span>
                                                         <i class="fa fa-caret-down pull-right"></i>
                                                     </button>
                                                 </div>
                                             </div>

                                             {{-- <div class="col-md-3 col-sm-4 col-xs-12">
                                                 <label>Property</label>
                                                 <select class="form-control select2" name="property" id="property">
                                                     <option value="">All</option>
                                                     @if (!empty($properties))
                                                         @foreach ($properties as $property)
                                                             <option value="{{ $property->id }}"
                                                                 {{ $property->id == $allproperties ? ' selected="selected"' : '' }}>
                                                                 {{ $property->name }}</option>
                                                         @endforeach
                                                     @endif
                                                 </select>
                                             </div> --}}

                                             <div class="col-md-3 col-sm-4 col-xs-12">
                                                 <label>Customer</label>
                                                 <select class="form-control select2customer" name="customer"
                                                     id="customer">
                                                     <option value="">All</option>
                                                     @if (!empty($customers))
                                                         @foreach ($customers as $customer)
                                                             <option value="{{ $customer->id }}"
                                                                 {{ $customer->id == $allcustomers ? ' selected="selected"' : '' }}>
                                                                 {{ $customer->first_name . ' ' . $customer->last_name }}
                                                             </option>
                                                         @endforeach
                                                     @endif
                                                 </select>
                                             </div>

                                             <div class="col-md-3 col-sm-4 col-xs-12">
                                                 <label>Status</label>
                                                 <select class="form-control" name="status" id="status">
                                                     <option value="">All</option>
                                                     <option value="Accepted"
                                                         {{ $allstatus == 'Accepted' ? ' selected="selected"' : '' }}>
                                                         Accepted</option>
                                                     <option value="Cancelled"
                                                         {{ $allstatus == 'Cancelled' ? ' selected="selected"' : '' }}>
                                                         Cancelled</option>
                                                     <option value="Declined"
                                                         {{ $allstatus == 'Declined' ? ' selected="selected"' : '' }}>
                                                         Declined</option>
                                                     <option value="Expired"
                                                         {{ $allstatus == 'Expired' ? ' selected="selected"' : '' }}>
                                                         Expired</option>
                                                     <option value="Pending"
                                                         {{ $allstatus == 'Pending' ? ' selected="selected"' : '' }}>
                                                         Pending</option>
                                                 </select>
                                             </div>

                                             <div class="col-md-3 col-sm-4 col-xs-12">
                                                 <br>
                                                 <label>
                                                     <input class="form-check-input" type="checkbox" name="referral"
                                                         value="1" {{ $referral == 1 ? 'checked' : '' }}>
                                                     Referral Bookings
                                                 </label>
                                             </div>

                                             <div class="col-md-1 col-sm-2 col-xs-4">
                                                 <br>
                                                 <button type="submit" name="btn"
                                                     class="btn btn-primary btn-flat">Filter</button>
                                             </div>

                                             <div class="col-md-1 col-sm-2 col-xs-4">
                                                 <br>
                                                 <button type="submit" name="reset_btn"
                                                     class="btn btn-primary btn-flat">Reset</button>
                                             </div>
                                         </div>
                                     </div>
                                 </div>
                             </form>
                         </div>
                     </div>
                 </div>
             </div>

             <div class="row">
                 <div class="col-xs-12">
                     <div class="box">
                         <div class="box-body">
                             <div class="row">
                                 <div class="col-md-3">
                                     <div class="panel panel-primary">
                                         <div class="panel-body text-center">
                                             <span class="text-20">{{ $total_bookings }}</span><br>
                                             <span class="font-weight-bold total-book">Total Bookings</span>
                                             <br>
                                             @if ($different_total_amounts)
                                                 {{-- @foreach ($different_total_amounts as $total_amount) --}}
                                                 <span class="text-8">{{ $different_total_amounts }}</span>
                                                 {{-- {{ $current_currency_symbol->currency_symbol }} --}}
                                                 {{-- @endforeach --}}
                                             @endif
                                         </div>
                                     </div>
                                 </div>
                                 {{-- @if ($different_total_amounts)
                                    @foreach ($different_total_amounts as $total_amount)
                                        <div class="col-md-3">
                                            <div class="panel panel-primary">
                                                <div class="panel-body text-center">
                                                    <span class="text-20">{!! $total_amount['total'] !!}</span><br>
                                                    Total<span class="font-weight-bold total-amount">
                                                        {{ $total_amount['currency_code'] }}</span> amount
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                @endif --}}
                                 <div class="col-md-3">
                                     <div class="panel panel-primary">
                                         <div class="panel-body text-center">
                                             <span class="text-20">{{ $unpaidCount }}</span><br>
                                             <span class="font-weight-bold total-customer">Upaid Bookings</span><br>
                                             <span class="text-8">{{ number_format($unpaidSum, 2, '.', '') }}</span>
                                             {{-- {{ $current_currency_symbol->currency_symbol }} --}}
                                         </div>
                                     </div>
                                 </div>
                                 <div class="col-md-3">
                                     <div class="panel panel-primary">
                                         <div class="panel-body text-center">
                                             <span class="text-20">{{ $cancelledCount }}</span><br>
                                             <span class="font-weight-bold total-customer">Cancelled Bookings</span><br>
                                             <span class="text-8">{{ number_format($cancelledSum, 2, '.', '') }}</span>
                                             {{-- {{ $current_currency_symbol->currency_symbol }} --}}
                                         </div>
                                     </div>
                                 </div>
                                 <div class="col-md-3">
                                     <div class="panel panel-primary">
                                         <div class="panel-body text-center">
                                             <span class="text-20">{{ $acceptedCount }}</span><br>
                                             <span class="font-weight-bold total-customer">Accepted Bookings</span><br>
                                             <span class="text-8">{{ number_format($acceptedSum, 2, '.', '') }}</span>
                                             {{-- {{ $current_currency_symbol->currency_symbol }} --}}
                                         </div>
                                     </div>
                                 </div>
                             </div>
                             <div class="row">
                                 <div class="col-md-3">
                                     <div class="panel panel-primary">
                                         <div class="panel-body text-center">
                                             <span class="text-20">{{ $pendingCount }}</span><br>
                                             <span class="font-weight-bold total-customer">Pending Bookings</span><br>
                                             <span class="text-8">{{ number_format($pendingSum, 2, '.', '') }}</span>
                                             {{-- {{ $current_currency_symbol->currency_symbol }} --}}
                                         </div>
                                     </div>
                                 </div>
                                 <div class="col-md-3">
                                     <div class="panel panel-primary">
                                         <div class="panel-body text-center">
                                             <span class="text-20">{{ $processingCount }}</span><br>
                                             <span class="font-weight-bold total-customer">Processing Bookings</span><br>
                                             <span class="text-8">{{ number_format($processingSum, 2, '.', '') }}</span>
                                             {{-- {{ $current_currency_symbol->currency_symbol }} --}}
                                         </div>
                                     </div>
                                 </div>
                                 <div class="col-md-3">
                                     <div class="panel panel-primary">
                                         <div class="panel-body text-center">
                                             <span class="text-20">{{ $declineCount }}</span><br>
                                             <span class="font-weight-bold total-customer">Declined Bookings</span><br>
                                             <span class="text-8">{{ number_format($declinedSum, 2, '.', '') }}</span>
                                             {{-- {{ $current_currency_symbol->currency_symbol }} --}}
                                         </div>
                                     </div>
                                 </div>
                                 <div class="col-md-3">
                                     <div class="panel panel-primary">
                                         <div class="panel-body text-center">
                                             <span class="text-20">{{ $expiredCount }}</span><br>
                                             <span class="font-weight-bold total-customer">Expired Bookings</span><br>
                                             <span class="text-8">{{ number_format($expiredSum, 2, '.', '') }}</span>
                                             {{-- {{ $current_currency_symbol->currency_symbol }} --}}
                                         </div>
                                     </div>
                                 </div>
                             </div>
                             <div class="row">
                                 <div class="col-md-3">
                                     <div class="panel panel-primary">
                                         <div class="panel-body text-center">
                                             <span class="text-20">{{ number_format($total_booking_nights, 2, '.', '') }}
                                             </span><br>
                                             <span class="font-weight-bold total-customer">Total Nights</span>
                                         </div>
                                     </div>
                                 </div>
                                 <div class="col-md-3">
                                     <div class="panel panel-primary">
                                         <div class="panel-body text-center">
                                             <span class="text-20">{{ number_format($total_revenue, 2, '.', '') }}
                                                 {{-- {{ $current_currency_symbol->currency_symbol }} --}}
                                             </span><br>
                                             <span class="font-weight-bold total-customer">Total Revenue</span>
                                         </div>
                                     </div>
                                 </div>
                             </div>
                         </div>
                     </div>
                 </div>
             </div>






             <div class="row">
                 <div class="col-xs-12">
                     <div class="box">
                         <div class="box-header">
                             <h3 class="box-title">Bookings Management</h3>
                             @if (Helpers::has_permission(Auth::guard('admin')->user()->id, 'add_properties'))
                                 <div class="pull-right"><a class="btn btn-success" href="{{ route('addBooking') }}">Add
                                         Booking</a></div>
                             @endif
                         </div>
                         <div class="box-body">
                             <div class="table-responsive">
                                 {!! $dataTable->table([
                                     'class' => 'table table-striped table-hover dt-responsive',
                                     'width' => '100%',
                                     'cellspacing' => '0',
                                 ]) !!}
                             </div>
                         </div>
                     </div>
                 </div>
             </div>
         </section>
     </div>
     <div class="modal fade" id="tawuniyaInsuranceModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
         aria-hidden="true">
         <div class="modal-dialog" role="document">
             <div class="modal-content">
                 <div class="modal-header text-center">
                     <h4 class="modal-title w-100 font-weight-bold">Tawniya</h4>
                     <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                         <span aria-hidden="true">&times;</span>
                     </button>
                 </div>
                 <div class="modal-body mx-3">
                     <ul class="ti-modal-list">
                         <form>
                             <input type="hidden" name="booking_id" value="">
                         </form>
                     </ul>
                 </div>
             </div>
         </div>
     </div>

 @endsection
 @push('scripts')
     <script src="{{ asset('DataTables-1.10.18/js/jquery.dataTables.min.js') }}"></script>
     <script src="{{ asset('Responsive-2.2.2/js/dataTables.responsive.min.js') }}"></script>
     <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
     {!! $dataTable->scripts() !!}
 @endpush
 @section('validate_script')
     <script type="text/javascript">
         // Select 2 for property search
         $('.select2').select2({
             // minimumInputLength: 3,
             ajax: {
                 url: 'bookings/property_search',
                 processResults: function(data) {
                     $('#property').val('DSD');
                     return {
                         results: data
                     };
                 }
             }
         });

         $('.select2customer').select2({
             ajax: {
                 url: 'bookings/customer_search',
                 processResults: function(data) {
                     $('#customer').val('DSD');
                     return {
                         results: data
                     };
                 }
             }
         });

         $(function() {
             var startDate = $('#startDate').val();
             var endDate = $('#endDate').val();
             dateRangeBtn(startDate, endDate, dt = 1);
             formDate(startDate, endDate);
             // <li><a href="" title="PDF" id="pdf">PDF</a></li>
             $(document).ready(function() {
                 $('#dataTableBuilder_length').after(
                     '<div id="exportArea" class="col-md-2 col-sm-2 "><div class="row mt-m-2"><div class="btn-group col-6"><button type="button" class="form-control dropdown-toggle" data-toggle="dropdown" aria-haspopup="true">Export</button><ul class="dropdown-menu"><li><a href="" title="CSV" id="csv">CSV</a></li></ul></div><div class="btn btn-group btn-refresh col-6"><a href="" id="tablereload" class="form-control"><span><i class="fa fa-refresh"></i></span></a></div></div></div>'
                 );
             });

             //csv convert
             $(document).on("click", "#csv", function(event) {
                 event.preventDefault();
                 var property = $('#property').val();
                 var customer = $('#customer').val();
                 var status = $('#status').val();
                 var to = $('#endDate').val();
                 var from = $('#startDate').val();
                 window.location = "booking/booking_list_csv?to=" + to + "&from=" + from + "&property=" +
                     property + "&status=" + status + "&customer=" + customer;
             });
             //pdf convert
             $(document).on("click", "#pdf", function(event) {
                 event.preventDefault();
                 var property = $('#property').val();
                 var customer = $('#customer').val();
                 var status = $('#status').val();
                 var to = $('#endDate').val();
                 var from = $('#startDate').val();
                 window.location = "booking/booking_list_pdf?to=" + to + "&from=" + from + "&property=" +
                     property + "&status=" + status + "&customer=" + customer;
             });
             //reload Datatable
             $(document).on("click", "#tablereload", function(event) {
                 event.preventDefault();
                 $("#dataTableBuilder").DataTable().ajax.reload();
             });
         });
         $(document).ready(function() {
             $('.applyBtn').on('click', function(ev) {
                 let picker = $('#daterange-btn').data('daterangepicker');
                 if (picker.startDate && !picker.endDate) {
                     let nextDay = picker.startDate.clone().add(1, 'day');
                     picker.endDate = nextDay;
                     $('#daterange-btn').data('daterangepicker').endDate = picker.endDate;
                     $('#daterange-btn').val(picker.startDate + ' - ' +
                         picker.endDate);
                 }
             });
         });

        $(document).on('change', '.status-dropdown', function () {
            const dropdown = $(this);
            const bookingId = dropdown.data('id');
            const newStatus = dropdown.val();

            // Get the previous value to revert if cancelled
            const previousStatus = dropdown.find('option').filter(':selected').val();

            Swal.fire({
                title: 'Are you sure?',
                text: `Change booking status to "${newStatus}"?`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, change it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    $.ajax({
                        url: "{{ route('admin.booking.status.force') }}",
                        method: 'POST',
                        data: {
                            _token: $('meta[name="csrf-token"]').attr('content'),
                            booking_id: bookingId,
                            status: newStatus
                        },
                        success: function (response) {
                            if(response.status == "error"){
                                Swal.fire('Error', 'Failed to update status', 'error');
                                // Optionally revert dropdown to previous value
                                dropdown.val(previousStatus);
                            } else {
                                Swal.fire('Updated!', 'Status has been changed.', 'success');
                            }
                        },
                        error: function () {
                            Swal.fire('Error', 'Failed to update status', 'error');
                            // Optionally revert dropdown to previous value
                            dropdown.val(previousStatus);
                        }
                    });
                } else {
                    // Revert to previous status if user cancels
                    dropdown.val(previousStatus);
                }
            });
        });
         $(document).on('click', '.tawuniyaBtn', function() {
             var bookingId = $(this).data('booking-id');
             console.log('Booking ID:', bookingId);

             $('#tawuniyaInsuranceModal').find('input[name="booking_id"]').val(bookingId);

             $.ajax({
                 url: '{{ route('admin.checkCertificate') }}', // URL to your server-side endpoint
                 method: 'POST',
                 data: {
                     booking_id: bookingId
                 },
                 success: function(response) {
                     var modalContent = '';
                     if (response.create_qoutation) {
                         modalContent += `
                    <p>Error: ${response.failure_quotation_payload}</p>
                    <button class="btn btn-secondary" onclick="window.location.href='${response.createQuotationUrl}'">Redo Quotation</button>
                `;
                     } else {
                         modalContent += `
                    <p>Success: ${response.success_quotation_payload}</p>
                `;
                     }

                     if (response.activate_certificate) {
                         modalContent += `
                    <p>Error: ${response.failure_certificate_payload}</p>
                    <button class="btn btn-secondary" onclick="window.location.href='${response.activateCertificateUrl}'">Activate Certificate</button>
                `;
                     } else if (response.success_certificate_payload) {
                         modalContent += `
                    <p>Success: ${response.success_certificate_payload}</p>
                `;
                     }

                     $('#tawuniyaInsuranceModal .modal-body .ti-modal-list').html(modalContent);
                     $('#tawuniyaInsuranceModal').modal('show');
                 },
                 error: function(error) {
                     console.error('An error occurred:', error);
                 }
             });
         });
     </script>
 @endsection
