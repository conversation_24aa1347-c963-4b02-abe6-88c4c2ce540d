@section('properties_data')
    @php
        use App\Models\WishlistName;
        use App\Models\Properties;


        $wishlistExist = WishlistName::where('creater_id', Auth::id())->get();
        if (isset($wishlistExist)) {
            $existWishlistModal = true;
            $wishlist_modal = true;
        } else {
            $existWishlistModal = false;
            $wishlist_modal = true;
        }
        $url = '';
        $dis = '#property_location';
        if(session()->get('header_checkin') && session()->get('header_checkout')){
            $url = '?checkin='.session()->get('header_checkin').'&checkout='.session()->get('header_checkout');
            $dis = '&#property_location';
        } else if(!empty($_GET['checkin']) && !empty($_GET['checkout'])){
            $url = '?checkin='.$_GET['checkin'].'&checkout='.$_GET['checkout'];
            $dis = '&#property_location';
        } else{
            $url = '';
            $dis = '#property_location';
        }
    @endphp
    @forelse ($search_result as $prop)

    <x-property-card :property="$prop" :url="$url" :dis="$dis" :wishlistExist="$wishlistExist" />

    @empty
        <div class="text-center justify-content-center w-100 position-center">
            <img width="400" src="{{ asset('icons/no-data-found.svg') }}" class="img-fluid not-found" alt="not-found">
            <h4 class="text-center text-20 font-weight-700">
                {{ customTrans('search.no_result_found') }}
            </h4>
        </div>
    @endforelse
@endsection
