@extends('template')

@section('main')
    @push('css')
        <script>
            document.documentElement.style.overflow = 'hidden'
        </script>
        <link rel="stylesheet"
              href="https://cdn.jsdelivr.net/npm/bootstrap-select@1.13.14/dist/css/bootstrap-select.min.css">
    @endpush
    @php
        $wishlist_modal = true;
        use App\Models\Settings;
        use App\Models\Properties;
        use App\Models\WishlistName;
        $wishlistExist = WishlistName::where('creater_id', Auth::id())->get();
        if (isset($wishlistExist)) {
            $existWishlistModal = true;
            $wishlist_modal = true;
        } else {
            $existWishlistModal = false;
            $wishlist_modal = true;
        }


    @endphp
    <section class="filter-main-inner">
        <div class="container">
            <div class="filter-design">
                @include('common.search_filter')
            </div>
        </div>
    </section>

    <section class="services mt-3">
        <div class="container">
            <div class="head">
                <div class="row align-items-center justify-content-between view-sc">
                    <div class="col-xl-3 col-md-3 col-sm-7 col-xs-12">
                        <h1>{{ customTrans('homepage.explore_next_vacation') }}</h1>
                    </div>
                    <div class="col-xl-5 col-md-6 col-sm-12 col-xs-12">
                        <ul class="product-category-2">
                            @php
                                $ptype = request()->query('property_type') ?? null;
                            @endphp
                            @forelse ($property_type as $type)

                                <li class='property_types_image'>
                                    <label class="listing-checkbox-wrapper fsrch">
                                        <input hidden type="checkbox" class='listing-checkbox-input'
                                               name="property_type[]"
                                               value="{{ $type->id }}" {{ $ptype == $type->id ? 'checked' : '' }} id="property_type_{{ $type->id }}" />
                                        <div class="listing-checkbox-tile">
                                            <div class="listing-checkbox-icon">
                                                <img src="{{ asset('icons/' . $type->icon_image) }}" alt="">
                                            </div>
                                            <div class="listing-checkbox-label">
                                                <p>{{ app()->getLocale() == 'ar' ? $type->name_ar : $type->name }}</p>
                                            </div>
                                        </div>
                                    </label>
                                </li>
                            @empty
                                No types available
                            @endforelse
                        </ul>
                    </div>
                    <div class="col-xl-4 col-md-3 col-lg-3 col-sm-5 col-xs-12 v-filter">
                        <div class="services-btn">
                            <div class="filter-btn">
                                <button class="grey-btn inner-btn dropdown-toggle" type="button" id="priceFilter"
                                        data-bs-toggle="dropdown" aria-expanded="false"><img
                                        src="{{ asset('icons/price-filter.svg') }}" alt=""></button>
                                <button class="grey-btn inner-btn" type="button" data-bs-toggle="modal"
                                        data-bs-target="#filter"><img src="{{ asset('icons/filter.svg') }}"
                                                                      alt="">{{ app()->getLocale() == 'ar' ? 'التصفية' : 'Filter' }}
                                </button>
                                {{-- <button class="theme-btn inner-btn"><img src="icons/view-list.svg" class="list-icon"
                                    alt=""><a href="home_user.html">View as a list</a></button> --}}
                                <button id="closeMap" class="theme-btn inner-btn closeMap"><img
                                        src="{{ asset('icons/view-list.svg') }}" class="list-icon"
                                        alt="">{{ app()->getLocale() == 'ar' ? 'عرض على شكل قائمة' : 'View as a list' }}
                                </button>
                                {{-- <button id="showMap" class="theme-btn inner-btn showMap"><img src="icons/show-on-map.svg"
                                        alt="">{{ app()->getLocale() == 'ar' ? 'عرض كخريطة' : 'Show map' }}</button> --}}


                                <button class="theme-btn inner-btn show-grid d-none" type="button">
                                    <i class="ri-layout-grid-fill"></i> Show Grid
                                </button>


                                {{--
                                <ul class="dropdown-menu custom-dropdown-menu" aria-labelledby="priceFilter">
                                    <li>
                                        <a class="dropdown-item priceFilter" data-value="asc"
                                            href="javascript:;">{{ customTrans('sort.low_prices_first') }}</a>
                                        <a class="dropdown-item priceFilter" data-value="desc"
                                            href="javascript:;">{{ customTrans('sort.high_prices_first') }}</a>
                                        <a class="dropdown-item priceFilter" data-value="mostRatedFirst"
                                            href="javascript:;">{{ customTrans('sort.most_rated_first') }}</a>
                                        <a class="dropdown-item priceFilter" data-value="nearestToCity"
                                            href="javascript:;">{{ customTrans('sort.newest_to_the_city') }}</a>
                                    </li>
                                </ul> --}}
                                <div class="overlay"></div>
                                <ul class="dropdown-menu sm-price-filter dropdown-menu-end dubai-ff"
                                    aria-labelledby="priceFilter">
                                    <li>
                                        <a class="dropdown-item priceFilter" data-value="asc"
                                           href="javascript:">{{ customTrans('sort.low_prices_first') }}</a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item priceFilter" data-value="desc"
                                           href="javascript:">{{ customTrans('sort.high_prices_first') }}</a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item priceFilter" data-value="mostRatedFirst"
                                           href="javascript:">{{ customTrans('sort.most_rated_first') }}</a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item priceFilter" data-value="nearestToCity"
                                           href="javascript:">{{ customTrans('sort.newest_to_the_city') }}</a>
                                    </li>

                                    <li>
                                        <a class="dropdown-item priceFilter" data-value="recentlyAdded"
                                           href="javascript:">{{ customTrans('sort.recently_added') }}</a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>

        <div class="container fl-container">

            <div class="cstm-property">
                <div class="row">
                    <div class="aft-map grid">
                        <div class="property">

                            {{-- <div id="loader" class="d-none loader-img text-center">
                                <div>
                                    <img src="{{ asset('icons/loader.gif') }}" alt="loader">
                                    <h4>{{ app()->getLocale() == 'ar' ? 'جار التحميل' : ' Loading...' }}</h4>
                                </div>
                            </div> --}}

                            <div class="row" id="properties_show">
                                {{-- SEARCH RESULT WILL SHOWN HERE --}}
                            </div>
                        </div>
                        <div id="loader-more" class="d-none loader-img text-center">
                            <div class="text-center">
                                {{-- <img src="{{ asset('icons/loader.gif') }}" alt="loader"> --}}
                                <div class="dots"></div>
                                <h4>{{ app()->getLocale() == 'ar' ? 'جار التحميل' : ' Loading...' }}</h4>
                            </div>
                        </div>
                        <!-- Pagination start -->

                    </div>
                    <div class="grid">
                        <div id="mapCol" class="v-none mapCol">
                            <div id="map_view" class="map-view" style="display: none;">
                            </div>
                        </div>
                    </div>
                </div>
            </div>


        </div>
        {{-- Wishlits Listing Modal --}}
        <div class="modal fade dubai-ff" id="whishlist-listing" data-bs-backdrop="static" data-bs-keyboard="false"
             tabindex="-1" aria-labelledby="whishlist-listing" aria-hidden="true">
            <div
                class="modal-dialog custom-modal-dialog modal-dialog-centered modal-dialog-scrollable bt-grey-scroller">
                <div class="modal-content cm-bd-content">
                    <div class="modal-header cm-bd-header">
                        <h5 class="w-100 text-center mb-0" id="whishlist-listing">
                            {{ customTrans('wishlist.your_wishlists') }}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                        </button>
                    </div>
                    <div class="modal-body cm-bd-body">
                        <form>
                            {{ csrf_field() }}
                            <input type="hidden" name="property_id" id="wishlist-property-id" value="">
                            <input type="hidden" name="before_discount" id="item_before_discount" value="">
                            <input type="hidden" name="total_price" id="item_total_price" value="">
                            <input type="hidden" name="property_type_name" id="item_property_type_name" value="">
                            <input type="hidden" name="city_name" id="item_city_name" value="">
                            <input type="hidden" name="host_id" id="item_host_id" value="">
                            <input type="hidden" name="day_price" id="item_day_price" value="">
                            <input type="hidden" name="number_of_days" id="item_number_of_days" value="">
                            <input type="hidden" name="property_code" id="item_property_code" value="">

                            {{-- {{dd($wishlistExist)}} --}}
                            @forelse ($wishlistExist as $wishlist)
                                <div class="wishlist-listing">
                                    @php
                                        $wishlistProperties = $wishlist->wishlistProperties;
                                    @endphp
                                    @if (count($wishlistProperties) > 0)
                                        <div class="wlisting-inner" id="existWishlist"
                                             data-wishlist-name-id="{{ $wishlist->id }}">
                                            <img
                                                src="{{ asset($wishlistProperties[0]->property?->getCoverPhotoAttribute()) }}"
                                                alt="">
                                            <h5 class="mb-0 ellipsis-oneline">{{ $wishlist->name }}</h5>
                                        </div>
                                    @else
                                        <div class="wlisting-inner" id="existWishlist"
                                             data-wishlist-name-id="{{ $wishlist->id }}">
                                            <img src="{{ asset('images/default-image-not-exist.png') }}" alt="">
                                            <h5 class="mb-0 ellipsis-oneline">{{ $wishlist->name }}</h5>
                                        </div>
                                    @endif
                                </div>
                            @empty
                                <p class="mb-0 text-center">To save your favorite places
                                    and Experiences to a wishlist.
                                </p>
                            @endforelse
                        </form>
                    </div>
                    <div class="modal-footer cm-bd-footer">
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal"
                                data-bs-target="#create-whishlist">{{ customTrans('wishlist.create_new_wishlist') }}</button>
                    </div>
                </div>
            </div>
        </div>

        
    </section>

    <!-- share modal start-->
    <div class="modal fade" id="single-share-modal" data-bs-backdrop="static"
            data-bs-keyboard="false" tabindex="-1" aria-labelledby="singleShareModalLabel" aria-hidden="true">
            <div class="modal-dialog custom-modal-dialog modal-dialog-centered">
                <div class="modal-content custom-modal-content">
                    <div class="modal-header custom-small-modal-header">
                        <button type="button" class="btn-close" data-bs-dismiss="modal"
                        aria-label="Close">
                        </button>
                    </div>
                    <div class="modal-body">
                        <h4 class="mb-4">
                            {{ customTrans('property_single.share_this_place') }}
                        </h4>
                        <div class="single-share-property">
                        <!-- Image tag will be here -->
                        <img src="" id="share-property-image">
                        <div class="ss-property-content">
                                <p class="mb-0" id="share-property-title"></p>
                            </div>
                        </div>
                        <input type="hidden" id="share-property-url" value="{{ url()->current() }}">
                        <div class="ss-property-btns">
                            <div class="row">
                                <div class="col-md-6">
                                    <button class="transparent-btn" id="copy-link-btn">
                                        <i class="far fa-copy"></i>
                                        {{ customTrans('property_single.copy_link') }}
                                    </button>
                                </div>
                                <div class="col-md-6">
                                    <button class="transparent-btn" id="email-share-btn">
                                        <i class="far fa-envelope"></i>
                                        {{ customTrans('property_single.email') }}
                                    </button>
                                </div>
                                <div class="col-md-6">
                                    <button class="transparent-btn" id="whatsapp-share-btn">
                                        <i class="fab fa-whatsapp"></i>
                                        {{ customTrans('property_single.whatsApp') }}
                                    </button>
                                </div>
                                <div class="col-md-6">
                                    <button class="transparent-btn" id="messenger-share-btn">
                                        <i class="fab fa-facebook-messenger"></i>
                                        {{ customTrans('property_single.messenger') }}
                                    </button>
                                </div>
                                <div class="col-md-6">
                                    <button class="transparent-btn" id="facebook-share-btn">
                                        <i class="fab fa-facebook"></i>
                                        {{ customTrans('property_single.facebook') }}
                                    </button>
                                </div>
                                <div class="col-md-6">
                                    <button class="transparent-btn" id="twitter-share-btn">
                                    <img src="{{asset('icons/twitter.svg')}}" alt="">
                                        {{ customTrans('property_single.twitter') }}
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div id="share-tooltip" class="tooltip-container" style="position: fixed; bottom: 20px; left: 50%; transform: translateX(-50%); z-index: 9999; display: none; opacity: 0; transition: opacity 0.3s ease-in-out;">
                            <div class="tooltip-content" style="background-color: rgba(0, 0, 0, 0.8); color: white; padding: 10px 20px; border-radius: 5px; font-size: 14px; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);">
                                <i class="fas fa-check-circle" style="margin-right: 5px;"></i> <span id="tooltip-message">Link copied to clipboard successfully!</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
    </div>
    <!-- share modal end -->

@stop

@push('jslibs')
    <script type="text/javascript" src="{{ asset('js/bootstrap-slider.min.js') }}"></script>
    <script
        src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-select/1.14.0-beta2/js/bootstrap-select.min.js"></script>
@endpush
@push('scripts')
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.lazyload/1.9.1/jquery.lazyload.min.js" integrity="sha512-jNDtFf7qgU0eH/+Z42FG4fw3w7DM/9zbgNPe3wfJlCylVDTT3IgKW5r92Vy9IHa6U50vyMz5gRByIu4YIXFtaQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>

    <script type="text/javascript">

        $(document).ready(function() {
            $('#filter-search-form').submit(function(e) {
                e.preventDefault();
                const urlParams = new URLSearchParams(window.location.search);
                const formData = $(this).serializeArray();
                formData.forEach(item => {
                    urlParams.set(item.name, item.value);
                });

                const baseUrl = window.location.pathname;
                const newUrl = baseUrl + '?' + urlParams.toString();
                window.location.href = newUrl;
            });

            function saveRecord(url = null) {
                fetch("{{ route('update.visits') }}", {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        device: 'web',
                        url: url
                    })
                })
                .then(res => res.json())
                .then(data => {
                    console.log(data);
                    if (data.record && data.status == 'success') {
                        console.log('success');
                    } else {
                        console.error('Something went wrong');
                    }
                });
            }
             let newUrl = window.location.href;
             saveRecord(newUrl);
        });

        var loadPage = "{{ route('search.resultV2') }}"

        function updateQueryParams(param, value) {
                // get current search
                const search = document.location.search.replace('?', '')
                // update it
                let chunks = search.split('&')
                if (!chunks.find((chunk) => chunk.startsWith(param + '='))) {
                    chunks.push(param + '=')
                }

                chunks = chunks.filter((item) => !!item).map((chunk) => {
                    return chunk.startsWith(param + '=') ? (param + '=' + value) : chunk
                })

                // glow everything to one string
                chunks = chunks.join('&')
                // push to history
                window.history.pushState({}, '', '?' + chunks)
            }
        $(document).ready(function () {
            localStorage.removeItem('selectDistrictArray')
            var markers = []
            var allowRefresh = true
            var currentInfoWindow = null
            var sortBy = (new URLSearchParams(window.location.search)).get('sort') ?? 'nearestToCity'
            var rb = (new URLSearchParams(window.location.search)).get('rb') || 'Any'
            var rbtwo = (new URLSearchParams(window.location.search)).get('rbtwo') || 'Any'
            var rbthree = (new URLSearchParams(window.location.search)).get('rbthree') || 'Any'
            var d = (new URLSearchParams(window.location.search)).get('d') || ''
            var pc = (new URLSearchParams(window.location.search)).get('pc') || ''
            var a = (new URLSearchParams(window.location.search)).get('a') || ''
            let currentRequest = null;



            function setMapOnAll(map) {
                for (var i = 0; i < markers.length; i++) {
                    markers[i].setMap(map)
                }
            }

            function clearMarkers() {
                setMapOnAll(null)
            }

            function deleteMarkers() {
                clearMarkers()
                markers = []
            }

            var finished = false
            var isLoading = false
            var page = 1
            var upcomingresult = 0

            function getPropertiesV2(onpageload = false,get_count = false) {
                if (!loadPage) {
                    return
                }
                if(get_count){
                    $('.search_loader').removeClass('d-none');
                    $("#search_text").addClass('d-none');
                }
                let range = (new URLSearchParams(window.location.search)).get('range') || '';
                let d = (new URLSearchParams(window.location.search)).get('d') || null;
                let a = (new URLSearchParams(window.location.search)).get('a') || null;
                if (a) {
                    const inputs = a.split(',')


                    for (const input of inputs) {
                        $('input[name="amenities1[]"][value="' + input + '"]').prop('checked', true)


                    }
                }
                let property_type_id = (new URLSearchParams(window.location.search)).get('property_type') || null;

                let guestParam = (new URLSearchParams(window.location.search)).get('guest') || null;
                let sortBy = (new URLSearchParams(window.location.search)).get('sort') || 'nearestToCity';

                if(property_type_id){
                    $(`#property_type_${property_type_id}`).prop('checked', true);
                    $(`#property_type${property_type_id}`).prop('checked', true);
                }

                if (range === '') {
                    range = $('#price-range').attr('data-value')
                }

                const range_parts = range.split(',')
                var location = $('#front-search-field').val()

                $('#header-search-form').val(location)
                var searchNights = '{{ $searchNights }}'
                var min_price = range_parts[0]
                var max_price = range_parts[1]
                $('#minPrice').html(min_price)
                $('#maxPrice').html(max_price)

                var amenities = getCheckedValueArray('amenities')


                var property_type = getCheckedValueArray('property_type')
                var book_type = getCheckedValueArray('book_type')
                var space_type = getCheckedValueArray('space_type')
                var bedrooms = $('input[name="rb-fillter"]:checked').val()
                var beds = $('input[name="rb-fillter-two"]:checked').val()
                var bathrooms = $('input[name="rb-fillter-three"]:checked').val()
                var checkin = $('#startDate').val()
                var checkout = $('#endDate').val()
                var guest = $('#front-search-guests').val()
                var adult_guest = $('input[name="adult_guest"]').val()
                var child_guest = $('input[name="child_guest"]').val()
                var property_code = $('#property_code').val()
                var districts = $('select[name="districts"]').val()

                if (onpageload && d != null) {
                    districts = d.split(',')
                }
                let selectDistrictArray = []
                if ($('#more_filters').css('display') != 'none') {
                    if (currentRequest && currentRequest.readyState !== 4) {
                        currentRequest.abort();
                    }
                    currentRequest  = $.ajax({
                        url: loadPage,
                        cache: false,
                        type: 'post',
                        data: {
                            '_token': "{{ csrf_token() }}",
                            'location': location,
                            'min_price': min_price,
                            'max_price': max_price,
                            'amenities': amenities,
                            'property_type': property_type,
                            'book_type': book_type,
                            'space_type': space_type,
                            'beds': beds,
                            'bathrooms': bathrooms,
                            'bedrooms': bedrooms,
                            'checkin': checkin,
                            'checkout': checkout,
                            'searchNights': searchNights,
                            'guest': guest,
                            'adult_guest': adult_guest,
                            'child_guest': child_guest,
                            'sortBy': sortBy,
                            'property_code': property_code,
                            'districts': districts,
                            'page': page,
                            'get_count' : get_count
                        },
                        dataType: 'json',
                        beforeSend: function () {
                            $('.property_types_image').prop('onclick', null).off('click')
                            $('.listing-checkbox-input').prop('disabled', true)
                            isLoading = true // Set loading to true before making the AJAX request
                            show_loader()
                        },
                        success: function (result) {
                            // ----------This Block is specific for Multi Select listing in filter modal -> Start ----------
                            if(get_count){
                                $("#search_text").html(result.search_text);
                                $('.search_loader').addClass('d-none');
                                if(result.current_count <= 0){
                                    $('#search_text').prop('disabled', true);
                                } else {
                                    $('#search_text').prop('disabled', false);

                                }
                                $("#search_text").removeClass('d-none');
                                
                                return true;
                            }
                            if (result.districts.length > 0) {
                                // Handle the Districts data here
                                const selectDistrict = document.getElementById('district-picker')
                                selectDistrict.onchange = function () {
                                    selectDistrictArray = Array.from(selectDistrict
                                        .selectedOptions).map(option => option.value)
                                    updateQueryParams('d', selectDistrictArray)
                                    localStorage.setItem('selectDistrictArray', JSON.stringify(
                                        selectDistrictArray))
                                }
                                selectDistrict.innerHTML = ''
                                let storedDistricts = JSON.parse(localStorage.getItem(
                                    'selectDistrictArray'))
                                var d = new URLSearchParams(window.location.search).get('d');
                                if (d != null) {
                                    storedDistricts = d.split(',');
                                }

                                var parts = ((new URLSearchParams(window.location.search)).get('range') || '').split(',')
                                var minPrice = parts[0] || {{ $min_price }};
                                var maxPrice = parts[1] || {{ $max_price }};
                                $('#min-price').val(minPrice);
                                $('#max-price').val(maxPrice);

                                if (storedDistricts !== null) {
                                    // If Data exists in localStorage
                                    result.districts.forEach(district => {
                                        const option = document.createElement('option')
                                        option.value = district
                                        option.textContent = district
                                        if (storedDistricts.includes(district)) {
                                            option.selected = true
                                        }
                                        selectDistrict.appendChild(option)
                                    })
                                    $(selectDistrict).selectpicker('refresh')
                                } else {
                                    // If Data doesnot exists in localStorage
                                    result.districts.forEach(district => {
                                        const option = document.createElement('option')
                                        option.value = district
                                        option.textContent = district
                                        selectDistrict.appendChild(option)
                                    })
                                    $(selectDistrict).selectpicker('refresh')
                                }
                            }
                            // ----------This Block is specific for Multi Select listing in filter modal -> Start ----------


                            allowRefresh = false
                            isLoading = false

                            if (result.pagedata.total) {
                                upcomingresult = result.current_count

                                $('#properties_show').append(result
                                    .proerties_html) // proerties_html -- "ajax-result.blade.php"
                                $('.listing-checkbox-input').prop('disabled', false)
                                $('.property_types_image').prop('onclick', false)
                                popOverFunction()
                                propertyCardSlider()
                                if (upcomingresult < 8 && result.pagedata.last_page == 1) {
                                    finished = true //turn off more loading
                                }

                                if (result.pagedata.current_page == result.pagedata.last_page) {
                                    page = 1
                                } else {
                                    page += 1
                                }

                            } else {
                                finished = true //turn off more loading
                                $('#properties_show').append(result
                                    .proerties_html) // proerties_html -- "ajax-result.blade.php"
                                page = 1
                            }
                            $('.lazy-loading').lazyload();
                            setTimeout(() => {
                                $('.loadskull').removeClass('loadskull')
                                var lazyloadImages = document.querySelectorAll('img.lazy')
                                var lazyloadThrottleTimeout

                                function lazyload() {
                                    if (lazyloadThrottleTimeout) {
                                        clearTimeout(lazyloadThrottleTimeout)
                                    }

                                    lazyloadThrottleTimeout = setTimeout(function () {
                                        var scrollTop = window.pageYOffset
                                        lazyloadImages.forEach(function (img) {
                                            if (img.offsetTop < (window
                                                    .innerHeight + scrollTop
                                            )) {
                                                img.src = img.dataset.src
                                                img.classList.remove(
                                                    'lazy')
                                            }
                                        })
                                        if (lazyloadImages.length == 8) {
                                            document.removeEventListener('scroll',
                                                lazyload)
                                            window.removeEventListener('resize',
                                                lazyload)
                                            window.removeEventListener(
                                                'orientationChange', lazyload)

                                        }
                                    }, 20)
                                }

                                lazyload()
                            }, 500)

                            $('.property_types_image').prop('onclick', true).on('click', function (
                                e) {
                                e.preventDefault()
                                let ele = $(this)
                                onClickPropertyFilter(ele)
                            })
                        },
                        error: function (request, error) {
                            allowRefresh = false
                            isLoading = false

                            $('.property_types_image').prop('onclick', true).on('click', function (
                                e) {
                                e.preventDefault()
                                let ele = $(this)
                                onClickPropertyFilter(ele)
                            })
                        },
                        complete: function () {
                            isLoading = false
                            hide_loader()
                            document.documentElement.style.overflow = 'auto'
                            $(window).scroll(function () {
                                var scrollTop = $(this).scrollTop()
                                var windowHeight = $(this).height()
                                if (scrollTop + windowHeight >= $(document).height() - 50)
                                    if (!isLoading)
                                        if (!finished) getPropertiesV2()
                                windowHeight = scrollTop
                            })

                        }
                    })
                }
            }
            const selectors = '.amenity-sync, .property-type-radio, .rb-radio-button, .editable-rang, .district-slt-picker, #price-range';
            $(document).on('click change', selectors, function () {
                // Wait a tick to let existing handlers run first
                setTimeout(() => {
                $('input[name="property_type[]"]').prop('checked', false);
                getPropertiesV2(false,true);
                }, 10); // slight delay to ensure previous handlers complete
            });
            $('.rst-btn').on('click', function () {
                $('#property_code').val('');
                $('#min-price').val(1);
                $('#max-price').val('4000+');
                $('input[name="rb-fillter"]:first').prop('checked', true);
                $('input[name="rb-fillter-two"]:first').prop('checked', true);
                $('input[name="rb-fillter-three"]:first').prop('checked', true);

                $('.cust-form-check-input').prop('checked', false);
                // $(".slider-handle:first")
                //     .attr('aria-valuemin', '1')
                //     .attr('aria-valuemax', '4000')
                //     .css('left', '0');
                // $(".slider-handle:last")
                //     .attr('aria-valuemin', '100')
                //     .attr('aria-valuemax', '4000')
                //     .css('left', '100%');
                $('#price-range').slider('setValue', [1, 5000]);
                $('#district-picker').val('').selectpicker('refresh');
                $('.fltr-chk-btn').prop('disabled', false)
                if ($('.filter-error').length) {
                    $('.filter-error').remove();
                }
                // update query paramerts
                updateQueryParams('property_type', '')
                updateQueryParams('sort', '')
                updateQueryParams('a', '')
                updateQueryParams('pc', '')
                updateQueryParams('d', '')
                updateQueryParams('rbthree', '')
                updateQueryParams('rbtwo', '')
                updateQueryParams('rb', '')
                updateQueryParams('range', '1,5000')
                $("#search_text").html("{{  customTrans('filter.show_results_count', ['num' => '1000+'])}}")
                $('#search_text').prop('disabled', false);
            });

            allowRefresh = true

            setTimeout(function () {
                getPropertiesV2(true)
            }, 1000)

            $('#header-search-form').on('change', function () {
                allowRefresh = true
                clearSearchData()
                // getProperties($('#map_view').locationpicker('map').map);
                getPropertiesV2()
            })

            function clearSearchData() {
                deleteMarkers()
                $('#properties_show').html('')
                finished = false
                page = 1
            }

            function onClickPropertyFilter(ele) {
                let url = "{{route('get.property.type', ':property_type_id')}}"
                let cb = ele.find('.listing-checkbox-input')
                $('.listing-checkbox-input').not(cb).prop('checked', false);
                // change page Url
                updateQueryParams('property_type', cb.val())

                $.ajax({
                    url: url.replace(':property_type_id', cb.val()),
                    type: 'get',
                    success: function (data) {
                        try {
                            //-----------WebEngage Integration------------(Verified)
                            let user = DEFAULT_USER
                            // let comment = DEFAULT_USER;
                            let authcheck = '{{ auth()->check() }}'
                            if (authcheck) {
                                // user_id = "{{ Auth::id() }}";
                                @auth
                                var isHost = @json(auth()->user()->is_host);
                                @endauth
                                    user = isHost == true ? 'Host' : DEFAULT_USER
                            }
                            payload = {
                                'Category Name': data.name,
                                'User': user
                            }
                            webEngageTracking(CATEGORY_SELECTED, payload)
                            //-----------WebEngage Integration------------
                        } catch (error) {
                            // Handle any errors that occur within the try block
                            console.error('Error:', error)
                        }
                    },
                    error: function (error) {
                        // Handle any errors that occur with the AJAX request
                        console.error('AJAX Error:', error)
                    }
                })
                cb.prop('checked', !cb.prop('checked'))
                clearSearchData()
                // getProperties($('#map_view').locationpicker('map').map);
                getPropertiesV2()
            }

            $('.property_types_image').on('click', function (e) {
                e.preventDefault()
                let ele = $(this)
                onClickPropertyFilter(ele)
            })

            $('.priceFilter').on('click', function () {
                sortBy = $(this).data('value')
                updateQueryParams('sort', sortBy)
                clearSearchData()
                // getProperties($('#map_view').locationpicker('map').map);
                getPropertiesV2()
            })

            $('#btnBook, #btnRoom, #btnPrice, .filter-apply').on('click', function () {
                event.preventDefault()

                let checkin = new Date($('#startDate').val())
                let checkout = new Date($('#endDate').val())

                // trackEvent('search', {
                //     search_term: $('#front-search-field').val(),
                //     search_month: checkin.toLocaleString('default', { month: 'long' }),
                //     search_duration: Math.round((checkout.getTime() - checkin.getTime()) / (1000 * 3600 * 24)),
                //     email: '{{ auth()->user()?->email }}',
                //     phone: '{{ auth()->user()?->phone }}'
                // }, 'ga')

                // trackEvent('Search', {
                //     contents: [],
                //     currency: '{{Session::get('currency')}}',
                //     query: $('#front-search-field').val(),
                //     search_month: checkin.toLocaleString('default', { month: 'long' }),
                //     search_duration: Math.round((checkout.getTime() - checkin.getTime()) / (1000 * 3600 * 24)),
                //     email: '{{ auth()->user()?->email }}',
                //     phone: '{{ auth()->user()?->phone }}'
                // }, 'tik')

                // trackEvent('SEARCH', {
                //     search_month: checkin.toLocaleString('default', { month: 'long' }),
                //     search_duration: Math.round((checkout.getTime() - checkin.getTime()) / (1000 * 3600 * 24)),
                //     item_category: 'product',
                //     search_string: $('#front-search-field').val(),
                //     user_email: '{{ auth()->user()?->email }}',
                //     user_phone_number: '{{ auth()->user()?->phone }}'
                // }, ['snap'])
                $('input[name="property_type[]"]').prop('checked', false);
                clearSearchData()
                // getProperties($('#map_view').locationpicker('map').map);
                getPropertiesV2()
                // $('.room_filter').addClass('d-none');
                $('#filter').modal('hide')
                // $('#more_filters').show();
                $('.dropdown-menu-price').removeClass('show')
            })

            // function getCheckedValueArray(field_name) {
            //     var array_Value = ''
            //     array_Value = $('input[name="' + field_name + '[]"]:checked').map(function () {
            //         return this.value
            //     })
            //         .get()
            //         .join(',')

            //     return array_Value
            // }
            function getCheckedValueArray(field_name) {
                let values = $('input[name="' + field_name + '[]"]:checked')
                    .map(function () {
                        return this.value;
                    })
                    .get();

                // Filter unique values using Set
                let uniqueValues = [...new Set(values)];

                return uniqueValues.join(',');
            }

            document.addEventListener("change", function (e) {
            if (e.target.classList.contains("amenity-sync")) {
                const value = e.target.value;
                const isChecked = e.target.checked;

                // Sync all checkboxes with the same value
                document.querySelectorAll(`.amenity-sync[value="${value}"]`).forEach(cb => {
                    cb.checked = isChecked;
                });
                updateQueryParams('a', getCheckedValueArray('amenities1'))
            }
        });

            $(document.body).on('click', '#map_view', function () {
                allowRefresh = true
                clearSearchData()
                getPropertiesV2()
            })

            $('#map_view').locationpicker({
                location: {
                    latitude: {{ "$lat" }},
                    longitude: {{ "$long" }}
                    // latitude: 24.8607,
                    // longitude: 67.0011
                },
                radius: 0,
                zoom: {{ "$zoom" }},
                addressFormat: '',
                markerVisible: false,
                markerInCenter: false,
                inputBinding: {
                    latitudeInput: $('#latitude'),
                    longitudeInput: $('#longitude'),
                    locationNameInput: $('#address_line_1')
                },
                enableAutocomplete: true,
                draggable: true,
                onclick: function (currentLocation, radius, isMarkerDropped) {
                    if (allowRefresh == true) {
                        //    getProperties($(this).locationpicker('map').map);
                    }
                },

                oninitialized: function (component) {
                    var addressComponents = $(component).locationpicker('map').location
                        .addressComponents
                }
            })

            $('.slider-selection').trigger('click')

            function show_loader() {
                $('#loader-more').removeClass('d-none')
                $('#pagination').hide()
            }

            function hide_loader() {
                $('#loader-more').addClass('d-none')
                $('#pagination').show()
            }

            // Map show
            $('#showMap').on('click', function () {
                $('#mapCol').removeClass('v-none')
                $('.closeMap').addClass('d-none')
                $('.showMap').addClass('d-none')
                $('.aft-map').addClass('d-none')
                $('.show-grid').removeClass('d-none')
                $('.fl-container').removeClass('container')
                $('.fl-container').addClass('container-fluid')

                allowRefresh = true
                clearSearchData()

                getPropertiesV2()
            })
            $('.show-grid').on('click', function () {
                $('.grid').addClass('col-6')
                $('.cstm-property').addClass('ongrid')
                $('.aft-map').removeClass('d-none')
                $('.show-grid').addClass('d-none')
                $('.closeMap').removeClass('d-none')

                allowRefresh = true
                clearSearchData()

                getPropertiesV2()

            })

            // Map Close
            $('#closeMap').on('click', function () {
                $('#mapCol').addClass('v-none')
                $('#showMap').removeClass('d-none')
                $('#closeMap').addClass('d-none')
                $('.property').removeClass('d-none')
                $('.grid').removeClass('col-6')
                $('.cstm-property').removeClass('ongrid')
                $('.fl-container').addClass('container')
                $('.fl-container').removeClass('container-fluid')


                allowRefresh = true
                clearSearchData()
                // getProperties($('#map_view').locationpicker('map').map);
                getPropertiesV2()

            })

            $(document).on('click', '.dropdown-menu-price', function (e) {
                e.stopPropagation()
            })

            @auth $(document).on('click', '.book_mark_change', function (event) {
                event.preventDefault()
                var property_id = $(this).data('id')
                var property_status = $(this).data('status')
                var user_id = "{{ Auth::id() }}"
                var dataURL = APP_URL + '/add-edit-book-mark'
                var that = this
                if (property_status == '1') {
                    var title = "{{ customTrans('favourite.remove') }}"

                } else {

                    var title = "{{ customTrans('favourite.add') }}"
                }

                swal({
                    title: title,
                    icon: 'warning',
                    buttons: {
                        cancel: {
                            text: "{{ customTrans('general.no') }}",
                            value: null,
                            visible: true,
                            className: 'btn btn-outline-danger text-16 font-weight-700 pt-3 pb-3 pl-5 pr-5',
                            closeModal: true
                        },
                        confirm: {
                            text: "{{ customTrans('general.yes') }}",
                            value: true,
                            visible: true,
                            className: 'btn vbtn-outline-success text-16 font-weight-700 pl-5 pr-5 pt-3 pb-3 pl-5 pr-5',
                            closeModal: true
                        }
                    },
                    dangerMode: true
                }).then((willDelete) => {
                    if (willDelete) {
                        $.ajax({
                            url: dataURL,
                            data: {
                                '_token': "{{ csrf_token() }}",
                                'id': property_id,
                                'user_id': user_id
                            },
                            type: 'post',
                            dataType: 'json',
                            success: function (data) {
                                $(that).removeData('status')
                                if (data.favourite.status == 'Active') {
                                    $(that).css('color', '#fcb23e')
                                    $(that).attr('data-status', 1)
                                    swal('success',
                                        '{{ customTrans('success.favourite_add_success') }}'
                                    ).then(function () {
                                        location.reload()
                                    })
                                } else {
                                    $(that).css('color', 'black')
                                    $(that).attr('data-status', 0)
                                    swal('success',
                                        '{{ customTrans('success.favourite_remove_success') }}'
                                    ).then(function () {
                                        location.reload()
                                    })
                                }
                            }
                        })
                    }
                })
            })
            @endauth

            $('#closeMap').addClass('d-none')

            if (a) {
                const inputs = a.split(',')

                for (const input of inputs) {

                    // $('input[name="amenities[]"][value="' + input + '"]').prop('checked', true)
                    $('#amenities'+input).prop('checked', true)
                }
            }


            $('input[name=\'rb-fillter\'][value=' + rb + ']').prop('checked', true)
            $('input[name=\'rb-fillter-two\'][value=' + rbtwo + ']').prop('checked', true)
            $('input[name=\'rb-fillter-three\'][value=' + rbthree + ']').prop('checked', true)
            $('select[name="d"]').value = d
            $('input[name="property_code"]').value = pc

            $('input[name="rb-fillter"]').on('change', (e) => {
                updateQueryParams('rb', e.target.value)
            })

            $('input[name="rb-fillter-two"]').on('change', (e) => {
                updateQueryParams('rbtwo', e.target.value)
            })

            $('input[name="rb-fillter-three"]').on('change', (e) => {
                updateQueryParams('rbthree', e.target.value)
            })

            $('select[name="districts"]').on('change', (e) => {
                updateQueryParams('d', e.target.value)
            })

            $('select[name="property_code"]').on('change', (e) => {
                updateQueryParams('pc', e.target.value)
            })

            $('input[name="amenities[]"]').on('change', () => {


                updateQueryParams('a', getCheckedValueArray('amenities'))
            })

            var parts = ((new URLSearchParams(window.location.search)).get('range') || '').split(',')
            var minPrice = parts[0] || {{ $min_price }};
            var maxPrice = parts[1] || {{ $max_price }};

            var aboveValue = 5000
            var priceRangeSlider = $('#price-range').slider({
                min: 5,
                max: aboveValue,
                values: [Number(minPrice), Number(maxPrice)],
                tooltip: 'hide'
            })

            priceRangeSlider.on('change', function (event) {
                updateQueryParams('range', event.target.dataset.value > 4000 ? event.target.dataset.value + '+' : event.target.dataset.value)
            })

            function updateSliderAndInputs() {
                var minPriceInput = isNaN(parseInt($('#min-price').val())) ? 1 : parseInt($('#min-price').val());
                var maxPriceInput = isNaN(parseInt($('#max-price').val())) ? 5000 : parseInt($('#max-price').val());
                if (minPriceInput >= 4000) {
                    $('#min-price').val(4000 + '+')
                }
                if (maxPriceInput >= 4000) {
                    $('#max-price').val(4000 + '+')
                }

                updateQueryParams('range', minPriceInput + ',' + maxPriceInput)
                priceRangeSlider.slider('setValue', [minPriceInput, maxPriceInput])
            }

            function validatePrice(){
                var minPriceInput = parseInt($('#min-price').val().replace('+', ''));
                var maxPriceInput = parseInt($('#max-price').val().replace('+', ''));
                var $errorContainer = $('.fltr-rang');
                var $submitButton = $('.fltr-chk-btn');

                // Clear previous errors
                $errorContainer.find('.error-msg').remove();

                var errors = [];

                // Validations
                if (isNaN(minPriceInput)) {
                    errors.push("{{ customTrans('filter.min_error') }}");
                }
                if (isNaN(maxPriceInput)) {
                    errors.push("{{ customTrans('filter.max_error') }}");
                }
                if (!isNaN(minPriceInput) && minPriceInput < 0) {
                    errors.push("{{ customTrans('filter.max_error_negative') }}");
                }
                if (!isNaN(maxPriceInput) && maxPriceInput < 0) {
                    errors.push("{{ customTrans('filter.min_error_negative') }}");
                }
                if (!isNaN(minPriceInput) && !isNaN(maxPriceInput) && minPriceInput > maxPriceInput) {
                    errors.push("{{ customTrans('filter.min_error_greater') }}");
                }

                // Append errors if any
                if (errors.length > 0) {
                    errors.forEach(function(msg) {
                        $errorContainer.append(`<div class="error-msg filter-error" style="color: red; font-size: 14px;text-align:center; margin-top: 10px;">${msg}</div>`);
                    });
                    $submitButton.prop('disabled', true);
                    return false;
                } else {
                    $submitButton.prop('disabled', false);
                    return true;
                }
            }

            $('#min-price, #max-price').on('input', function () {
                let price = validatePrice();
                if(price){
                    updateSliderAndInputs()
                }
            })

            priceRangeSlider.on('slide', function (slideEvt) {
                var minPrice = slideEvt.value[0]
                var maxPrice = slideEvt.value[1]
                if (minPrice >= 4000) {
                    $('#min-price').val(4000 + '+')
                } else {
                    $('#min-price').val(minPrice)
                }
                if (maxPrice >= 4000) {
                    $('#max-price').val(4000 + '+')
                } else {
                    $('#max-price').val(maxPrice)
                }
            })

            function setInputFieldsFromSlider() {
                var sliderValues = priceRangeSlider.slider('getValue')
                if (sliderValues[0] >= 4000) {
                    $('#min-price').val(4000 + '+')
                } else {
                    $('#min-price').val(sliderValues[0])
                }
                if (sliderValues[1] >= 4000) {
                    $('#max-price').val(4000 + '+')
                } else {
                    $('#max-price').val(sliderValues[1])
                }
            }

            setInputFieldsFromSlider()

            // dropdown overlay
            $(document).on('click', '.fav-icon', function (e) {

                var itemId = $(this).data('item-id')
                const elem = this
                // Set the item ID as the value of the hidden input field in the modal
                $('#item_id').val(itemId)
                $('#item_before_discount').val(elem.dataset.beforeDiscount);
                $('#item_total_price').val(elem.dataset.totalPrice);
                $('#item_property_type_name').val(elem.dataset.propertyTypeName);
                $('#item_city_name').val(elem.dataset.cityName);
                $('#item_host_id').val(elem.dataset.hostId);
                $('#item_day_price').val(elem.dataset.dayPrice);
                $('#item_number_of_days').val(elem.dataset.numberOfDays);
                $('#item_property_code').val(elem.dataset.propertyCode);
                $('#wishlist-property-id').val(itemId)

            })

            // Clear the hidden input field value when the modal is closed
            $('#create-whishlist').on('hidden.bs.modal', function () {
                $('#item_id').val('')
                $('#wishlist-property-id').val('')
            })
            $('#whishlist-listing').on('hidden.bs.modal', function () {
                $('#wishlist-property-id').val('')
            })
            $('#priceFilter').on('click', function () {
                if ($(this).hasClass('show')) {
                    $('.overlay').show()
                } else {
                    $('.overlay').hide()
                }
            })

            $(document).on('click', function (event) {
                var target = $(event.target)
                if (!target.closest('#priceFilter').length) {
                    $('.overlay').hide()
                }
            })
        })

        // Capture the click event on the heart icon
        $(document).on('click', '#wishlistBtn', function (e) {
            e.preventDefault()
            $(this).addClass('disabled')

            var name = $('#name').val()
            var property_id = $('#item_id').val()
            var token = $('input[name="_token"]').val()
            $.ajax({
                type: 'POST',
                url: "{{ route('createWishlist') }}",
                data: {
                    '_token': token,
                    'name': name,
                    'property_id': property_id
                },
                success: function (data) {
                    if (data == 'Success') {

                        // trackEvent('add_to_wishlist', {
                        //     value: Number.parseFloat($('#item_day_price').val()).toFixed(2),
                        //     currency: '{{Session::get('currency')}}',
                        //     discount: Number.parseFloat($('#item_before_discount').val() - $('#item_total_price').val()).toFixed(2),
                        //     item_type: $('#item_property_type_name').val(),
                        //     item_city_name: $('#item_city_name').val(),
                        //     item_host_id: $('#item_host_id').val(),
                        //     price: Number.parseFloat($('#item_day_price').val()).toFixed(2),
                        //     quantity: $('#item_number_of_days').val(),
                        //     total_price: Number.parseFloat($('#item_total_price').val()).toFixed(2),
                        // }, 'ga')

                        // trackEvent('AddToWishlist', {
                        //     content_ids: [$('#item_id').val()],
                        //     contents: [
                        //         {
                        //             'content_id': $('#item_id').val(),
                        //             'content_name': name,
                        //             'content_type': 'product'
                        //         }
                        //     ],
                        //     currency: '{{Session::get('currency')}}',
                        //     value:  $('#item_total_price').val(),
                        // }, ['tik'])

                        // trackEvent('ADD_TO_WISHLIST', {
                        //     item_ids: [$('#item_property_code').val()],
                        //     item_category: 'product',
                        //     number_items: $('#item_number_of_days').val(),
                        //     price: Number.parseFloat($('#item_total_price').val()).toFixed(2),
                        //     currency: '{{Session::get('currency')}}',
                        //     user_email: '{{ auth()->user()?->email }}',
                        //     user_phone_number: '{{ auth()->user()?->phone }}'
                        // }, ['snap'])

                        $('#create-whishlist').modal('hide')
                        $('#success').modal('show')
                        // window.location.reload();
                    }

                }
            })
        })
        $(document).on('click', '.successmodalbtn', function (e) {
            window.location.reload(true)
        })
        $(document).on('click', '#existWishlist', function (e) {
            e.preventDefault()
            $(this).addClass('disabled')
            var property_id = $('#wishlist-property-id').val()
            var wishlist_name_id = $(this).data('wishlist-name-id')
            var token = $('input[name="_token"]').val()
            $.ajax({
                type: 'POST',
                url: "{{ route('addRemoveWishlist') }}",
                data: {
                    '_token': token,
                    'wishlist_name_id': wishlist_name_id,
                    'property_id': property_id
                },
                success: function (data) {
                    if (data.msg == 'Success') {
                        $('#whishlist-listing').modal('hide')
                        //-----------WebEngage Integration------------(Verified)
                        let user = DEFAULT_USER
                        // let comment = DEFAULT_USER;
                        let authcheck = '{{ auth()->check() }}'
                        if (authcheck) {
                            // user_id = "{{ Auth::id() }}";
                            @auth
                            var isHost = @json(auth()->user()->is_host);
                            @endauth
                                user = isHost == true ? 'Host' : DEFAULT_USER
                        }

                        payload = {
                            'Name': data.property.name,
                            'Unit Code': data.property.property_code,
                            'Cost Per Night': data.property.property_price.price,
                            'Category Name': data.property.property_type.name,
                            'User': user
                        }
                        webEngageTracking(MARK_AS_WISHLIST, payload)
                        //-----------WebEngage Integration------------
                        window.location.reload()

                    }

                }
            })
        })
        $(document).on('click','.share-property-btn',function(e){
            e.preventDefault()
            var propertyTypeName = $(this).data('property-type-name');
            var cityName = $(this).data('city-name');
            var bedrooms = $(this).data('bedrooms');
            var bathrooms = $(this).data('bathrooms');
            var beds = $(this).data('beds');
            var rating = $(this).data('rating');
            var image = $(this).data('image');
            let propertyTitleShareModal = propertyTypeName + ' in ' + cityName + ' · ' + rating + bedrooms + ' bedroom · ' + beds + ' bed · ' + bathrooms + ' bath';
            // console.log(propertyTitleShareModal);
            $('#share-property-image').attr('src', image);
            $('#share-property-title').text(propertyTitleShareModal);
        })  

        $(document).on('click', '#toggleWishlist', function (e) {
            let authcheck = '{{ auth()->check() }}'
            if (authcheck) {
                e.preventDefault()
                $(this).addClass('disabled')
                // var property_id = $('#wishlist-property-id').val();
                var user_id = "{{ Auth::id() }}"
                var property_id = $(this).data('item-id')
                var $icon = $(this)
                var token = $('input[name="_token"]').val()

                $.ajax({    
                    type: 'POST',
                    url: "{{ route('toggleWishlist') }}",
                    data: {
                        '_token': token,
                        'user_id': user_id,
                        'property_id': property_id
                    },
                    success: function (data) {
                        if (data.msg == 'Success') {
                            $icon.removeAttr('id')
                            $icon.find('.heart_icon').removeClass('active')
                            $icon.find('.heart_icon').attr('data-status', 0)
                            $icon.attr('data-bs-target',
                                "{{ isset($wishlistExist) ? '#whishlist-listing' : '#create-whishlist' }}"
                            )
                            $icon.removeClass('disabled')
                            // window.location.reload();
                        }

                    }
                })
            }


        })
        $(document).on('click', '#guestWishlist', function () {
            $('#staticBackdrop').modal('show')
            // window.location.href = "{{ url('/?login') }}";
        })

        $(document).ready(function () {
            $('#city_select').on('change', function () {
                var selectedCity = $(this).val()
                $('#front-search-field').val(selectedCity)
            })

            function showTooltip(message) {
                $('#tooltip-message').text(message);
                var tooltip = $('#share-tooltip');
                tooltip.css('display', 'block');

                // Use setTimeout to ensure the display property is applied before changing opacity
                setTimeout(function() {
                    tooltip.css('opacity', '1');

                    // Hide tooltip after 2 seconds
                    setTimeout(function() {
                        tooltip.css('opacity', '0');

                        // Hide element after fade out animation completes
                        setTimeout(function() {
                            tooltip.css('display', 'none');
                        }, 300);
                    }, 2000);
                }, 10);
            }

             // Copy link functionality
            $('#copy-link-btn').on('click', function() {
                var propertyUrl = $('#share-property-url').val();
                navigator.clipboard.writeText(propertyUrl).then(function() {
                    showTooltip('Link copied to clipboard successfully!');
                });
            });

            // Email share functionality
            $('#email-share-btn').on('click', function() {
                var propertyUrl = $('#share-property-url').val();
                var propertyName = $('#share-property-title').val();
                var subject = "Check out this property: " + propertyName;
                var body = "I found this amazing property on Darent: " + propertyUrl;
                showTooltip('Opening email client...');
                setTimeout(function() {
                    window.location.href = "mailto:?subject=" + encodeURIComponent(subject) + "&body=" + encodeURIComponent(body);
                }, 500);
            });

            // WhatsApp share functionality
            $('#whatsapp-share-btn').on('click', function() {
                var propertyUrl = $('#share-property-url').val();
                var propertyName = $('#share-property-title').val();
                var text = "Check out this property on Darent: " + propertyName + " " + propertyUrl;
                showTooltip('Opening WhatsApp...');
                window.open('https://wa.me/?text=' + encodeURIComponent(text), '_blank');
            });

            // Facebook share functionality
            $('#facebook-share-btn').on('click', function() {
                var propertyUrl = $('#share-property-url').val();
                showTooltip('Opening Facebook...');
                window.open('https://www.facebook.com/sharer/sharer.php?u=' + encodeURIComponent(propertyUrl), '_blank', 'width=600,height=400');
            });

            // Twitter share functionality
            $('#twitter-share-btn').on('click', function() {
                var propertyUrl = $('#share-property-url').val();
                var propertyName = $('#share-property-title').val();
                var text = "Check out this property on Darent: " + propertyName;
                showTooltip('Opening Twitter...');
                window.open('https://twitter.com/intent/tweet?text=' + encodeURIComponent(text) + '&url=' + encodeURIComponent(propertyUrl), '_blank', 'width=600,height=400');
            });

            // Facebook Messenger share functionality
            $('#messenger-share-btn').on('click', function() {
                var propertyUrl = $('#share-property-url').val();
                showTooltip('Opening Messenger...');
                window.open('https://www.facebook.com/dialog/send?link=' + encodeURIComponent(propertyUrl) + '&app_id=966920033870&redirect_uri=' + encodeURIComponent(propertyUrl), '_blank', 'width=600,height=400');
            });
        })

        function handlePropertyClick(productContainer) {
            const elem = productContainer.find('.go-to-single');
            
            if (elem.length === 0) {
                console.error('Product element not found');
                return;
            }

            try {
                // Track Google Analytics event
                // trackEvent('select_item', {
                //     item_id: elem.data('id'),
                //     affiliation: '',
                //     discount: Number.parseFloat(elem.data('beforeDiscount') - elem.data('totalPrice')).toFixed(2),
                //     index: elem.data('index'),
                //     item_type: elem.data('propertyTypeName'),
                //     item_city_name: elem.data('cityName'),
                //     item_host_id: elem.data('hostId'),
                //     price: Number.parseFloat(elem.data('dayPrice')).toFixed(2),
                //     quantity: elem.data('numberOfDays'),
                //     total_price: Number.parseFloat(elem.data('totalPrice')).toFixed(2),
                // }, 'ga');

                // // Track TikTok event
                // trackEvent('SelectItem', {
                //     index: elem.data('index'),
                //     content_id: elem.data('propertyCode'),
                //     content_type: 'product',
                //     quantity: elem.data('numberOfDays'),
                //     currency: '{{Session::get('currency')}}',
                //     value: elem.data('totalPrice'),
                // }, ['tik']);

                // Construct and open URL
                const baseUrl = 'properties';
                const slug = elem.data('slug');
                const params = new URLSearchParams({
                    checkin: '{{Session::get('header_checkin')}}',
                    checkout: '{{Session::get('header_checkout')}}'
                });
                
                const url = `${baseUrl}/${slug}?${params.toString()}`;
                window.open(url, '_blank');
            } catch (error) {
                console.error('Error handling property click:', error);
            }
        }

        // Handle clicks on the product container
        $(document).on('click', '.product', function(e) {
            // Prevent handling if click is on child interactive elements
            if ($(e.target).closest('.fav-icon, .swiper-button-next, .swiper-button-prev, .swiper-pagination').length) {
                return;
            }
            
            e.preventDefault();
            handlePropertyClick($(this));
        });

        // Handle clicks on swiper slides
        $(document).on('click', '.swiper-slide a', function(e) {
            e.preventDefault();
            const productContainer = $(this).closest('.product');
            handlePropertyClick(productContainer);
        });
    


    </script>
    <script>



    const appLocale = "{{ app()->getLocale() }}";
    const assetBaseUrl = "{{ asset('icons') }}";
    const amenitiesSelected = @json($amenities_selected);


    document.addEventListener("DOMContentLoaded", function () {
        const container = document.getElementById("recommended-amenities");

        function loadAmenities(propertyTypeId) {
            updateQueryParams('property_type', propertyTypeId)

            const url = `/get-amenities/${propertyTypeId}`;
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    container.innerHTML = ''; // Clear previous

                    if (data.length === 0) {
                        container.innerHTML = '<div class="col-12">No amenities found.</div>';
                        return;
                    }

                    data.forEach(function (amenity) {
                        const checked = amenitiesSelected.includes(amenity.id) ? 'checked' : '';
                        const name = appLocale === 'ar' ? amenity.title_ar : amenity.title;
                        const imageUrl = amenity.icon_image ? `${assetBaseUrl}/${amenity.icon_image}` : 'https://img.icons8.com/emoji/48/question-mark.png';
                        const html = `
                            <div class="col-3 px-2">
                                <input type="checkbox" value="${amenity.id}" name="amenities1[]" id="amenity-${amenity.id}" class="cust-form-check-input recommended-checkbox amenity-sync" ${checked}>
                                <label for="amenity-${amenity.id}" class="recommended-label" >
                                    <img src="${imageUrl}">
                                </label>
                                <p class="recommended-content">${name}</p>
                            </div>
                        `;
                        container.insertAdjacentHTML('beforeend', html);
                    });
                })
                .catch(error => {
                    console.error("Error fetching amenities:", error);
                });
        }


        document.querySelectorAll(".property-type-radio").forEach(function (radio) {
            radio.addEventListener("change", function () {
                $('input[name="amenities[]"]').prop('checked', false);
                $('input[name="amenities1[]"]').prop('checked', false);
                loadAmenities(this.value);
            });
        });


        const selectedRadio = document.querySelector(".property-type-radio:checked");
        if (selectedRadio) {
            loadAmenities(selectedRadio.value);
        }
    });

       
</script>

    @if (isset($TypeName))
        <script>
            let unit_event = {!! json_encode($TypeName) !!};
        </script>
    @endif
@endpush
