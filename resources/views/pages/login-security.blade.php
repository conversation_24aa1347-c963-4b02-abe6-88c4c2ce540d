@extends('template')
@push('css')
    <link rel="stylesheet" type="text/css" href="{{ asset('css/bootstrap-datetimepicker.css') }}">
@endpush
@section('main')
    <section class="list">
        <div class="container">
            <div class="list-hd">
                <div class="row">
                    <div class="col-md-6">
                        <h3>{{ customTrans('header.account') }}</h3>
                    </div>

                </div>
            </div>
            <div class="list-manage">
                <div class="row">
                    @include('common.account_sidenav')
                    <div class="col-xl-9 col-lg-9 col-sm-9 col-xs-12">
                        <div class="side-list">
                            <div class="account-side">
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="account-detail">
                                            <div class="acc-inner">
                                                <div class="log-in">
                                                    <ul>
                                                        <li>
                                                            <div class="row align-items-center mb-3">
                                                                <div class="col-2">
                                                                    <div class="ls-icon-main">
                                                                        <img src="{{ asset('icons/message.svg') }}"
                                                                            class="ls-icon">
                                                                        <img src="{{ asset('images/verify-icon.png') }}"
                                                                            class="circle-check">
                                                                    </div>
                                                                </div>
                                                                <div class="col-10">
                                                                    <div class="ac-dt">
                                                                        <p class="fs-16">
                                                                            {{ customTrans('users_profile.verified_email') }}
                                                                        </p>
                                                                        <h5 class="fc-black" data-name="email">
                                                                            {{ isset(auth()->user()->email) ? auth()->user()->email : '' }}
                                                                        </h5>
                                                                    </div>
                                                                    <div class="ac-act">
                                                                        <a href="javascript:;" class="d-none otp-sv emailSubmit" data-section="email">{{ customTrans('users_profile.save') }}</a>
                                                                        <a href="#" class="fc-black"
                                                                            onclick="editField('email', '{{ $user->email }}', event)">{{ customTrans('header.edit') }}</a>
                                                                    </div>
                                                                    <div id="emailError" class="em-err"></div>
                                                                </div>

                                                            </div>
                                                        </li>
                                                        <li>
                                                            <div class="row align-items-center mb-3">
                                                                <div class="col-2">
                                                                    <div class="ls-icon-main">
                                                                        <img src="{{ asset('icons/mobile.svg') }}"
                                                                            class="ls-icon ls-icon-mobile">
                                                                        <img src="{{ asset('images/verify-icon.png') }}"
                                                                            class="circle-check">
                                                                    </div>
                                                                </div>
                                                                <div class="col-10">
                                                                    <div class="ac-dt">
                                                                        <p class="fs-16 ">
                                                                            {{ customTrans('users_profile.verified_phone_number') }}
                                                                        </p>
                                                                        <h5 class="fc-black" data-name="formatted_phone">
                                                                            <select class="phone-slt" disabled>
                                                                                <option value="+966" selected>
                                                                                    {{ app()->getLocale() == 'ar' ? digitsToArabic('+966') : '+966' }}
                                                                                </option>
                                                                            </select>
                                                                            {{ app()->getLocale() == 'ar' ? digitsToArabic(str_replace('+966', '', auth()->user()->phone)) : str_replace('+966', '', auth()->user()->phone) }}
                                                                        </h5>
                                                                        <!-- Error Container -->
                                                                        <div id="user-formatted_phoneError"></div>
                                                                    </div>
                                                                </div>
                                                                <div class="col-2 text-right">
                                                                    <div class="ac-act">
                                                                        <a href="javascript:;" class="d-none otp-sv phoneSubmit" data-section="formatted-phone">{{ customTrans('users_profile.save') }}</a>

                                                                        <a href="#" class="fc-black"
                                                                            onclick="editField('formatted_phone', '{{ str_replace('+966', '', $user->phone) }}', event)">{{ customTrans('header.edit') }}</a>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </li>
                                                        <li>
                                                            <div class="row align-items-center mb-3">
                                                                <div class="col-2">
                                                                    <div class="ls-icon-main">
                                                                        <img src="{{ asset('icons/yaqeen_logo.jpg') }}"
                                                                            class="ls-icon">
                                                                        @if (!$is_verified)
                                                                            <img src="{{ asset('images/icons8-cross-24.png') }}"
                                                                                class="circle-check">
                                                                        @else
                                                                            <img src="{{ asset('images/verify-icon.png') }}"
                                                                                class="circle-check">
                                                                        @endif

                                                                    </div>
                                                                </div>
                                                                <div class="col-10">
                                                                    <div class="ac-dt">
                                                                        <p class="fs-16">
                                                                            {{ customTrans('users_profile.elm_verification') }}
                                                                        </p>

                                                                    </div>
                                                                    <div class="ac-act">
                                                                        @php
                                                                            $yaqeen_param = request()->input('is_yaqeen');
                                                                        @endphp
                                                                        @if (!$is_verified)
                                                                            <a href="javascript:;" data-bs-toggle="modal"
                                                                                data-bs-target="#ilmyakeen">
                                                                                <button class="theme-btn">
                                                                                    {{ customTrans('general.verify_elm') }}
                                                                                </button>
                                                                            </a>
                                                                        @else
                                                                            <span class="text-success">
                                                                                {{ customTrans('users_profile.verified') }}
                                                                            </span>
                                                                        @endif


                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
@stop

@push('scripts')
    <style>
        .dz-image img {
            width: 120px;
            height: 120px;
        }
    </style>

    <link rel="stylesheet" href="https://unpkg.com/dropzone@5/dist/min/dropzone.min.css" type="text/css" />
    <script src="https://unpkg.com/dropzone@5/dist/min/dropzone.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/moment-hijri@2.1.2/moment-hijri.min.js"></script>
    <script src="{{ asset('js/bootstrap-hijri-datetimepicker.js') }}"></script>
    <script>
        $(document).ready(function() {
            var isYaqeen = @json($yaqeen_param);
            if (isYaqeen && isYaqeen == "true" && !is_verified) {
                console.log('yakeen true');
                $("#ilmyakeen").modal('show');
            } else {
                console.log('yakeen fale');
            }
        });
        $(document).on('click', '.SendOtp', function() {
            let email = $('#email').val();
            const emailNow = $("#hiddenEmailValue").val();
            if (!email) {
                return
            }
            if (email.trim().toLowerCase() == emailNow.trim().toLowerCase()) {
                const $container = $(this).closest('.cont-host');
                $container.find('.hs-view').show();
                $container.find('.hs-view-edit').hide();
                $('.hs-edit').show();
                return
            }
            $("#hiddenEmailValue").val(email);
            $.ajax({
                url: "{{ route('changeemail') }}",
                type: 'GET',
                data: {
                    "email": email
                },
                success: function(response) {
                    $('.SendOtp').addClass('d-none');
                    $('#emailOtp').removeClass('d-none');
                    // $('.otp-sv').removeClass('d-none');
                    $('#email').addClass('d-none');
                    $('.emailSubmit').removeClass('d-none');

                },
                error: function(xhr, status, error) {
                    var errorContainer = $('#emailError');
                    errorContainer.empty(); // Clear previous errors
                    if (xhr.responseJSON && xhr.responseJSON.message && xhr.responseJSON.message
                        .email) {
                        const emailErrors = xhr.responseJSON.message.email;
                        errorContainer.append('<p class="text-danger">' +
                            emailErrors[0] +
                            '</p>');
                    } else {
                        errorContainer.append('<p class="text-danger">' +
                            xhr.responseJSON.message +
                            '</p>');
                    }

                }
            });
            // if (isset($request->email)) {
            //     SendOtpEmailJob::dispatch($user->email, $otp)->onQueue('emails');
            // }

        });
        $(document).on('click', '.SendPhoneOtp', function() {
            let formattedPhone = $('#user-formatted-phone').val();
            const formattedPhoneNow = $("#hiddenFormattedPhoneValue").val();
            const PhoneCode = '+966';
            if (!formattedPhone) {
                return
            }
            if (formattedPhone == formattedPhoneNow) {
                const $container = $(this).closest('.cont-host');
                $container.find('.hs-view').show();
                $container.find('.hs-view-edit').hide();
                $('.hs-edit').show();
                return
            }
            $("#hiddenFormattedPhoneValue").val(formattedPhone);
            $.ajax({
                url: "{{ route('changePhone') }}",
                type: 'GET',
                data: {
                    "phone": formattedPhone,
                    "code" : PhoneCode
                },
                success: function(response) {
                    $('.SendPhoneOtp').addClass('d-none');
                    $('#phoneOtp').removeClass('d-none');
                    $('#user-formatted-phone').addClass('d-none');
                    $('.phoneSubmit').removeClass('d-none');

                },
                error: function(xhr, status, error) {
                    // console.log(xhr);
                    var errorContainer = $('#user-formatted_phoneError');
                    errorContainer.empty(); // Clear previous errors
                    if (xhr.responseJSON && xhr.responseJSON.message && xhr.responseJSON.message
                        .phone) {
                        const phoneErrors = xhr.responseJSON.message.phone;
                        errorContainer.append('<p class="text-danger">' +
                            phoneErrors[0] +
                            '</p>');
                    } else {
                        errorContainer.append('<p class="text-danger">' +
                            xhr.responseJSON.message +
                            '</p>');
                    }

                }
            });

        });

        $(document).on('click', '.emailSubmit', function() {
            let otpValue = $('#emailOtp').val();
            let email = $('#hiddenEmailValue').val();
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });

            $.ajax({
                url: "{{ route('verifyEmailChangeOtp') }}",
                type: 'GET',
                data: {
                    'otp': otpValue,
                    'email': email
                },
                success: function(data) {
                    // $('#enteremailotp').modal('hide');
                    window.location.reload(true);
                    // window.location.href = "{{ route('home') }}"
                },
                error: function(xhr, status, error) {
                    var errorContainer = $('#emailError');
                    errorContainer.empty(); // Clear previous errors
                    if (xhr.responseJSON && xhr.responseJSON.message && (xhr.responseJSON.message
                            .email || xhr.responseJSON.message.otp)) {
                        const emailErrors = xhr.responseJSON.message.email;
                        const otpErrors = xhr.responseJSON.message.otp;
                        if (emailErrors != undefined) {
                            errorContainer.append('<p class="text-danger">' +
                                emailErrors[0] +
                                '</p>');
                        }
                        // if (otpErrors != undefined) {
                        //     errorContainer.append('<p class="text-danger">' +
                        //         otpErrors[0] +
                        //         '</p>');
                        // }
                    } else {
                        errorContainer.append('<p class="text-danger">' +
                            xhr.responseJSON.message +
                            '</p>');
                    }


                }
            });
        });

        $(document).on('click', '.phoneSubmit', function() {
            let otpValue = $('#phoneOtp').val();
            let phone = $('#hiddenFormattedPhoneValue').val();
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });

            $.ajax({
                url: "{{ route('verifyPhoneChangeOtp') }}",
                type: 'GET',
                data: {
                    'otp': otpValue,
                    'phone': '+966'+phone,
                },
                success: function(data) {
                    window.location.reload(true);
                },
                error: function(xhr, status, error) {
                    var errorContainer = $('#user-formatted_phoneError');
                    errorContainer.empty(); // Clear previous errors
                    if (xhr.responseJSON && xhr.responseJSON.message && (xhr.responseJSON.message
                            .phone || xhr.responseJSON.message.otp)) {
                        const phoneErrors = xhr.responseJSON.message.phone;
                        const otpErrors = xhr.responseJSON.message.otp;
                        if (phoneErrors != undefined) {
                            errorContainer.append('<p class="text-danger">' +
                                phoneErrors[0] +
                                '</p>');
                        }
                        if (otpErrors != undefined) {
                            errorContainer.append('<p class="text-danger">' +
                                otpErrors[0] +
                                '</p>');
                        }
                    } else {
                        errorContainer.append('<p class="text-danger">' +
                            xhr.responseJSON.message +
                            '</p>');
                    }


                }
            });
        });

        Dropzone.options.dropzone = {
            acceptedFiles: ".jpeg,.jpg,.png,.pdf",
            thumbnailWidth: 120,
            thumbnailHeight: 120,
            addRemoveLinks: true,
            timeout: 50000,
            init: async function() {
                await getDocuments(this)
            },
            removedfile: function(file) {
                if (this.options.dictRemoveFile) {
                    return Dropzone.confirm("Are You Sure to " + this.options.dictRemoveFile, async function() {
                        if (file.previewElement.dataset.id) {
                            await removeDocument(file.previewElement.dataset.id)
                        }
                        var fileRef;
                        return (fileRef = file.previewElement) != null ?
                            fileRef.parentNode.removeChild(file.previewElement) : void 0;
                    });
                }
            },

            success: function(file, response) {
                file.previewElement.id = response.data.id;
                file.previewElement.setAttribute("data-id", response.data.id)
                const olddatadzname = file.previewElement.querySelector("[data-dz-name]");
                file.previewElement.querySelector("img").alt = response.data.name;
                olddatadzname.innerHTML = response.data.name;
            },
            error: function(file, response) {
                if (response == "You can't upload files of this type.") {
                    file.previewElement.classList.add("dz-error");
                    _ref = file.previewElement.querySelectorAll("[data-dz-errormessage]");
                    _results = [];

                    for (const errors of _ref.values(response)) {
                        _results.push(_ref.textContent = "errors");
                    }
                    return _results;
                }
            }

        }

        async function getDocuments(myDropzone) {
            try {
                const formData = new FormData()
                formData.append("_token", "{{ csrf_token() }}")
                const response = await fetch("{{ route('user.documents') }}", {
                    headers: {
                        'accept': 'application/json'
                    },
                    method: "POST",
                    body: formData
                });
                const data = await response.json();
                if (!response.ok) throw data;
                if (data.success) {
                    for (const document of data.documents) {
                        const file = {
                            id: document.id,
                            name: document.name,
                            size: document.size
                        };

                        myDropzone.options.addedfile.call(myDropzone, file);
                        var ext = document.name.split('.').pop();

                        if (ext == "pdf") {
                            var pdf = APP_URL + '/images/login-security/pdf.png';
                            myDropzone.options.thumbnail.call(myDropzone, file, pdf);
                        } else {
                            myDropzone.options.thumbnail.call(myDropzone, file, document.file);

                        }
                        myDropzone.emit("complete", file);
                        if (!!file.previewElement) {
                            file.previewElement.setAttribute("data-id", document.id)
                        }
                    }
                }

            } catch (errorRes) {
                console.log(errorRes);
            }
        }

        async function removeDocument(file_id) {
            try {
                const formData = new FormData()
                formData.append("_token", "{{ csrf_token() }}")
                url = "{{ route('user.documents.delete', 'file_id') }}"
                url = url.replace("file_id", file_id)
                const response = await fetch(url, {
                    headers: {
                        'accept': 'application/json'
                    },
                    method: "POST",
                    body: formData
                });
                const data = await response.json();
                removeErrors()
                if (!response.ok) throw data;
                if (data.success) {
                    alert(data.message);
                }

            } catch (errorRes) {
                console.log(errorRes);
            }
        }

        function editField(name, value, e = null) {
            if (!!e) {
                e.preventDefault();
            }
            txtEl = document.querySelector(`[data-name='${name}']`)
            if (!txtEl) {
                return
            }

            let inpEl = `<input type="text" id="user-${name.replace("_", "-")}" name="${name}" value="${value}"/>`

            // console.log(txtEl,inpEl);
            if (name == "formatted_phone") {
                inpEl =
                    `<select id="user-code" name="user_code" class="phone-slt phone-focus">
                            <option value="+966" selected>+966</option>
                        </select>
                        <input type="hidden" id="hiddenFormattedPhoneValue" name="formatted_phone"
                                                    value="${value}">
                       <input
    type="tel"
    pattern="\d{0,9}"
    maxlength="9"
    class="lst-lgin-input"
    id="user-${name.replace("_", "-")}"
    name="${name}"
    value="${value}"
    oninput="this.value = this.value.replace(/[^0-9]/g, '').slice(0,9);"
/>
<input type="number" maxlength="4"
                                                    class=" otp-input d-none" pattern="[0-9]*" id="phoneOtp"
                                                    name="phoneOtpField" autocomplete="on" placeholder="Enter Otp">`
            }
            if (name == "email") {
                inpEl = `<input type="hidden" id="hiddenEmailValue" name="email"
                                                    value="${value}">

                                                <input type="email"
                                                    value="${value}"
                                                    id="email" class="" placeholder="Your Email">
                                                <input type="number" maxlength="4"
                                                    class=" otp-input d-none" pattern="[0-9]*" id="emailOtp"
                                                    name="otpfield" autocomplete="on" placeholder="Enter Otp">`;

            }
            // else if (isotp) {
            //     inpEl =
            //         `<input type="text" maxlength="4" pattern="[0-9]{4}" data-email="${value}" id="user-email" name="otp" placeholder="Enter Otp">`
            // }
            // if (!after) {
            closeEditBtns()
            txtEl.classList.add("d-none")
            txtEl.insertAdjacentHTML("afterend", inpEl)
            if (name == "email") {
                e.target.insertAdjacentHTML("afterend",
                    `<a href="#" class="fc-black SendOtp">Change</a>`
                )
            }
            else if(name == "formatted_phone"){
                e.target.insertAdjacentHTML("afterend",
                    `<a href="#" class="fc-black SendPhoneOtp">Change</a>`
                )
            } else {
                e.target.insertAdjacentHTML("afterend",
                    `<a href="#" class="fc-black" onclick="saveField(event, '${name}')">{{ customTrans('users_profile.save') }}</a>
                        <a href="#" class="ml-2 fc-black" onclick="restoreField(event, '${name}')">{{ customTrans('host_dashboard.cancel') }}</a>`
                )
            }
            // } else {
            //     const oldEl = document.getElementById(`user-${name}`)
            //     if (!oldEl) return;
            //     oldEl.remove()
            // }
        }
        // function editField(e, name, value) {
        //     e.preventDefault();
        //     txtEl = document.querySelector(`[data-name='${name}']`)
        //     if (!txtEl) {
        //         return
        //     }

        //     let inpEl = `<input type="text" id="user-${name.replace("_","-")}" name="${name}" value="${value}" />`
        //     if (name == "formatted_phone") {
        //         inpEl =
        //             `<select id="user-code" name="user_code" class="phone-slt phone-focus">
    //                     <option value="+966" selected>+966</option>
    //                 </select>
    //                 <input type="number" class="lst-lgin-input" id="user-${name.replace("_","-")}" name="${name}" value="${value}" />`
        //     }
        //     closeEditBtns()
        //     txtEl.classList.add("d-none")
        //     txtEl.insertAdjacentHTML("afterend", inpEl)
        //     e.target.insertAdjacentHTML("afterend",
        //         `<a href="#" class="fc-black" onclick="saveField(event, '${name}')">{{ customTrans('users_profile.save') }}</a>
    //         <a href="#" class="ml-2 fc-black" onclick="restoreField(event, '${name}')">{{ customTrans('host_dashboard.cancel') }}</a>`
        //     )
        // }

        function closeEditBtns(close = true) {
            const editBtns = document.querySelectorAll("a.fc-black[onclick]")
            for (let i = 0; i < editBtns.length; i++) {
                if (close) {
                    editBtns[i].classList.add("d-none");
                } else {
                    editBtns[i].classList.remove("d-none");
                }
            }
        }

        function restoreField(e, name, cancel = true) {
            if (cancel) {
                e.preventDefault();
            }
            inpEl = document.querySelector(`[id='user-${name.replace("_", "-")}']`)
            if (!inpEl) {
                return
            }
            if (name == "formatted_phone") {
                document.getElementById("user-code").remove()
            }
            inpEl.parentElement.querySelector("h5.d-none")?.classList?.remove("d-none")
            inpEl.remove()
            e.target?.[!cancel ? "nextElementSibling" : "previousElementSibling"]?.remove()
            e.target.remove()
            closeEditBtns(false)
        }

        async function saveField(e, name) {
            e.preventDefault()
            inpEl = document.querySelector(`[id='user-${name.replace("_","-")}']`)
            if (!inpEl) {
                return
            }
            const isOtp = "email" in inpEl.dataset
            try {
                const formData = new FormData()
                formData.append("_token", "{{ csrf_token() }}")
                let url = !isOtp ? "{{ route('user.profile.save') }}" :
                    "{{ route('verifyEmailChangeOtp') }}"
                if (isOtp) {
                    url += ("?" + new URLSearchParams({
                        "email": inpEl.dataset.email,
                        "otp": inpEl.value
                    }))
                } else {
                    formData.append(name, inpEl.value)
                }
                const response = await fetch(url, {
                    headers: {
                        'accept': 'application/json'
                    },
                    method: !isOtp ? "POST" : "GET",
                    body: !isOtp ? formData : undefined
                });
                if (response.status == 204) {
                    restoreField(e, name, false)
                    return
                }
                const data = await response.json();
                removeErrors()
                if (!response.ok) throw data;
                if (name != "email" || isOtp) {
                    location.reload()
                } else {
                    editField(name, inpEl.value, null, true)
                }
            } catch (errorRes) {
                if (errorRes.hasOwnProperty("errors") || errorRes.status == 422) {
                    for (const [key, errors] of Object.entries(errorRes?.[isOtp ? 'message' : 'errors'])) {
                        setErrors("user-" + key, errors);
                    }
                } else {
                    console.log(errorRes);
                }
            }
        }




        document.addEventListener('DOMContentLoaded', function() {
            // Initialize variables
            let phoneInput = null;
            let countryCodeSelect = null;

            // Event delegation to capture dynamically added input fields
            document.addEventListener('input', function(e) {
                if (e.target && e.target.id === 'user-formatted-phone') {
                    phoneInput = e.target;
                    countryCodeSelect = document.getElementById('user-code');
                    validatePhoneNumber();
                }
            });

            // Event delegation for Save button
            document.addEventListener('click', function(e) {
                if (e.target.matches('a.fc-black[onclick^="saveField"]')) {
                    const name = e.target.getAttribute('onclick').match(/'([^']+)'/)[1];
                    if (name === 'formatted_phone') {
                        if (!validatePhoneNumber()) {
                            e.preventDefault();
                            return false;
                        }
                    }
                }
            });

            // Function to validate phone number
            function validatePhoneNumber() {
                const phoneNumber = phoneInput.value.trim();
                const countryCode = countryCodeSelect.value;

                // Remove existing error messages first
                removeErrors('user-formatted_phone');

                // Check if the field is empty
                if (!phoneNumber) {
                    setErrors('user-formatted_phone', ['Phone number is required.']);
                    return false;
                }

                // Regex pattern for Saudi Arabia mobile numbers (+966)
                const saudiPhoneRegex = /^[0-9]{9}$/;

                if (countryCode === '+966') {
                    if (!saudiPhoneRegex.test(phoneNumber)) {
                        // Display error message
                        setErrors('user-formatted_phone', ['Please enter a valid Saudi Arabia phone number.']);
                        return false;
                    } else {
                        // Remove any existing error messages
                        removeErrors('user-formatted_phone');
                        return true;
                    }
                }

                // Add validation for other country codes if necessary
                return true;
            }

            // Existing functions (editField, saveField, setErrors, removeErrors)...

            // Ensure these functions are defined as in the previous code
        });

    </script>
@endpush
