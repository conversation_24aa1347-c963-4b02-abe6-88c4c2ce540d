@extends('mobile.template')
@push('css')
    <link rel="stylesheet" href="{{ asset('css/fancy-box.css') }}">
    {{-- Add desktop daterangepicker CSS for booking functionality --}}
    <style>
        header {
            display: none !important;
        }
    </style>
@endpush

@php
    use App\Models\Settings;
    use App\Models\Properties;
    use App\Models\WishlistName;
    use Carbon\Carbon;

    $res_modal = true;
    $checkin_param = request()->get('checkin');
    $checkout_param = request()->get('checkout');

    // Validate and parse checkin date
    $checkin_session = ($checkin_param && $checkin_param !== 'Invalid date')
        ? Carbon::parse($checkin_param)
        : Carbon::now();

    // Validate and parse checkout date
    $checkout_session = ($checkout_param && $checkout_param !== 'Invalid date')
        ? Carbon::parse($checkout_param)
        : Carbon::now()->addDay();

    // Ensure checkout is not the same as check-in
    if ($checkin_session->equalTo($checkout_session)) {
        $checkout_session->addDay();
    }

    // Format the dates after comparison
    $checkin_session = $checkin_session->format('m/d/Y');
    $checkout_session = $checkout_session->format('m/d/Y');

    $child_guest_session = Session::get('child_guest_session');
    $adult_guest_session = Session::get('adult_guest_session');

    // IF SESSION CLEARED
    if (!$adult_guest_session) {
        $adult_guest_session = 1;
    }
    if (!$child_guest_session) {
        $child_guest_session = 0;
    }
    $wishlist_modal = true;

    $wishlistExist = WishlistName::where('creater_id', Auth::id())->get();
    if (isset($wishlistExist)) {
        $existWishlistModal = true;
        $wishlist_modal = true;
    } else {
        $existWishlistModal = false;
        $wishlist_modal = true;
    }

    $result = $property;

    function loadImage($path, $property_id) {

        if (is_null($path)) return '/images/default-image.png';

        if (str_starts_with($path, 'http'))
            return $path;

        if (str_contains($path, 'images/property'))
            return asset($path);

        $base = '/images/property';
        if (!is_null($property_id)) $base .= '/' . $property_id;
        return  asset($base . (str_starts_with($path, '/') ? $path : '/' . $path));
    }
@endphp

@section('main')
    <div class="property-gallery">
        <a href="javascript:void(0);" onclick="customGoBack(event)" class="floated-dt-back" id="goBack">
            <img src="{{ asset('icons/go-back.svg') }}" alt="">
        </a>
        <a href="javascript:" class="float-wishlist">
                        <div class="fav-icon" data-bs-toggle="modal"
                @auth
@if ($property->wishlist == true) id="toggleWishlist"
                 @else
                     data-bs-target="{{ isset($wishlistExist) ? '#whishlist-listing' : '#create-whishlist' }}"
                 @endif @endauth
                @guest
id="guestWishlist" @endguest data-item-id="{{ $property->id }}" alt="">

                <div class="fav-in {{ $property->wishlist == true ? 'active' : '' }}  heart_icon"
                    data-status="{{ $property->book_mark }}" data-id="{{ $property->id }}">
                    <img src="{{ asset('icons/dt-heart.svg') }}" alt="" class="fav-blank">
                    <img src="{{ asset('icons/dt-heart-fill.svg') }}" alt="" class="fav-fill">
                </div>
            </div>
        </a>
        <!-- Share Button -->
        <a href="javascript:" class="single-property-float-share" data-bs-toggle="modal" data-bs-target="#shareModal">
           <img src="{{asset('icons/share-icon.svg')}}" alt="Share Icon">
        </a>
        <div class="pg-slide">
            <div class="mb-slid2" id="pr-sld" data-locale="{{ app()->getLocale() }}">
                @for ($i = 0; $i < count($photos); $i++)
                    <div class="mb-sl-in">
                        <div class="inner-image">
                            <a data-fancybox="gallery-mobile"
                                href="{{ loadImage($photos[$i]->photo, property_id: $property->id) }}?t={{ time() }}">
                                <img src="{{ asset('images/loader.gif') }}"
                                    data-src="{{ loadImage($photos[$i]->photo, property_id: $property->id) }}?t={{ time() }}"
                                    class="inner-image image-show lazy" rel="album-2" alt="">
                            </a>
                        </div>
                    </div>
                @endfor
            </div>
            @if(isset($property->is_exclusive) && $property->is_exclusive)
                <span class="custom-tag exclusive-tag">{{ customTrans('property_single.exclusive') }}</span>
            @endif
            <span class="pagingInfo"></span>
        </div>
    </div>
    <div class="property-detail">
        @if (session('error'))
            <div class=" alert alert-success">
                {!! session('error') !!}
            </div>
        @endif
        <div class="container">
            <div class="head-content">
                <h4 class="fw-500 mb-2 fs-lg">{{ propertyTitleForListing($grid_property ?? $property) }}
                    @if ($property->propertyDiscount && $property->propertyDiscount->discount_percentage > 0)
                        <span class="inner-discount">{{ $property->propertyDiscount->discount_percentage }}%</span>
                    @endif
                </h4>
                <h5 class="mb-0 fw-400">{{ app()->getLocale() == 'ar' ? $property->name_ar : $property->name }}</h5>
                <p class="mb-0">
                    {{ isset($property->property_address->city) ? transStateCity($property->property_address->city, app()->getlocale()) . ', ' : '' }}
                    {{ isset($property->property_address->state) ? transStateCity($property->property_address->state, app()->getlocale()) . ', ' : '' }}
                    {{ isset($property->property_address->countries->name) ? (app()->getlocale() == 'ar' ? $property->property_address->countries->name_ar : $property->property_address->countries->name) : '' }}
                </p>
                <p class="unit-code d-flex m-0 p-0">
                    {{ customTrans('home.unit_code') }}(<span>{{ $property->property_code }}</span>)</p>
                <ul class="d-flex m-0 p-0">
                    <li>{{ digitsToArabic($property->bedrooms) . ' ' . trans('messages.property_single.bedroom') }},
                    </li>
                    <li>&nbsp;{{ digitsToArabic($property->beds) . ' ' . trans('messages.property_single.bed') }},
                    </li>
                    <li>&nbsp;
                        {{ digitsToArabic($property->adult_guest) . ' ' . trans('messages.property_single.guest') }},
                    </li>
                    <li>&nbsp;
                        {{ digitsToArabic($property->bathrooms) . ' ' . trans('messages.property_single.bathroom') }},
                    </li>
                </ul>
                @if($property->license_no)
                    <p class="display-license-num">
                        <img src="{{ asset('icons/verified.svg') }}" alt="">
                        {{ customTrans('listing.license_number') }} : <span> {{ $property->license_no }} </span>
                    </p>
                @endif
                <div class="property-inner-detail">
                    <div class="property-description">
                        @if($property->is_new_lable)
                            <p class="">
                                <span class="badge bg-danger px-2 py-1 shadow-1-strong mb-3 fs-6">{{ customTrans('host_dashboard.new') }}</span>
                            </p>
                        @else
                            <p class="product-rate">
                                <i class="ri-star-fill"></i>
                                <span class="ss-pr-inner">
                                    {{ $property->rating_avg ?? 0 }} /
                                    <span> 5 </span>
                                </span>
                            </p>
                        @endif
                    </div>
                </div>
            </div>

            {{-- overview --}}
            <div class="overview">
                <h4 class="fw-400">{{ customTrans('property_single.overveiw') }}</h4>
                <p>{{ $property->propertyDescription->summary ?? '' }}</p>
            </div>
            {{-- over view end --}}


            {{-- Booking Section for Mobile --}}
            @include('mobile.property.v0.partials.booking')

            {{-- Amenities start --}}
            <div class="">
                <div class="amenities property-feature-list">
                    <h4 class="fw-400">{{ customTrans('property_single.amenity') }}</h4>
                    <ul class="d-flex flex-wrap m-0 p-0">
                        @foreach ($amenities as $all_amenities)
                            @if ($all_amenities->status != null)
                                <li>
                                    <div class="am-icon">
                                        <img src="{{ asset('icons/' . $all_amenities->icon_image) }}" alt="">
                                    </div>
                                    <p>{{ app()->getLocale() == 'ar' ? $all_amenities->title_ar : $all_amenities->title }}
                                    </p>
                                </li>
                            @endif
                        @endforeach
                    </ul>
                </div>
                @if (isset($safety_amenities) && count($safety_amenities) > 0)
                    <div class="safety-feature property-feature-list">
                        <h4 class="fw-400">{{ customTrans('property_single.safety_feature') }}</h4>
                        <ul class="d-flex flex-wrap m-0 p-0">
                            @foreach ($safety_amenities as $row_safety)
                                @if ($row_safety->status != null)
                                    <li>
                                        <div class="am-icon">
                                            <img src="{{ asset('icons/' . $row_safety->icon_image) }}" alt="">
                                        </div>
                                        <p>{{ app()->getLocale() == 'ar' ? $row_safety->title_ar : $row_safety->title }}
                                        </p>
                                    </li>
                                @endif
                            @endforeach
                        </ul>
                    </div>
                @endif

                @if ($property->custom_amenities)
                    <div class="amenities property-feature-list">
                        <h4 class="fw-400">Custom Amenities</h4>
                        <ul class="cs-amt">
                            @foreach (json_decode($property->custom_amenities) ?? [] as $amenity)
                                <li>
                                    <p>{{ app()->getLocale() == 'ar' ? $amenity : $amenity }}</p>
                                </li>
                            @endforeach
                        </ul>
                    </div>
                @endif

                <div class="additional-info property-feature-list">
                    <h4 class="fw-400 mb-4">{{ customTrans('property_single.additional_information') }}</h4>
                    <div class="">
                        <div class="">
                            <h6 class="fw-400 mb-3">{{ customTrans('property_single.accommodation_rules') }}</h6>
                            <ul class="m-0 p-0">
                                <li>
                                    <div class="am-icon"><img src="{{ asset('icons/check-in.svg') }}" alt="">
                                    </div>
                                    <p>{{ customTrans('property_single.check_in') }}</p>
                                    <span class="mx-3 me-0">{{ date('h:i A', strtotime($property->checkinTime)) }}</span>
                                </li>
                                <li>
                                    <div class="am-icon"><img src="{{ asset('icons/check-out.svg') }}" alt="">
                                    </div>
                                    <p>{{ customTrans('property_single.check_out') }}</p>
                                    <span class="mx-3 me-0">{{ date('h:i A', strtotime($property->checkoutTime)) }}</span>
                                </li>

                                @if ($property->children_guest)
                                    <li class="align-items-baseline">
                                        <div class="am-icon"><img src="{{ asset('icons/for-child.svg') }}"
                                                alt="">
                                        </div>
                                        <p class="suitable">{{ customTrans('property_single.suitable_for_children') }}
                                        </p>
                                    </li>
                                @endif
                            </ul>
                        </div>
                        <div class="">
                            <h6 class="fw-400 mb-3">{{ customTrans('payment.house_rule') }}</h6>
                            <ul class="m-0 p-0">
                                @forelse ($house_rule_amenities as $row_house_rule)
                                    @if ($row_house_rule->status != null)
                                        <li>
                                            <div class="am-icon">
                                                <img src="{{ asset('icons/' . $row_house_rule->icon_image) }}"
                                                    alt="">
                                            </div>
                                            <p>{{ app()->getLocale() == 'ar' ? $row_house_rule->title_ar : $row_house_rule->title }}
                                            </p>
                                        </li>
                                    @endif
                                @empty
                                    <p>{{ customTrans('property_single.no_house_rule_available') }}</p>
                                @endforelse
                            </ul>
                        </div>
                        <div class="">
                            <h6 class="fw-400 mb-3">{{ customTrans('property_single.cancellations') }}</h6>
                            <ul class="m-0 p-0">
                                <li>
                                    <div class="am-icon"><img src="{{ asset('icons/moderate.svg') }}" alt="">
                                    </div>
                                    <span>
                                        @if (app()->getLocale() == 'ar')
                                            @switch($property->cancellation)
                                                @case('Flexible')
                                                    {{ 'مرن' }}
                                                @break

                                                @case('Moderate')
                                                    {{ 'معتدل' }}
                                                @break

                                                @case('Firm')
                                                    {{ 'حازم' }}
                                                @break

                                                @case('Strict')
                                                    {{ 'حازم' }}
                                                @break

                                                @default
                                            @endswitch
                                        @else
                                            {{ $property->cancellation }}
                                        @endif
                                    </span>
                                </li>
                                <li>
                                    <p>
                                        @if (app()->getLocale() == 'ar')
                                            @switch($property->cancellation)
                                                @case('Flexible')
                                                    {{ 'استرداد كامل المبلغ قبل يوم واحد من الوصول' }}
                                                @break

                                                @case('Moderate')
                                                    {{ 'رد المبلغ كاملاً قبل خمسة أيام من الوصول' }}
                                                @break

                                                @case('Firm')
                                                    {{ 'رد كامل للمبلغ المدفوع للإلغاءات التي تتم حتى قبل 30 يومًا من تاريخ الوصول. إذا تم الحجز قبل أقل من 30 يومًا من تسجيل الوصول، فسيتم استرداد المبلغ بالكامل للإلغاءات التي تتم في غضون 48 ساعة بعد إتمام الحجز وقبل 14 يومًا على الأقل من تسجيل الوصول. وبعد ذلك، يتم رد 50% من المبلغ المدفوع إذا تم الإلغاء حتى 7 أيام قبل تسجيل الوصول. ولن يتم رد المبالغ المدفوعة للإلغاءات التي تتم بعد ذلك.' }}
                                                @break

                                                @case('Strict')
                                                    {{ 'استرداد كامل للإلغاءات التي تمت في غضون 48 ساعة من الحجز ، إذا كان تاريخ تسجيل الوصول بعد 14 يومًا على الأقل. استرداد 50٪ للإلغاء الذي تم إجراؤه قبل 7 أيام على الأقل من تسجيل الوصول. لا يوجد استرداد لعمليات الإلغاء خلال 7 أيام من تسجيل الوصول' }}
                                                @break

                                                @default
                                            @endswitch
                                        @else
                                            @switch($property->cancellation)
                                                @case('Flexible')
                                                    {{ 'Full refund 1 day prior to arrival' }}
                                                @break

                                                @case('Moderate')
                                                    {{ 'Full refund 5 days prior to arrival' }}
                                                @break

                                                @case('Firm')
                                                    {{ 'Full refund for cancellations up to 30 days before check-in. If booked fewer than 30 days before check-in, a full refund for cancellations made within 48 hours of booking and at least 14 days before check-in. After that, a 50% refund up to 7 days before check-in. No refund after that.' }}
                                                @break

                                                @case('Strict')
                                                    {{ 'Full refund for cancellations made within 48 hours of booking, if the check-in date is at least 14 days away. 50% refund for cancellations made at least 7 days before check-in. No refunds for cancellations made within 7 days of check-in.' }}
                                                @break

                                                @default
                                            @endswitch
                                        @endif
                                    </p>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            {{-- Amenities end --}}

            {{-- Map Section --}}
            @include('mobile.property.v0.partials.map')

            {{-- Reviews Section --}}
            @include('mobile.property.v0.partials.reviews')

            {{-- Similar Properties Section --}}
            @include('mobile.property.v0.partials.similar-properties')


        </div>
    </div>



    {{-- Include V0 specific modals --}}
    {{-- @include('mobile.property.v0.partials.modals') --}}

    {{-- Map Modal --}}
    <div class="modal fade dubai-ff" id="getdirections" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
        <div class="modal-dialog custom-modal-dialog modal-dialog-centered">
            <div class="modal-content cm-simple-content">
                <div class="modal-header cm-simple-header">
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body cm-simple-body">
                    <div class="alert-modal">
                        <img src="{{ asset('icons/info-alert.svg') }}" alt="" class="mb-3">
                        <h3 class="mb-4 fw-600">{{ customTrans('footer.alert') }}</h3>
                        <p class="mb-4">{{ customTrans('footer.approximate_location') }}</p>
                        <a id="mapShow" href="#" target="_blank">
                            <button class="theme-btn w-100" data-bs-dismiss="modal" aria-label="Close">
                                {{ customTrans('host_reservation.confirm') }}
                            </button>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
@stop

@push('scripts')
    <script type="text/javascript" src="{{ asset('js/sweetalert.min.js') }}"></script>
    <script type="text/javascript" src="{{ asset('js/locationpicker.jquery.min.js') }}"></script>
    <script src="{{ asset('js/fancy-box.js') }}"></script>
    <!-- slick slider -->
    <script type="text/javascript" src="{{ asset('cdns/js/slick.min.js') }}"></script>
    <!-- slick slider end-->

    <style>
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }

        .skeleton-loader {
            position: relative;
            overflow: hidden;
        }

        .single-map-w {
            height: 300px;
            border-radius: 8px;
            overflow: hidden;
            position: relative;
        }

        .mp-loader {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 10;
        }

        .pin {
            width: 30px;
            height: 30px;
            border-radius: 50% 50% 50% 0;
            background: #329793;
            position: absolute;
            transform: rotate(-45deg);
            left: 50%;
            top: 50%;
            margin: -20px 0 0 -20px;
        }

        .pin:after {
            content: '';
            width: 14px;
            height: 14px;
            margin: 8px 0 0 8px;
            background: #fff;
            position: absolute;
            border-radius: 50%;
        }

        .pulse {
            background: rgba(50, 151, 147, 0.2);
            border-radius: 50%;
            height: 14px;
            width: 14px;
            position: absolute;
            left: 50%;
            top: 50%;
            margin: 11px 0px 0px -12px;
            transform: rotateX(55deg);
            z-index: -2;
        }

        .pulse:before {
            content: '';
            border-radius: 50%;
            height: 40px;
            width: 40px;
            position: absolute;
            margin: -13px 0 0 -13px;
            animation: pulsate 1s ease-out;
            animation-iteration-count: infinite;
            opacity: 0;
            box-shadow: 0 0 1px 2px #329793;
            animation-delay: 1.1s;
        }

        .pulse:after {
            content: '';
            border-radius: 50%;
            height: 40px;
            width: 40px;
            position: absolute;
            margin: -13px 0 0 -13px;
            animation: pulsate 1s ease-out;
            animation-iteration-count: infinite;
            opacity: 0;
            box-shadow: 0 0 1px 2px #329793;
            animation-delay: 1.3s;
        }

        @keyframes pulsate {
            0% {
                transform: scale(0.1, 0.1);
                opacity: 0;
            }
            50% {
                opacity: 1;
            }
            100% {
                transform: scale(1.2, 1.2);
                opacity: 0;
            }
        }

        .mobile-booking-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 12px;
            margin: 20px 0;
        }

        .mobile-booking-section .property-pricing {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
    </style>

    {{-- AJAX Loading Scripts --}}
    @include('mobile.property.v0.partials.ajax-scripts')

    {{-- Include Desktop Booking Scripts for pricing logic (adapted for mobile) --}}
    @include('property.v0.partials.js.booking-scripts')

    {{-- Mobile DateRangePicker Adapter Script --}}
    <script type="text/javascript">
        // Flag to prevent desktop daterangepicker initialization
        window.isMobileV0 = true;

        $(document).ready(function() {
            // Override the desktop daterangepicker initialization to work with mobile
            var unavailableDates = {!! json_encode($booked_dates ?? []) !!};
            var dates = [];
            for (var i = 0; i < unavailableDates.length; i++) {
                dates.push(unavailableDates[i].date);
            }

            // Parse initial dates from hidden inputs (MM/DD/YYYY format)
            var checkinValue = $('#url_checkin').val();
            var checkoutValue = $('#url_checkout').val();

            // Convert MM/DD/YYYY to moment objects for date picker
            var startDate = checkinValue ? moment(checkinValue, 'MM/DD/YYYY') : moment();
            var endDate = checkoutValue ? moment(checkoutValue, 'MM/DD/YYYY') : moment().add(1, 'day');

            // Ensure valid dates
            if (!startDate.isValid()) {
                startDate = moment();
            }
            if (!endDate.isValid() || endDate.isSameOrBefore(startDate)) {
                endDate = startDate.clone().add(1, 'day');
            }

            // Initialize mobile daterangepicker (using daterangepickers method)
            $('input[name="daterange"]').daterangepickers({
                opens: 'center',
                startDate: startDate,
                endDate: endDate,
                minDate: moment(),
                locale: {
                    monthNames: window.calendarContent ? window.calendarContent.monthNames :
                        ["يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو", "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"],
                    daysOfWeek: window.calendarContent ? window.calendarContent.daysOfWeek :
                        ["الأحد", "الاثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة", "السبت"],
                    applyLabel: window.calendarLabels ? window.calendarLabels.applyLabel : 'اختار',
                    cancelLabel: window.calendarLabels ? window.calendarLabels.cancelLabel : 'إلغاء'
                },
                isInvalidDate: function(date) {
                    return dates.indexOf(date.format('YYYY-MM-DD')) != -1;
                }
            }, function(start, end, label) {
                var guest = $('#number_of_guests').val();

                // Update hidden inputs with MM/DD/YYYY format (like original)
                $('#url_checkin').val(start.format('MM/DD/YYYY'));
                $('#url_checkout').val(end.format('MM/DD/YYYY'));

                // Update URL with DD-MM-YYYY format (like original)
                const newUrl = new URL(window.location.href);
                newUrl.searchParams.set('checkin', start.format('DD-MM-YYYY'));
                newUrl.searchParams.set('checkout', end.format('DD-MM-YYYY'));
                history.pushState(null, '', newUrl);

                // Send DD-MM-YYYY to API (like original)
                if (typeof price_calculation === 'function') {
                    price_calculation(start.format('DD-MM-YYYY'), end.format('DD-MM-YYYY'), guest);
                }
            });

            // Set initial display value for the date picker after initialization
            setTimeout(function() {
                var picker = $('input[name="daterange"]').data('daterangepickers');
                if (picker) {
                    // Display in MM/DD/YYYY format like the original
                    $('input[name="daterange"]').val(startDate.format('MM/DD/YYYY') + ' - ' + endDate.format('MM/DD/YYYY'));
                    $('#url_checkin').val(startDate.format('MM/DD/YYYY'));
                    $('#url_checkout').val(endDate.format('MM/DD/YYYY'));
                } else {
                    // Fallback if daterangepicker is not initialized yet
                    $('input[name="daterange"]').val(startDate.format('MM/DD/YYYY') + ' - ' + endDate.format('MM/DD/YYYY'));
                    $('#url_checkin').val(startDate.format('MM/DD/YYYY'));
                    $('#url_checkout').val(endDate.format('MM/DD/YYYY'));
                }
            }, 100);

            // Handle apply button for mobile daterangepicker
            $('input[name="daterange"]').on('apply.daterangepickers', function(ev, picker) {
                var startDate = picker.startDate.format('MM/DD/YYYY');
                var endDate = picker.endDate.format('MM/DD/YYYY');
                $('#url_checkin').val(startDate);
                $('#url_checkout').val(endDate);

                // Update property view
                if (typeof updatePropertyView === 'function') {
                    updatePropertyView(null, $('#url_checkin').val(), $('#url_checkout').val());
                }

                // Set session
                $.ajaxSetup({
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                });

                $.ajax({
                    url: "{{ route('session.v0.set') }}",
                    method: 'POST',
                    data: {
                        startDate: startDate,
                        endDate: endDate
                    },
                    success: function(response) {
                        setTimeout(() => {
                            if (typeof handleWalletPayChange === 'function') {
                                handleWalletPayChange();
                            }
                        }, 500);
                    },
                    error: function(xhr, status, error) {
                        console.error('Error setting session value:', error);
                    }
                });
            });

            // Lazy loading: Initialize pricing calculation only when booking section is visible
            function initializeMobilePricing() {
                var guest = $('#number_of_guests').val() || 1;
                var checkin = $('#url_checkin').val() || '';
                var checkout = $('#url_checkout').val() || '';

                // Set default dates if empty
                if (!checkin || checkin.trim() === '') {
                    checkin = moment().format('MM/DD/YYYY');
                    $('#url_checkin').val(checkin);
                }
                if (!checkout || checkout.trim() === '') {
                    checkout = moment().add(1, 'day').format('MM/DD/YYYY');
                    $('#url_checkout').val(checkout);
                }

                // Convert MM/DD/YYYY to DD-MM-YYYY for API
                var checkinForAPI = moment(checkin, 'MM/DD/YYYY').format('DD-MM-YYYY');
                var checkoutForAPI = moment(checkout, 'MM/DD/YYYY').format('DD-MM-YYYY');

                // Update URL with DD-MM-YYYY format
                try {
                    const newUrl = new URL(window.location.href);
                    newUrl.searchParams.set('checkin', checkinForAPI);
                    newUrl.searchParams.set('checkout', checkoutForAPI);
                    history.replaceState(null, '', newUrl);
                } catch (e) {
                    console.log('Could not update URL:', e);
                }

                // Run initial price calculation
                if (typeof price_calculation === 'function') {
                    price_calculation(checkinForAPI, checkoutForAPI, guest);
                }
            }

            // Set up Intersection Observer for lazy loading
            function setupLazyLoading() {
                const bookingSection = document.querySelector('.property-pricing');

                if (!bookingSection) {
                    // Fallback: if section not found, initialize immediately
                    console.log('Booking section not found, initializing immediately');
                    setTimeout(initializeMobilePricing, 1000);
                    return;
                }

                // Check if Intersection Observer is supported
                if ('IntersectionObserver' in window) {
                    const observer = new IntersectionObserver(function(entries, observer) {
                        entries.forEach(function(entry) {
                            if (entry.isIntersecting) {
                                console.log('Mobile booking section visible, initializing pricing');
                                // Initialize pricing when section becomes visible
                                setTimeout(initializeMobilePricing, 500);
                                // Stop observing after first intersection
                                observer.unobserve(entry.target);
                            }
                        });
                    }, {
                        root: null,
                        rootMargin: '50px', // Start loading 50px before section is visible
                        threshold: 0.1 // Trigger when 10% of the section is visible
                    });

                    observer.observe(bookingSection);
                } else {
                    // Fallback for browsers without Intersection Observer support
                    console.log('Intersection Observer not supported, using scroll fallback');
                    var bookingInitialized = false;

                    $(window).on('scroll', function() {
                        if (!bookingInitialized) {
                            var scrollTop = $(window).scrollTop();
                            var elementTop = $('.property-pricing').offset().top;
                            var elementHeight = $('.property-pricing').height();
                            var windowHeight = $(window).height();

                            // Check if booking section is in viewport (with 100px margin)
                            if (scrollTop + windowHeight >= elementTop - 100) {
                                console.log('Mobile booking section visible (scroll fallback), initializing pricing');
                                bookingInitialized = true;
                                setTimeout(initializeMobilePricing, 500);
                                $(window).off('scroll'); // Remove scroll listener
                            }
                        }
                    });
                }
            }

            // Setup lazy loading after a short delay to ensure DOM is ready
            setTimeout(setupLazyLoading, 100);
        });
    </script>

    {{-- Include Mobile Booking Scripts --}}
    {{-- @include('mobile.property.v0.partials.booking-scripts') --}}

    {{-- AJAX Scripts with Lazy Loading --}}
    @include('mobile.property.v0.partials.ajax-scripts')

    <script type="text/javascript">
        // Back Button Function
        function customGoBack() {
            const referrer = document.referrer;
            const currentHost = window.location.host;

            if (referrer) {
                const refUrl = new URL(referrer);
                const isSameHost = refUrl.host === currentHost;
                const isFromSummary = /\/(en|ar)?\/?summary\/[^/]+$/.test(refUrl.pathname);

                // if came from summary → go home instead of going back
                if (isSameHost && isFromSummary) {
                    window.location.href = '/';
                    return;
                }

                // Otherwise, go back if same domain
                if (isSameHost) {
                    window.history.back();
                    return;
                }
            }

            // fallback: go home
            window.location.href = '/';
        }

        $(document).ready(function() {
            // Initialize Slick Slider
            $('.mb-slid2').slick({
                dots: 1,
                arrows: 1,
                autoplay: true,
                infinite: false,
                slidesToShow: 1,
                slidesToScroll: 1,
                pauseOnHover: 1,
                draggable: 0,
                lazyLoad: 'ondemand',
                // Check for the data-locale attribute and set rtl if it's 'ar'
                rtl: document.getElementById('pr-sld').getAttribute('data-locale') === 'ar'
            });

            var i = $('.pagingInfo');
            $('.mb-slid2').on('init reInit afterChange', function(t, n, a, e) {
                i.text((a || 0) + 1 + '/' + n.slideCount);
            });

            // Booking form interactions
            $('#add_to_wishlist_btn').click(function() {
                // Add to wishlist functionality
                console.log('Add to wishlist clicked');
            });

            // Map directions
            $('#mapShow').attr('href', `https://maps.google.com/maps?q={{ $property->property_address->latitude ?? '24.7136' }},{{ $property->property_address->longitude ?? '46.6753' }}`);
        });

        // Wishlist functionality
        $(document).on('click', '#toggleWishlist', function(e) {
            let authcheck = '{{ auth()->check() }}'
            if (authcheck) {
                e.preventDefault()
                $(this).addClass('disabled')
                var user_id = "{{ Auth::id() }}"
                var property_id = $(this).data('item-id')
                var $icon = $(this)

                $.ajax({
                    type: 'POST',
                    url: "{{ route('toggleWishlist') }}",
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    data: {
                        'user_id': user_id,
                        'property_id': property_id
                    },
                    success: function(data) {
                        if (data.msg == 'Success') {
                            $icon.removeAttr('id')
                            $icon.find('.heart_icon').removeClass('active')
                            $icon.find('.heart_icon').attr('data-status', 0)
                            $icon.attr('data-bs-target',
                                "{{ isset($wishlistExist) ? '#whishlist-listing' : '#create-whishlist' }}"
                            )
                            $icon.removeClass('disabled')
                        }
                    }
                })
            }
        })

        $(document).on('click', '#guestWishlist', function() {
            $('#staticBackdrop').modal('show')
        })
    </script>

    <!-- Share Modal JavaScript -->
    <script>
        $(document).ready(function() {
            // Function to show tooltip with custom message
            function showTooltip(message) {
                $('#tooltip-message').text(message);
                var tooltip = $('#share-tooltip');
                tooltip.css('display', 'block');

                // Use setTimeout to ensure the display property is applied before changing opacity
                setTimeout(function() {
                    tooltip.css('opacity', '1');

                    // Hide tooltip after 2 seconds
                    setTimeout(function() {
                        tooltip.css('opacity', '0');

                        // Hide element after fade out animation completes
                        setTimeout(function() {
                            tooltip.css('display', 'none');
                        }, 300);
                    }, 2000);
                }, 10);
            }

            // Copy link functionality
            $('#copy-link-btn').on('click', function() {
                var propertyUrl = $('#share-property-url').val();
                navigator.clipboard.writeText(propertyUrl).then(function() {
                    showTooltip('Link copied to clipboard successfully!');
                });
            });

            // Email share functionality
            $('#email-share-btn').on('click', function() {
                var propertyUrl = $('#share-property-url').val();
                var propertyName = "{{ $property->name }}";
                var subject = "Check out this property: " + propertyName;
                var body = "I found this amazing property on Darent: " + propertyUrl;
                showTooltip('Opening email client...');
                setTimeout(function() {
                    window.location.href = "mailto:?subject=" + encodeURIComponent(subject) + "&body=" + encodeURIComponent(body);
                }, 500);
            });

            // WhatsApp share functionality
            $('#whatsapp-share-btn').on('click', function() {
                var propertyUrl = $('#share-property-url').val();
                var propertyName = "{{ $property->name }}";
                var text = "Check out this property on Darent: " + propertyName + " " + propertyUrl;
                showTooltip('Opening WhatsApp...');
                window.open('https://wa.me/?text=' + encodeURIComponent(text), '_blank');
            });

            // Facebook share functionality
            $('#facebook-share-btn').on('click', function() {
                var propertyUrl = $('#share-property-url').val();
                showTooltip('Opening Facebook...');
                window.open('https://www.facebook.com/sharer/sharer.php?u=' + encodeURIComponent(propertyUrl), '_blank', 'width=600,height=400');
            });

            // Twitter share functionality
            $('#twitter-share-btn').on('click', function() {
                var propertyUrl = $('#share-property-url').val();
                var propertyName = "{{ $property->name }}";
                var text = "Check out this property on Darent: " + propertyName;
                showTooltip('Opening Twitter...');
                window.open('https://twitter.com/intent/tweet?text=' + encodeURIComponent(text) + '&url=' + encodeURIComponent(propertyUrl), '_blank', 'width=600,height=400');
            });

            // Facebook Messenger share functionality
            $('#messenger-share-btn').on('click', function() {
                var propertyUrl = $('#share-property-url').val();
                showTooltip('Opening Messenger...');
                window.open('https://www.facebook.com/dialog/send?link=' + encodeURIComponent(propertyUrl) + '&app_id=966920033870&redirect_uri=' + encodeURIComponent(propertyUrl), '_blank', 'width=600,height=400');
            });
        });
    </script>

@endpush

<!-- Share Modal -->
<div class="modal fade modal-dr-bottom" id="shareModal" tabindex="-1" aria-labelledby="shareModalLabel" aria-hidden="true">
    <div class="modal-dialog custom-modal-dialog modal-dialog-centered">
        <div class="modal-content custom-modal-content">
                <div class="modal-header custom-small-modal-header">
                    <button type="button" class="btn-close" data-bs-dismiss="modal"
                    aria-label="Close">
                    </button>
                </div>
            <div class="modal-body">
                <h4 class="mb-4">
                    {{ customTrans('property_single.share_this_place') }}
                </h4>
                <!-- Property Preview -->
                <div class="property-preview">
                    <div class="row">
                        <div class="col-12">
                            <img src="{{ isset($property_photos[0]['photo']) ? asset($property_photos[0]['photo']) : asset('images/property-placeholder.jpg') }}" alt="{{ $property->name }}" class="img-fluid">
                        </div>
                        <div class="col-12">
                            <div class="ss-property-content">
                                <p class="mb-0">{{ $property->propertyType->name ?? '' }} in {{ $property->property_address->city ?? '' }} · @if($property->avg_rating) ★{{ number_format($property->avg_rating, 2) }} · @endif {{ $property->bedrooms }} bedroom · {{ $property->beds }} bed · {{ $property->bathrooms }} bath</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Hidden input to store property URL -->
                <input type="hidden" id="share-property-url" value="{{ url()->current() }}">

                <!-- Share Buttons -->
                <div class="ss-property-btns">
                    <div class="row g-2">
                        <div class="col-6">
                            <button id="copy-link-btn">
                                <i class="ri-link"></i>
                                {{ customTrans('property_single.copy_link') }}
                            </button>
                        </div>
                        <div class="col-6">
                            <button id="email-share-btn">
                                <i class="ri-mail-line"></i>
                                {{ customTrans('property_single.email') }}
                            </button>
                        </div>
                        <div class="col-6">
                            <button id="whatsapp-share-btn">
                                <i class="ri-whatsapp-line"></i>
                                {{ customTrans('property_single.whatsApp') }}
                            </button>
                        </div>
                        <div class="col-6">
                            <button id="messenger-share-btn">
                                <i class="ri-messenger-line"></i>
                                {{ customTrans('property_single.messenger') }}
                            </button>
                        </div>
                        <div class="col-6">
                            <button id="facebook-share-btn">
                                <i class="ri-facebook-circle-line"></i>
                                {{ customTrans('property_single.facebook') }}
                            </button>
                        </div>
                        <div class="col-6">
                            <button id="twitter-share-btn">
                                <img src="{{asset('icons/twitter.svg')}}" alt="">
                                {{ customTrans('property_single.twitter') }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Tooltip for share feedback -->
<div id="share-tooltip" style="position: fixed; bottom: 20px; left: 50%; transform: translateX(-50%); background-color: rgba(0, 0, 0, 0.8); color: white; padding: 10px 20px; border-radius: 5px; z-index: 9999; display: none; opacity: 0; transition: opacity 0.3s ease;">
    <span id="tooltip-message"></span>
    <i class="ri-check-line ms-1"></i>
</div>
