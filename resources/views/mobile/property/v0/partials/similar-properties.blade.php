{{-- Similar Properties Section Skeleton --}}
<div id="similar-skeleton" class="services">
    <div class="container">
        <h4 class="fw-400 mt-5 mb-5">
            <div class="skeleton-text" style="width: 180px; height: 28px; background: #f0f0f0; border-radius: 4px; animation: pulse 1.5s ease-in-out infinite;"></div>
        </h4>
        <div class="property">
            <div class="row">
                @for($i = 0; $i < 4; $i++)
                    <div class="col-12 mb-4">
                        <div class="product">
                            <div class="image">
                                <div class="skeleton-image" style="width: 100%; height: 200px; background: #f0f0f0; border-radius: 8px; animation: pulse 1.5s ease-in-out infinite;"></div>
                            </div>
                            <div class="product-detail">
                                <div class="title">
                                    <div class="skeleton-text" style="width: 100%; height: 20px; background: #f0f0f0; border-radius: 4px; margin-bottom: 8px; animation: pulse 1.5s ease-in-out infinite;"></div>
                                    <div class="skeleton-text" style="width: 80px; height: 16px; background: #f0f0f0; border-radius: 4px; margin-bottom: 8px; animation: pulse 1.5s ease-in-out infinite;"></div>
                                </div>
                                <div class="skeleton-text" style="width: 100%; height: 16px; background: #f0f0f0; border-radius: 4px; margin-bottom: 4px; animation: pulse 1.5s ease-in-out infinite;"></div>
                                <div class="skeleton-text" style="width: 70%; height: 16px; background: #f0f0f0; border-radius: 4px; margin-bottom: 8px; animation: pulse 1.5s ease-in-out infinite;"></div>
                                <div class="skeleton-text" style="width: 120px; height: 18px; background: #f0f0f0; border-radius: 4px; animation: pulse 1.5s ease-in-out infinite;"></div>
                            </div>
                        </div>
                    </div>
                @endfor
            </div>
        </div>
    </div>
</div>

{{-- Actual Similar Properties Content (Hidden Initially) --}}
<div id="similar-properties-container" class="services" style="display: none;">
    {{-- Content will be loaded via AJAX --}}
</div>

<style>
/* Desktop Similar Properties Styling - Match Old Design */
#similar-properties-container .host-slider {
    height: 280px;
    position: relative;
    z-index: 11;
}

#similar-properties-container .view-img {
    border-radius: 8px;
    overflow: hidden;
}

#similar-properties-container .product-img {
    width: 100%;
    height: 280px;
    object-fit: cover;
    object-position: center;
}

/* Swiper pagination styling to match old design */
#similar-properties-container .swiper-pagination-bullet {
    background: #f5c33e !important;
}

#similar-properties-container .swiper-pagination-bullet-active {
    background-color: var(--theme-primary) !important;
}

/* Ensure proper image loading */
#similar-properties-container img.lazy {
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

#similar-properties-container img.loaded {
    opacity: 1;
}
</style>