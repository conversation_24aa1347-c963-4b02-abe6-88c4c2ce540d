@php
    function loadImage($path, $property_id) {

        if (is_null($path)) return '/images/default-image.png';

        if (str_starts_with($path, 'http'))
            return $path;

        if (str_contains($path, 'images/property'))
            return asset($path);

        $base = '/images/property';
        if (!is_null($property_id)) $base .= '/' . $property_id;
        return  asset($base . (str_starts_with($path, '/') ? $path : '/' . $path));
    }
@endphp

{{-- Mobile Similar Properties Items (AJAX Loaded) --}}
<div class="container">
    <h4 class="fw-400 mt-5 mb-5">{{ customTrans('property_single.similar_listing') }}</h4>
    <div class="property">
        <div class="row">
            @forelse ($similar as $property)
                <div class="col-xl-3 col-lg-4 col-sm-4 col-xs-12">
                    <div class="product">
                        <div class="image">
                            @if ($property->property_discount)
                                <div class="discount-tag">
                                    <span>{{ $property->property_discount->discount }}</span>%
                                </div>
                            @endif
                            <div class="views">
                                <span><i class="ri-eye-fill"></i></span>
                                <span>{{ App\Http\Helpers\Common::formatProductViewCount((int) ($property->userPropertyView ? $property->userPropertyView->count() : 0) + 100) }}</span>
                            </div>

                            <swiper-container class="host-slider mobile" pagination="true" pagination-dynamic-bullets="true" navigation="true">
                                <swiper-slide>
                                    <a href="{{ route('property.v0.single', ['lang' => app()->getLocale(), 'slug' => $property->slug]) }}"
                                        aria-label="{{ $property->name }}" class="">
                                        <img src="{{ asset('images/loader.gif') }}" height="auto"
                                            width="auto" class="product-img lazy"
                                            data-src="{{ loadImage($property->cover_photo, property_id: $property->id) }}" loading="lazy"
                                            alt="{{ $property->name }}">
                                    </a>
                                </swiper-slide>

                                @php $filteredPhotos = $property->property_photos->where('cover_photo', '!=', 1)->take(5); @endphp
                                @foreach ($filteredPhotos as $filterphoto)
                                    <swiper-slide>
                                        <a href="{{ route('property.v0.single', ['lang' => app()->getLocale(), 'slug' => $property->slug]) }}"
                                            aria-label="{{ $property->name }}" class="">
                                            <img src="{{ asset('images/loader.gif') }}" height="auto"
                                                width="auto" class="product-img lazy"
                                                data-src="{{ loadImage($filterphoto->photo, property_id: $property->id) }}" loading="lazy"
                                                alt="{{ $property->name }}">
                                        </a>
                                    </swiper-slide>
                                @endforeach
                            </swiper-container>

                            <div class="fav-icon " data-bs-toggle="modal"
                                @auth
                                    @if ($property->wishlist == true) id="toggleWishlist"
                                    @else
                                        data-bs-target="{{ isset($wishlistExist) ? '#whishlist-listing' : '#create-whishlist' }}"
                                    @endif
                                @endauth
                                @guest
                                    id="guestWishlist"
                                @endguest
                                data-item-id="{{ $property->id }}">
                                <div class="fav-in {{ $property->wishlist == true ? 'active' : '' }}  heart_icon"
                                    data-status="{{ $property->wishlist }}" data-id="{{ $property->id }}">
                                    <img src="{{ asset('icons/Path 125.svg') }}" height="auto"
                                        width="auto" alt="heart" class="fav-blank">
                                    <img src="{{ asset('icons/fill-heart.svg') }}" height="auto"
                                        width="auto" alt="heart" class="fav-fill">
                                </div>
                            </div>
                        </div>
                        <div class="product-detail">
                            <a href="{{ route('property.v0.single', ['lang' => app()->getLocale(), 'slug' => $property->slug]) }}"
                                aria-label="{{ $property->name }}" class="float-anc"></a>
                            <div class="title">
                                <a href="{{ route('property.v0.single', ['lang' => app()->getLocale(), 'slug' => $property->slug]) }}"
                                    aria-label="{{ $property->name }}">
                                    <h4>
                                        {{ propertyTitle($property->id, $property->propertyType) }}
                                    </h4>
                                </a>
                                @if($property->is_new_lable)
                                    <p class="product-rate">
                                        <span class="badge bg-danger px-2 py-1 shadow-1-strong mb-3">{{ customTrans('host_dashboard.new') }}</span>
                                    </p>
                                @else
                                    <p class="product-rate"><img src="{{ asset('icons/rate-2.svg') }}"
                                            height="auto" width="auto" alt="">
                                        @if ($property->guest_review)
                                            {{ digitsToArabic($property->avg_rating) }}/
                                            <span>{{ digitsToArabic('5') }}</span>
                                            ({{ digitsToArabic($property->guest_review) }})
                                        @else
                                            {{ digitsToArabic('0') }}/<span>{{ digitsToArabic('5') }}</span>
                                            ({{ digitsToArabic('0') }})
                                        @endif
                                    </p>
                                @endif
                            </div>

                            {{-- getDescription in helpers.php --}}
                            @php $description = getDescription($property->property_description); @endphp
                            <p class="product-content">
                                {{-- shortString in helpers.php --}}
                                {{ shortString($description, 70) }}
                                @if (strlen($description) > 70)
                                    <span class="rd-more">{{ customTrans('property_single.read_more') }}</span>
                                @endif
                            </p>

                            <div class="pr-code">
                                <p class="product-price">
                                    @if ($property->before_discount > $property->total_price)
                                        <style>
                                            .old-price {
                                                text-decoration: line-through;
                                                font-size: .9em;
                                            }
                                        </style>
                                        <span class="old-price">
                                            {{ number_format($property->before_discount / $property->number_of_days, 2, '.', '') }}
                                        </span>
                                    @endif
                                    {{ number_format($property->total_price / $property->number_of_days, 2, '.', '') }}
                                    <span class="sar-pr">{{ customTrans('utility.sar') }}</span>/ Night
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            @empty
                <div class="col-12">
                    <p class="text-center">{{ customTrans('property_single.no_similar_properties') }}</p>
                </div>
            @endforelse
        </div>
    </div>
</div>

