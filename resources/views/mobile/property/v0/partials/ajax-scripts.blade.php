<script>
// Ensure jQuery is loaded and DOM is ready
// Wait for jQuery to be available
function waitForJQuery(callback) {
    if (typeof $ !== 'undefined' && $.fn) {
        callback();
    } else {
        setTimeout(function() {
            waitForJ<PERSON>uery(callback);
        }, 100);
    }
}

function initializeLazyLoading() {
    const lazyImages = document.querySelectorAll('#similar-properties-container img.lazy');

    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    if (img.dataset.src) {
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        img.classList.add('loaded');
                        imageObserver.unobserve(img);
                    }
                }
            });
        });

        lazyImages.forEach(img => imageObserver.observe(img));
    } else {
        // Fallback for older browsers
        lazyImages.forEach(img => {
            if (img.dataset.src) {
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                img.classList.add('loaded');
            }
        });
    }
}

waitForJQuery(function() {
    $(document).ready(function() {

    const propertySlug = '{{ $property->slug }}';

    // Lazy Load Reviews Section
    function loadReviews() {
        $.ajax({
            url: '{{ route("property.v0.reviews") }}',
            method: 'GET',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            },
            data: {
                property_id: '{{ $property->id }}',
                slug: '{{ $property->slug }}',
                offset: 0,
                limit: 5
            },
            success: function(response) {
                if (response.status === 'success') {
                    $('#reviews-content').html(response.html).show();
                    $('#reviews-skeleton').hide().css('display', 'none !important').addClass('skeleton-hidden');
                } else {
                    $('#reviews-skeleton').html('<div class="alert alert-warning">Unable to load reviews at this time.</div>');
                }
            },
            error: function(xhr, status, error) {
                $('#reviews-skeleton').html('<div class="alert alert-warning">Unable to load reviews at this time.</div>');
            }
        });
    }

    // Lazy Load Map Section
    function loadMap() {
        // Create map content directly since we have the data
        const mapContent = `
            <div class="property-location">
                <div class="row">
                    <div class="col-6">
                        <h5 class="fw-400 mb-4">{{ customTrans('property_single.location_on_map') }}</h5>
                    </div>
                    <div class="col-6 text-right-dir mb-4">
                        <button class="theme-btn" data-bs-toggle="modal" data-bs-target="#getdirections">
                            {{ customTrans('footer.get_directions') }}
                        </button>
                    </div>
                </div>
                <div id="room-detail-map" class="single-map-w" style="height: 250px !important;">
                    <div class="mp-loader">
                        <div class="pin"></div>
                        <div class="pulse"></div>
                    </div>
                </div>
            </div>
        `;

        $('#map-content').html(mapContent).show();
        $('#map-skeleton').hide().css('display', 'none !important').addClass('skeleton-hidden');

        // Initialize the map with locationpicker
        setTimeout(function() {
            if (typeof $.fn.locationpicker !== 'undefined') {
                $('#room-detail-map').locationpicker({
                    location: {
                        latitude: "{{ $property->property_address->latitude ?? '24.7136' }}",
                        longitude: "{{ $property->property_address->longitude ?? '46.6753' }}"
                    },
                    radius: 0,
                    addressFormat: '',
                    markerVisible: false,
                    markerInCenter: false,
                    enableAutocomplete: true,
                    scrollwheel: false,
                    oninitialized: function(component) {
                        setMapCircle($(component).locationpicker('map').map);
                    }
                });
            }
        }, 500);

        // Function to set circle on map
        function setMapCircle(map) {
            if (typeof google !== 'undefined' && google.maps) {
                var cityCircle = new google.maps.Circle({
                    strokeColor: '#329793',
                    strokeOpacity: 0.8,
                    strokeWeight: 2,
                    fillColor: '#329793',
                    fillOpacity: 0.35,
                    map: map,
                    center: {
                        lat: parseFloat("{{ $property->property_address->latitude ?? '24.7136' }}"),
                        lng: parseFloat("{{ $property->property_address->longitude ?? '46.6753' }}")
                    },
                    radius: 240
                });
            }
        }
    }

    // Lazy Load Similar Properties Section
    function loadSimilarProperties() {
        $.ajax({
            url: '{{ route("property.v0.similar") }}',
            method: 'GET',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            },
            data: {
                property_id: '{{ $property->id }}',
                slug: '{{ $property->slug }}',
                limit: 4
            },
            success: function(response) {
                if (response.status === 'success') {
                    $('#similar-properties-container').html(response.html).show();
                    $('#similar-skeleton').hide().css('display', 'none !important').addClass('skeleton-hidden');

                    // Initialize lazy loading for similar property images
                    if (typeof initializeLazyLoading === 'function') {
                        initializeLazyLoading();
                    }

                    // Initialize swiper for similar properties
                    if (typeof initializeSimilarPropertiesSwiper === 'function') {
                        initializeSimilarPropertiesSwiper();
                    }
                } else {
                    $('#similar-skeleton').html('<div class="alert alert-warning">Unable to load similar properties at this time.</div>');
                }
            },
            error: function(xhr, status, error) {
                $('#similar-skeleton').html('<div class="alert alert-warning">Unable to load similar properties at this time.</div>');
            }
        });
    }

    // Setup Intersection Observers for lazy loading
    function setupSectionLazyLoading() {
        // Check if Intersection Observer is supported
        if ('IntersectionObserver' in window) {
            // Observer for Reviews Section
            const reviewsSection = document.querySelector('#reviews-skeleton');
            if (reviewsSection) {
                let reviewsLoaded = false;
                const reviewsObserver = new IntersectionObserver(function(entries, observer) {
                    entries.forEach(function(entry) {
                        if (entry.isIntersecting && !reviewsLoaded) {
                            reviewsLoaded = true;
                            setTimeout(loadReviews, 300);
                            observer.unobserve(entry.target);
                        }
                    });
                }, {
                    root: null,
                    rootMargin: '100px', // Start loading 100px before section is visible
                    threshold: 0.1
                });
                reviewsObserver.observe(reviewsSection);
            }

            // Observer for Map Section
            const mapSection = document.querySelector('#map-skeleton');
            if (mapSection) {
                let mapLoaded = false;
                const mapObserver = new IntersectionObserver(function(entries, observer) {
                    entries.forEach(function(entry) {
                        if (entry.isIntersecting && !mapLoaded) {
                            mapLoaded = true;
                            setTimeout(loadMap, 300);
                            observer.unobserve(entry.target);
                        }
                    });
                }, {
                    root: null,
                    rootMargin: '100px', // Start loading 100px before section is visible
                    threshold: 0.1
                });
                mapObserver.observe(mapSection);
            }

            // Observer for Similar Properties Section
            const similarSection = document.querySelector('#similar-skeleton');
            if (similarSection) {
                let similarLoaded = false;
                const similarObserver = new IntersectionObserver(function(entries, observer) {
                    entries.forEach(function(entry) {
                        if (entry.isIntersecting && !similarLoaded) {
                            similarLoaded = true;
                            setTimeout(loadSimilarProperties, 300);
                            observer.unobserve(entry.target);
                        }
                    });
                }, {
                    root: null,
                    rootMargin: '100px', // Start loading 100px before section is visible
                    threshold: 0.1
                });
                similarObserver.observe(similarSection);
            }
        } else {
            // Fallback for browsers without Intersection Observer support
            var reviewsLoaded = false;
            var mapLoaded = false;
            var similarLoaded = false;

            $(window).on('scroll', function() {
                var scrollTop = $(window).scrollTop();
                var windowHeight = $(window).height();

                // Check Reviews Section
                if (!reviewsLoaded && $('#reviews-skeleton').length) {
                    var reviewsTop = $('#reviews-skeleton').offset().top;
                    if (scrollTop + windowHeight >= reviewsTop - 150) {
                        reviewsLoaded = true;
                        setTimeout(loadReviews, 300);
                    }
                }

                // Check Map Section
                if (!mapLoaded && $('#map-skeleton').length) {
                    var mapTop = $('#map-skeleton').offset().top;
                    if (scrollTop + windowHeight >= mapTop - 150) {
                        mapLoaded = true;
                        setTimeout(loadMap, 300);
                    }
                }

                // Check Similar Properties Section
                if (!similarLoaded && $('#similar-skeleton').length) {
                    var similarTop = $('#similar-skeleton').offset().top;
                    if (scrollTop + windowHeight >= similarTop - 150) {
                        similarLoaded = true;
                        setTimeout(loadSimilarProperties, 300);
                    }
                }

                // Remove scroll listener if all sections are loaded
                if (reviewsLoaded && mapLoaded && similarLoaded) {
                    $(window).off('scroll');
                }
            });
        }
    }

    // Add manual test functions to window for debugging
    window.testLoadReviews = loadReviews;
    window.testLoadSimilar = loadSimilarProperties;
    window.testLoadMap = loadMap;

    // Initialize lazy loading after a short delay
    setTimeout(setupSectionLazyLoading, 200);
    });
});
</script>

{{-- Skeleton Animation CSS --}}
<style>
@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 1;
    }
}

.skeleton-text, .skeleton-bar, .skeleton-avatar, .skeleton-image, .skeleton-map {
    animation: pulse 1.5s ease-in-out infinite;
}

.skeleton-text {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

.mp-loader .pulse {
    animation: pulse-ring 2s ease-in-out infinite;
}

@keyframes pulse-ring {
    0% {
        transform: scale(0.8);
        opacity: 1;
    }
    100% {
        transform: scale(1.2);
        opacity: 0;
    }
}

/* Force hide skeletons when content is loaded */
.skeleton-hidden {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
}
</style>
