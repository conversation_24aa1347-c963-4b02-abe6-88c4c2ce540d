@extends('mobile.template')
@push('css')
    <style>
        footer {
            display: none;
        }
    </style>
@endpush
@section('main')
    @php
        $user_id = Auth::id();
        // dd($user_id);
    @endphp
    <div class="container-fluid">
        <section class="wishlist-listing-sec dubai-ff">
            <div class="row h-100">
                <div class="col-md-6 col-12">
                    <div class="wl-property">
                        {{-- <div class="pg-head d-flex justify-content-between align-items-center my-4">
                            <div class="d-flex align-items-center">
                                <a href="{{ route('wishlists.listing') }}" class="transparent-btn wl-left-btn rtl-leftArrowRotat">
                                    <img src="{{ asset('icons/black-larrow.svg') }}" alt="">
                                </a>
                                <h2 class="pg-main-title mb-0 ms-4">{{ $wishlist->name }}</h2>
                            </div>
                            @auth

                                @if ($wishlist->creater_id == $user_id)
                                    <div class="wl-right-btn">
                                        <button class="transparent-btn">
                                            <img src="{{ asset('icons/share-icon.svg') }}" alt="" id="share_link"
                                                data-bs-toggle="modal" data-bs-target="#wishlist-share-link">
                                        </button>
                                        <button class="transparent-btn btn-delete" data-bs-toggle="modal"
                                            data-bs-target="#delete-wishlist" data-wishlist-id="{{ $wishlist->id }}"
                                            data-wishlist-name="{{ $wishlist->name }}">
                                            <img src="{{ asset('icons/dots.svg') }}" alt="">
                                        </button>
                                    </div>
                                @endif
                                @if (Str::contains(',' . $wishlist->share_with . ',', ',' . $user_id . ','))
                                    <div class="wl-right-btn">
                                        <button class="transparent-btn btn-delete" data-bs-toggle="modal"
                                            data-bs-target="#collaborator-wishlist" data-wishlist-id="{{ $wishlist->id }}"
                                            data-wishlist-name="{{ $wishlist->name }}">
                                            <img src="{{ asset('icons/dots.svg') }}" alt="">
                                        </button>
                                    </div>
                                @endif


                            @endauth

                        </div> --}}

                        <div class="row">
                            <h2 class="pg-main-title">{{ $wishlist->name }}</h2>
                            @foreach ($wishlistProperties as $wishlistProperty)
                                @php
                                    $url = '';
                                    $dis = '#property_location';
                                    if (session()->get('header_checkin') && session()->get('header_checkout')) {
                                        $url =
                                            '?checkin=' .
                                            session()->get('header_checkin') .
                                            '&checkout=' .
                                            session()->get('header_checkout');
                                        $dis = '&#property_location';
                                    } elseif (!empty($_GET['checkin']) && !empty($_GET['checkout'])) {
                                        $url = '?checkin=' . $_GET['checkin'] . '&checkout=' . $_GET['checkout'];
                                        $dis = '&#property_location';
                                    } else {
                                        $url =
                                            '?checkin=' .
                                            date('Y-m-d') .
                                            '&checkout=' .
                                            date('Y-m-d', strtotime('+1 day'));
                                        $dis = '#property_location';
                                    }
                                @endphp
                                <x-property-card :property="$wishlistProperty->property" :url="$url" :dis="$dis" :wishlistExist="$wishlist"
                                    :per_row="2" />
                            @endforeach
                            {{-- <div class="col-md-4">
                                <div class="property">
                                    <div class="product">
                                        <a href="#" class="float-anc"></a>
                                        <div class="image">
                                            <img src="{{ asset('images/p3.png') }}" class="product-img" alt="">
                                            <div class="fav-icon ">
                                                <div class="fav-in">
                                                    <img src="{{ asset('icons/Path 125.svg') }}" height="auto"
                                                        width="auto" alt="heart" class="fav-blank">
                                                    <img src="{{ asset('icons/fill-heart.svg') }}" height="auto"
                                                        width="auto" alt="heart" class="fav-fill">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="product-detail">
                                            <div class="title">
                                                <h4>demo</h4>

                                                <p class="product-rate"><img src="{{ asset('icons/rate-2.svg') }}"
                                                        height="auto" width="auto" alt="">
                                                </p>
                                            </div>
                                            <p class="product-content ">
                                                <span class="rd-more">demo2</span>
                                            </p>

                                            <p class="product-price"> <span class="discount-cut"></span>
                                                200</p>
                                        </div>

                                    </div>
                                </div>
                            </div>
                            <div class="col-xxl-4 col-xl-6">
                                <div class="property">
                                    <div class="product">
                                        <a href="#" class="float-anc"></a>
                                        <div class="image">
                                            <img src="{{ asset('images/p3.png') }}" class="product-img" alt="">
                                            <div class="fav-icon ">
                                                <div class="fav-in">
                                                    <img src="{{ asset('icons/Path 125.svg') }}" height="auto"
                                                        width="auto" alt="heart" class="fav-blank">
                                                    <img src="{{ asset('icons/fill-heart.svg') }}" height="auto"
                                                        width="auto" alt="heart" class="fav-fill">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="product-detail">
                                            <div class="title">
                                                <h4>demo</h4>

                                                <p class="product-rate"><img src="{{ asset('icons/rate-2.svg') }}"
                                                        height="auto" width="auto" alt="">
                                                </p>
                                            </div>
                                            <p class="product-content ">
                                                <span class="rd-more">demo2</span>
                                            </p>

                                            <p class="product-price"> <span class="discount-cut"></span>
                                                200</p>
                                        </div>

                                    </div>
                                </div>
                            </div> --}}
                        </div>
                    </div>
                </div>
                <div class="col-md-6 d-none">
                    <div class="wishlist-map ">
                        <iframe
                            src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d41997.722104195665!2d39.18398250360444!3d21.503439840383827!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x15c3d01fb1137e59%3A0xe059579737b118db!2sJeddah%20Saudi%20Arabia!5e0!3m2!1sen!2s!4v1688462531962!5m2!1sen!2s"
                            width="100%" height="100%" style="border:0;" allowfullscreen="" loading="lazy"
                            referrerpolicy="no-referrer-when-downgrade"></iframe>
                    </div>
                </div>
            </div>
    </div>

    {{-- Success Modal --}}
    <div class="modal fade modal-dr-bottom" id="updatedSuccess" data-bs-backdrop="static" data-bs-keyboard="false"
        tabindex="-1" aria-labelledby="successLabel" aria-hidden="true">
        <div class="modal-dialog custom-modal-dialog custom-small-modal-width modal-dialog-centered">
            <div class="modal-content custom-modal-content">
                <div class="modal-header  custom-small-modal-header">

                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                    </button>
                </div>
                <div class="modal-body">
                    <div class="alert-modal">
                        <img src="{{ asset('icons/check-success.svg') }}" alt="" class="mb-3">
                        <h3 class="text-success mb-4 fw-600 ">{{ customTrans('general.success') }}</h3>
                        <p class="mb-4">{{ customTrans('wishlist.wishlist_updated_successfully') }}</p>
                        <a href="#">
                            <button class="theme-btn btn-success w-100 successmodalbtn" data-bs-dismiss="modal"
                                aria-label="Close">{{ customTrans('general.ok') }}</button>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Share Link Modal --}}
    <div class="modal fade dubai-ff modal-dr-bottom" id="wishlist-share-link" data-bs-backdrop="static"
        data-bs-keyboard="false" tabindex="-1" aria-labelledby="share-link" aria-hidden="true">
        <div class="modal-dialog custom-modal-dialog modal-dialog-centered">
            <div class="modal-content cm-bd-content">
                <div class="modal-header cm-bd-header">
                    <h5 class="w-100 text-center mb-0" id="share-link">How do you want to share?</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                    </button>
                </div>
                <div class="modal-body cm-bd-body">
                    <div class="share-link">

                        <button class="transparent-btn sl-btn" id="shareLinkBtn" onclick="copyLinkField()">
                            <input type="hidden" name="share_link" id="link_field" value="{{ $wishlist->share_link }}">
                            <img src="{{ asset('icons/link-icon.svg') }}" alt="">
                            <div class="sl-content">
                                <h5>Send a link</h5>
                                <p class="mb-0">anyone with the link can view</p>
                            </div>
                        </button><br />
                        @if (isset($wishlist->collaborate_link))
                            <button class="transparent-btn sl-btn" id="collaborateBtn" onclick="copyCollaboratorField()">
                                <input type="hidden" name="collaborate" id="collaborator_field"
                                    value="{{ isset($wishlist->collaborate_link) ? $wishlist->collaborate_link : null }}">
                                <input type="hidden" name="code" id="code" value="{{ $wishlist->code }}">

                                <img src="{{ asset('icons/link-icon.svg') }}" alt="">
                                <div class="sl-content">
                                    <h5>Collaborate</h5>
                                    <p class="mb-0">Invite others to add to this wishlist</p>
                                </div>
                            </button>
                        @endif
                        <div class="share-si">
                            <h4>Send via social media</h4>
                            <ul>
                                <li>
                                    <a href="https://www.facebook.com/profile.php?id=100083206208439&is_tour_dismissed=true"
                                        target="_blank">
                                        <img src="{{ asset('images/facebook.png') }}" alt="">
                                    </a>
                                </li>
                                <li>
                                    <a href="https://www.instagram.com/darentapp" target="_blank">
                                        <img src="{{ asset('images/instagram.png') }}" alt="">
                                    </a>
                                </li>
                                <li>
                                    <a href="https://api.whatsapp.com/send?phone=966533318409&text=Hi" target="_blank">
                                        <img src="{{ asset('images/whatsapp.png') }}" alt="">
                                    </a>
                                </li>
                                <li>
                                    <a href="#">
                                        <img src="{{ asset('images/snapchat.png') }}" alt="">
                                    </a>
                                </li>
                                <li>
                                    <a href="https://www.tiktok.com/@darentapp" target="_blank">
                                        <img src="{{ asset('images/tik-tok.png') }}" alt="">
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- share modal start-->
    <div class="modal fade modal-dr-bottom mb-card-share-modal" id="single-share-modal" data-bs-backdrop="static"
        data-bs-keyboard="false" tabindex="-1" aria-labelledby="singleShareModalLabel" aria-hidden="true">
        <div class="modal-dialog custom-modal-dialog modal-dialog-centered">
            <div class="modal-content custom-modal-content">
                <div class="modal-header custom-small-modal-header">
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                    </button>
                </div>
                <div class="modal-body">
                    <h4 class="mb-4">
                        {{ customTrans('property_single.share_this_place') }}
                    </h4>
                    <div class="property-preview">
                        <!-- Image tag will be here -->
                        <div class="row">
                            <div class="col-12">
                                <img src="" id="share-property-image">
                            </div>
                            <div class="col-12">
                                <div class="ss-property-content">
                                    <p class="mb-0" id="share-property-title"></p>
                                </div>
                            </div>
                        </div>
                        <input type="hidden" id="share-property-url" value="{{ url()->current() }}">
                        <div class="ss-property-btns">
                            <div class="row g-2">
                                <div class="col-6">
                                    <button class="transparent-btn" id="copy-link-btn">
                                        <i class="far fa-copy"></i>
                                        {{ customTrans('property_single.copy_link') }}
                                    </button>
                                </div>
                                <div class="col-6">
                                    <button class="transparent-btn" id="email-share-btn">
                                        <i class="far fa-envelope"></i>
                                        {{ customTrans('property_single.email') }}
                                    </button>
                                </div>
                                <div class="col-6">
                                    <button class="transparent-btn" id="whatsapp-share-btn">
                                        <i class="fab fa-whatsapp"></i>
                                        {{ customTrans('property_single.whatsApp') }}
                                    </button>
                                </div>
                                <div class="col-6">
                                    <button class="transparent-btn" id="messenger-share-btn">
                                        <i class="fab fa-facebook-messenger"></i>
                                        {{ customTrans('property_single.messenger') }}
                                    </button>
                                </div>
                                <div class="col-6">
                                    <button class="transparent-btn" id="facebook-share-btn">
                                        <i class="fab fa-facebook"></i>
                                        {{ customTrans('property_single.facebook') }}
                                    </button>
                                </div>
                                <div class="col-6">
                                    <button class="transparent-btn" id="twitter-share-btn">
                                        <img src="{{ asset('icons/twitter.svg') }}" alt="">
                                        {{ customTrans('property_single.twitter') }}
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div id="share-tooltip" class="tooltip-container"
                            style="position: fixed; bottom: 20px; left: 50%; transform: translateX(-50%); z-index: 9999; display: none; opacity: 0; transition: opacity 0.3s ease-in-out;">
                            <div class="tooltip-content"
                                style="background-color: rgba(0, 0, 0, 0.8); color: white; padding: 10px 20px; border-radius: 5px; font-size: 14px; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);">
                                <i class="fas fa-check-circle" style="margin-right: 5px;"></i> <span
                                    id="tooltip-message">Link copied to clipboard successfully!</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- share modal end -->
    </section>
@stop
@push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {

            propertyCardSlider()
            popOverFunction()
        });
        $(document).on('click', '.share-property-btn', function(e) {
            e.preventDefault()
            var propertyTypeName = $(this).data('property-type-name');
            var cityName = $(this).data('city-name');
            var bedrooms = $(this).data('bedrooms');
            var bathrooms = $(this).data('bathrooms');
            var beds = $(this).data('beds');
            var rating = $(this).data('rating');
            var image = $(this).data('image');
            let propertyTitleShareModal = propertyTypeName + ' in ' + cityName + ' · ' + rating + bedrooms +
                ' bedroom · ' + beds + ' bed · ' + bathrooms + ' bath';
            // console.log(propertyTitleShareModal);
            $('#share-property-image').attr('src', image);
            $('#share-property-title').text(propertyTitleShareModal);
        })

        function showTooltip(message) {
            $('#tooltip-message').text(message);
            var tooltip = $('#share-tooltip');
            tooltip.css('display', 'block');

            // Use setTimeout to ensure the display property is applied before changing opacity
            setTimeout(function() {
                tooltip.css('opacity', '1');

                // Hide tooltip after 2 seconds
                setTimeout(function() {
                    tooltip.css('opacity', '0');

                    // Hide element after fade out animation completes
                    setTimeout(function() {
                        tooltip.css('display', 'none');
                    }, 300);
                }, 2000);
            }, 10);
        }

        // Copy link functionality
        $('#copy-link-btn').on('click', function() {
            var propertyUrl = $('#share-property-url').val();
            navigator.clipboard.writeText(propertyUrl).then(function() {
                showTooltip('Link copied to clipboard successfully!');
            });
        });

        // Email share functionality
        $('#email-share-btn').on('click', function() {
            var propertyUrl = $('#share-property-url').val();
            var propertyName = $('#share-property-title').val();
            var subject = "Check out this property: " + propertyName;
            var body = "I found this amazing property on Darent: " + propertyUrl;
            showTooltip('Opening email client...');
            setTimeout(function() {
                window.location.href = "mailto:?subject=" + encodeURIComponent(subject) + "&body=" +
                    encodeURIComponent(body);
            }, 500);
        });

        // WhatsApp share functionality
        $('#whatsapp-share-btn').on('click', function() {
            var propertyUrl = $('#share-property-url').val();
            var propertyName = $('#share-property-title').val();
            var text = "Check out this property on Darent: " + propertyName + " " + propertyUrl;
            showTooltip('Opening WhatsApp...');
            window.open('https://wa.me/?text=' + encodeURIComponent(text), '_blank');
        });

        // Facebook share functionality
        $('#facebook-share-btn').on('click', function() {
            var propertyUrl = $('#share-property-url').val();
            showTooltip('Opening Facebook...');
            window.open('https://www.facebook.com/sharer/sharer.php?u=' + encodeURIComponent(propertyUrl), '_blank',
                'width=600,height=400');
        });

        // Twitter share functionality
        $('#twitter-share-btn').on('click', function() {
            var propertyUrl = $('#share-property-url').val();
            var propertyName = $('#share-property-title').val();
            var text = "Check out this property on Darent: " + propertyName;
            showTooltip('Opening Twitter...');
            window.open('https://twitter.com/intent/tweet?text=' + encodeURIComponent(text) + '&url=' +
                encodeURIComponent(propertyUrl), '_blank', 'width=600,height=400');
        });

        // Facebook Messenger share functionality
        $('#messenger-share-btn').on('click', function() {
            var propertyUrl = $('#share-property-url').val();
            showTooltip('Opening Messenger...');
            window.open('https://www.facebook.com/dialog/send?link=' + encodeURIComponent(propertyUrl) +
                '&app_id=966920033870&redirect_uri=' + encodeURIComponent(propertyUrl), '_blank',
                'width=600,height=400');
        });

        $(document).ready(function() {
            $('.btn-delete').click(function() {
                var wishlistId = $(this).data('wishlist-id');
                var wishlistName = $(this).data('wishlist-name');

                $('#wishlist-id').val(wishlistId);
                $('#rename-title').val(wishlistName);
                $('.within-delete-wishlist').attr('data-id', wishlistId);
            });
        });
        $('#rename-title').on('input', function() {
            let characterCount = $(this).val().length,
                minimum = 0,
                maximum = 50,
                remaining = maximum - characterCount,
                theCount = $(this).siblings('.character-count-display');

            theCount.text(characterCount + '/' + maximum + ' characters maximum');
        });
        $(document).on('click', "#updateWishlistName", function(e) {
            e.preventDefault();
            $(this).addClass('disabled');

            var id = $('#wishlist-id').val();
            var name = $('#rename-title').val();
            var token = $('input[name="_token"]').val();
            $.ajax({
                type: "POST",
                url: "{{ route('updateWishlist') }}",
                data: {
                    "_token": token,
                    "name": name,
                    "id": id,
                },
                success: function(data) {
                    if (data.msg == "Success") {
                        $("#delete-wishlist").modal("hide");
                        $('#updatedSuccess').modal('show');
                        window.location.reload(true);

                    }

                }
            });
        });
        $(document).on('click', '.within-delete-wishlist', function(e) {
            var id = $(this).data('id');
            $('#wishlist-id').val(id);
        });
        $(document).on('click', '#confirmDeleteBtn', function(e) {
            e.preventDefault();
            $(this).addClass('disabled');

            var id = $('#wishlist-id').val();
            var token = $('input[name="_token"]').val();
            $.ajax({
                type: "POST",
                url: "{{ route('deleteWishlist') }}",
                data: {
                    "_token": token,
                    "id": id,
                },
                success: function(data) {
                    if (data.msg == "Success") {
                        $("#confirm-delete-wishlist").modal("hide");
                        // $('#success').modal('show');
                        window.location.replace(window.location.origin + '/wishlists');

                    }

                }
            });
        });
        $(document).on('click', '#heartIcon', function(e) {
            e.preventDefault();
            $(this).addClass('disabled');
            var wishlistNameId = $(this).data('wishlist-name-id');
            var propertyId = $(this).data('property-id');
            var token = $('input[name="_token"]').val();

            $.ajax({
                type: "POST",
                url: "{{ route('addRemoveWishlist') }}",
                data: {
                    "_token": token,
                    "wishlist_name_id": wishlistNameId,
                    "property_id": propertyId,
                },
                success: function(data) {
                    if (data.msg == "Success") {
                        if (data.status == '1') {
                            $('#heartIcon').addClass('active');
                        } else {
                            $('#heartIcon').removeClass('active');
                        }
                        // $("#confirm-delete-wishlist").modal("hide");
                        // $('#success').modal('show');
                        $('#heartIcon').removeClass('disabled');
                        window.location.reload();
                    }

                }
            });
        });

        function copyLinkField() {
            var linkFieldValue = $('#link_field').val();

            navigator.clipboard.writeText(linkFieldValue)
                .then(function() {
                    // Success message or any other action
                    $('#wishlist-share-link').modal('hide');

                    // Add 'copiedLink' class to the button
                    $('#shareLinkBtn').addClass('copiedLink');
                })
                .catch(function(error) {
                    // Error handling
                    console.error('Failed to copy link field value:', error);
                });
        }

        function copyCollaboratorField() {
            var collaboratorFieldValue = $('#collaborator_field').val();

            // Copy the value to clipboard or perform any other desired action
            navigator.clipboard.writeText(collaboratorFieldValue)
                .then(function() {
                    var code = $('#code').val();
                    $.ajax({
                        type: "GET",
                        url: "{{ route('collaborate') }}",
                        data: {
                            "code": code,
                        },
                        success: function(data) {
                            if (data.msg == "Success") {
                                $('#wishlist-share-link').modal('hide');
                                console.log("Success");

                                // Change button color to grey
                                $('#collaborateBtn').addClass('copiedLink');
                            }
                        }
                    });
                    // Success message or any other action
                    console.log('Collaborator field value copied successfully');
                })
                .catch(function(error) {
                    // Error handling
                    console.error('Failed to copy collaborator field value:', error);
                });
        }
    </script>
@endpush
