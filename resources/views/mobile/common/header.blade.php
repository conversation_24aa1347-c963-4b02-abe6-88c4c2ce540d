<!--================ Header Menu Area start =================-->
<?php
$lang = Session::get('language');
$checkin_session = Session::get('header_checkin');
$checkout_session = Session::get('header_checkout');
$adult_guest_session = Session::get('adult_guest_session') == '' ? 1 : Session::get('adult_guest_session');
$child_guest_session = Session::get('child_guest_session') == '' ? 0 : Session::get('child_guest_session');
$guest = $child_guest_session + $adult_guest_session;
$routeName = Route::currentRouteName();
$group = explode('.', $routeName);

use App\Models\Settings;
use App\Models\Properties;
use Carbon\Carbon;

$cities = app('CITIES');
$default_city = app('DEFAULT_CITY');
?>
{{-- @php
    dd(app()->getLocale());
@endphp --}}
<link rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-select/1.14.0-beta2/css/bootstrap-select.min.css">
{{-- <input type="hidden" id="front_date_format_type" value="{{ Session::get('front_date_format_type') }}"> --}}
{{-- @if (request()->segment()) --}}
@if (request()->route()->uri == 'listing/{id}/{step}' ||
        request()->route()->uri == 'starthosting' ||
        request()->route()->uri == 'property/addplace')
    <header id="headerforPDF" class="nav-header inner-header d-none">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-2">
                    <div class="page-back">
                        <a href="{{ url()->previous() }}">
                            <button class="go-back">
                                <img src="{{ asset('../icons/go-back.svg') }}" alt=""
                                     class="rtl-leftArrowRotat">
                            </button>
                        </a>
                    </div>
                </div>
                <div class="col-8">
                    <div class="head-content">
                        @php
                            $title = isset($title) ? $title : 'Darent';
                        @endphp
                        <h3>{{ $title }}</h3>
                    </div>
                </div>
                <div class="col-2 p-0">
                    @if ($title == 'Profile')
                        <a href="{{ route('user.profile.view') }}"
                           class="edit-btn">{{ customTrans('host_dashboard.edit') }}</a>
                    @elseif($title == customTrans('wishlist.wishlist_listing'))
                        @if ($wishlist->creater_id == $user_id)
                            <div class="wl-right-btn">
                                <button class="transparent-btn">
                                    <img src="{{ asset('icons/share-icon.svg') }}" alt="" id="share_link"
                                         data-bs-toggle="modal" data-bs-target="#wishlist-share-link">
                                </button>
                                <button class="transparent-btn btn-delete" data-bs-toggle="modal"
                                        data-bs-target="#delete-wishlist" data-wishlist-id="{{ $wishlist->id }}"
                                        data-wishlist-name="{{ $wishlist->name }}">
                                    <img src="{{ asset('icons/dots.svg') }}" alt="">
                                </button>
                            </div>
                        @endif
                        {{-- @elseif($title == "Profile Edit")
            <a href="">prof edit icon</a> --}}
                    @else
                    @endif
                </div>
            </div>
        </div>
    </header>
@elseif(request()->route()->uri == 'managehost/bookings/{booking}/reviews/create')
    <style>
        body.sp-ontop {
            margin-top: 0px !important;
        }
    </style>
    <header id="headerforPDF" class="nav-header nav-listing dubai-ff host-header">
        <div class="container-host">
            <div class="header-inner">
                <div class="row align-items-center justify-content-between">
                    <div class="col-6">
                        <div class="text-left logo-main-listing">
                            <a href="{{ route('home') }}" class="wht-logo">
                                <img src="{{ asset('logo/logo.svg') }}" height="auto" width="auto" loading="lazy"
                                     class="logo" alt="logo">
                            </a>
                        </div>

                    </div>

                    <div class="col-6 text-right">
                        @if ($routeName == 'managehost.guest.review.create')
                            <button class="review-se btn-save"
                                    id="saveExitButton">{{ customTrans('listing.save_exit') }}</button>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </header>
@else
    @if (request()->route()->getName() == 'home' ||
            request()->route()->getName() == 'property.single' || request()->route()->getName() == 'lp.riyadh')
        <div class="download-app-top">
            <div class="dap-icon">
                <img src="{{ asset('logo/logo-add.png') }}" alt="">
            </div>
            <div class="dap-content">
                <h3>{{ customTrans('header.downloadapp') }}</h3>
            </div>
            <div class="dap-down">
                @if (strstr(strtolower($_SERVER['HTTP_USER_AGENT']), 'iphone') ||
                        strstr(strtolower($_SERVER['HTTP_USER_AGENT']), 'ipad'))
                    <!-- iOS download link -->
                    <a href="https://apps.apple.com/us/app/darent/id1661536049" class="mobile_header_strip" target="_blank">
                        <button class="btn-download-app">{{ customTrans('header.download') }}</button>
                    </a>
                @else
                    <!-- Android download link -->
                    <a href="https://play.google.com/store/apps/details?id=com.darent" class="mobile_header_strip" target="_blank">
                        <button class="btn-download-app">{{ customTrans('header.download') }}</button>
                    </a>
                @endif

            </div>
            <div class="dap-closed">
                <i class="ri-close-line"></i>
            </div>
        </div>
    @endif
    @if (request()->route()->getName() == 'home' || request()->route()->getName() == 'lp.riyadh' || request()->route()->getName() == 'search')
        <header id="headerforPDF" class="nav-header">
            <div class="container">
                <div class="header-inner">
                    <div class="row align-items-center fl-wr">
                        <div class="col-xl-6 col-lg-6">
                            <form action="{{ url('search') }}" id="float-form">
                                <div class="float-hd">
                                    <a href="javascript:;" class="fl-menu">
                                        <div class="location-menu">
                                            <div class="icons"><i class="ri-search-line"></i></div>
                                        </div>
                                        @php
                                       
                                            // $checkin = carbonDate($checkin_session);
                                            // $checkout = carbonDate($checkout_session);
                                            
                                            // $checkin = isset($checkin_session) ? carbonDate($checkin_session) : ;// Carbon::now()->addDay()->format('m-d-Y');
                                            // $checkout = isset($checkout_session) ? carbonDate($checkout_session) : Carbon::now()->addDays(2)->format('m-d-Y');

                                            $checkin = isset($checkin_session) ? carbonDate($checkin_session) : Carbon::now()->format('m-d-Y');
                                            $checkout = isset($checkout_session) ? carbonDate($checkout_session) : Carbon::now()->addDays()->format('m-d-Y');
                                        @endphp
                                        <div class="lc-ad">
                                            <h5>{{ isset($location) ? (strlen($location) > 5 ? substr($location, 0, 14) . '...' : $location) : customTrans('header.select_location') }}
                                            </h5>

                                            <div class="ui-dt">
                                                <p>
                                                    @if(isset($checkin_session))
                                                    {{ customTrans('months.' . Carbon::parse($checkin)->format('M')) }}
                                                    /{{ Carbon::parse($checkin)->format('d') }}
                                                    -
                                                    {{ customTrans('months.' . Carbon::parse($checkout)->format('M')) }}
                                                    /{{ Carbon::parse($checkout)->format('d') }}
                                                    @else
                                                    
                                                    {{ customTrans('months.' . Carbon::createFromFormat('m-d-Y', $checkin)->format('M')) }}
                                                    /{{ Carbon::createFromFormat('m-d-Y', $checkin)->format('d') }}
                                                    -
                                                    {{ customTrans('months.' . Carbon::createFromFormat('m-d-Y', $checkout)->format('M')) }}
                                                    /{{ Carbon::createFromFormat('m-d-Y', $checkout)->format('d') }}
                                                    @endif
                                                    
                                                    
                                                </p>

                                                {{-- <p><span>{{ Carbon::createFromFormat('m-d-Y', $checkin)->format('M/d') . ' - ' . Carbon::createFromFormat('m-d-Y', $checkout)->format('M/d') }}</span> --}}
                                                </p>
                                                <p style="display:none">{{ customTrans('header.guest') }} {{ $guest ?? '' }}</p>
                                            </div>
                                        </div>
                                    </a>
                                    <div class="filter-btn">

                                        @if (request()->route()->getName() == 'search' || request()->route()->getName() == 'lp.riyadh')
                                            <button class="btn-fliter" type="button" id="priceFilter"
                                                    data-bs-toggle="dropdown" aria-expanded="false">
                                                <img src="{{ asset('icons/price-filter.svg') }}" alt="">

                                            </button>
                                            <button class="btn-fliter" type="button" data-bs-toggle="modal"
                                                    data-bs-target="#filter">
                                                <img src="{{ asset('icons/filter_search.svg') }}" alt="">
                                            </button>
                                        @else
                                            <a href="{{ route('search') }}">
                                                <button class="btn-fliter" type="button">
                                                    <i class="ri-send-plane-fill"></i>
                                                </button>
                                            </a>
                                        @endif
                                        <ul class="dropdown-menu custom-dropdown-menu" aria-labelledby="priceFilter">
                                            <li>
                                                <a class="dropdown-item priceFilter" data-value="asc"
                                                   href="javascript:;">{{ customTrans('sort.low_prices_first') }}</a>
                                                <a class="dropdown-item priceFilter" data-value="desc"
                                                   href="javascript:;">{{ customTrans('sort.high_prices_first') }}</a>
                                                <a class="dropdown-item priceFilter" data-value="mostRatedFirst"
                                                   href="javascript:;">{{ customTrans('sort.most_rated_first') }}</a>
                                                <a class="dropdown-item priceFilter" data-value="nearestToCity"
                                                   href="javascript:;">{{ customTrans('sort.newest_to_the_city') }}</a>
                                                <a class="dropdown-item priceFilter" data-value="recentlyAdded"
                                                   href="javascript:;">{{ customTrans('sort.recently_added') }}</a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>


                                {{-- swipe up menu for mobile --}}

                                <div class="float-menu">
                                    <a class="fl-menu cls-men"><i class="ri-close-fill"></i></a>
                                    <div class="stays">
                                        <div class="accordion" id="accordionExample">
                                            <div class="accordion-item">
                                                <h2 class="accordion-header" id="locatio">
                                                    <button class="accordion-button" type="button"
                                                            data-bs-toggle="collapse" data-bs-target="#collapseOne"
                                                            aria-expanded="true" aria-controls="collapseOne">
                                                        {{ customTrans('header.where_to_q') }}
                                                    </button>
                                                </h2>
                                                <div id="collapseOne" class="accordion-collapse collapse show"
                                                     aria-labelledby="locatio" data-bs-parent="#accordionExample"
                                                     style=" transition: 0.3s ease;">
                                                    <div>
                                                        <input type="hidden" name="location"
                                                               class="form-control rest-inp" id="front-search-field"
                                                               autocomplete="off"
                                                               value="{{ $location ?? $default_city }}"
                                                               placeholder="{{ customTrans('home.where_want_to_go') }}">
                                                    </div>
                                                    <div class="accordion-body">
                                                        <div class="search-filter-select">
                                                            <select class="selectpicker search-filter-select show-tick"
                                                                    data-live-search="true" data-size="3"
                                                                    data-live-search-placeholder="Search Location"
                                                                    id="city_select">
                                                                @if (isset($location))
                                                                    @foreach ($cities as $city)
                                                                        <option value="{{ $city->name }}"
                                                                            {{ $location == $city->name ? 'Selected' : '' }}>
                                                                            {{ $lang == 'ar' ? $city->name_ar : $city->name }}
                                                                        </option>
                                                                    @endforeach
                                                                @else
                                                                    @foreach ($cities as $city)
                                                                        <option value="{{ $city->name }}"
                                                                            {{ $default_city == $city->name ? 'Selected' : '' }}>
                                                                            {{ $lang == 'ar' ? $city->name_ar : $city->name }}
                                                                        </option>
                                                                    @endforeach
                                                                @endif
                                                            </select>
                                                        </div>
                                                        {{-- <div>
                                                            <input type="text" name="location"
                                                                class="form-control rest-inp" id="front-search-field"
                                                                autocomplete="off"
                                                                value="{{ $location ?? 'Saudi Arabia' }}"
                                                                placeholder="{{ customTrans('home.where_want_to_go') }}">
                                                        </div> --}}
                                                        {{-- <div class="location-bx for-eng bx-en">
                                                            @forelse ($popular_cities as $city)
                                                                <div class="inner-main pop-img popularcity locui"
                                                                    data-cityname="{{ $city->name }}">
                                                                    <a>
                                                                        <div>
                                                                            <img loading="lazy"
                                                                                src="{{ $city->image_url }}"
                                                                                height="auto" width="auto"
                                                                                alt="{{ $city->name }}">
                                                                            <p>{{ app()->getLocale() == 'ar' ? $city->name_ar : $city->name }}
                                                                            </p>
                                                                        </div>
                                                                    </a>
                                                                </div>
                                                            @empty
                                                                <p>No location for now </p>
                                                            @endforelse
                                                        </div> --}}
                                                        {{-- <div class="location-bx for-ar bx-ar">
                                                            @forelse ($popular_cities as $city)
                                                                <div class="inner-main pop-img popularcity locui"
                                                                    data-cityname="{{ $city->name }}">
                                                                    <a>
                                                                        <div>
                                                                            <img loading="lazy"
                                                                                src="{{ $city->image_url }}"
                                                                                height="auto" width="auto"
                                                                                alt="{{ $city->name }}">
                                                                            <p>{{ app()->getLocale() == 'ar' ? $city->name_ar : $city->name }}
                                                                            </p>
                                                                        </div>
                                                                    </a>
                                                                </div>
                                                            @empty
                                                                <p>No location for now </p>
                                                            @endforelse
                                                        </div> --}}
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="accordion-item">
                                                <h2 class="accordion-header" id="date-range">
                                                    <button class="accordion-button collapsed" type="button"
                                                            data-bs-toggle="collapse" data-bs-target="#collapseTwo"
                                                            aria-expanded="false" aria-controls="collapseTwo">
                                                        {{ customTrans('header.when_q') }}
                                                    </button>
                                                </h2>
                                                <div id="collapseTwo" class="accordion-collapse collapse"
                                                     aria-labelledby="date-range" data-bs-parent="#accordionExample"
                                                     style=" transition: 0.3s ease;">
                                                    <div class="accordion-body">
                                                        <div class="date-clendar-mb">
                                                            <div class="ui-calendar">
                                                                {{-- <input type="integer" class="date-ui form-control" placeholder="Select Date" readonly> --}}
                                                                <input type="text" name="headerdaterange"
                                                                       placeholder="Date"
                                                                       class="date-ui form-control rest-inp"
                                                                       value="{{ $checkin . ' - ' . $checkout }}"
                                                                       readonly="true"/>
                                                                <input type="hidden" name="checkin" id="startDate"
                                                                       class="rest-inp" value="{{ $checkin }}">
                                                                <input type="hidden" name="checkout" id="endDate"
                                                                       class="rest-inp" value="{{ $checkout }}">
                                                                <i class="ri-calendar-check-line"></i>
                                                            </div>
                                                            <div
                                                                class="view-calendar bt-grey-scroller cust-double-rangepicker">

                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="accordion-item" style="display:none">
                                                <h2 class="accordion-header" id="guest-list">
                                                    <button class="accordion-button collapsed" type="button"
                                                            data-bs-toggle="collapse" data-bs-target="#collapseThree"
                                                            aria-expanded="false" aria-controls="collapseThree">
                                                        {{ customTrans('header.guest_q') }}
                                                    </button>
                                                </h2>
                                                <div id="collapseThree" class="accordion-collapse collapse"
                                                     aria-labelledby="guest-list" data-bs-parent="#accordionExample"
                                                     style=" transition: 0.3s ease;">
                                                    <div class="accordion-body">
                                                        <div class="total-guest">
                                                            <div class="content">
                                                                <h4>{{ customTrans('header.adult') }}</h4>
                                                                <p>{{ customTrans('header.more_than_13_y') }}</p>
                                                            </div>
                                                            <div class="guest-counter">
                                                                <span class="minus"><img
                                                                        src="{{ asset('icons/minus.svg') }}"
                                                                        height="auto" width="auto" loading="lazy"
                                                                        alt="minus icon"></span>
                                                                <input type="number" class="clickdisabled"
                                                                       name="adult_guest"
                                                                       value="{{ $adult_guest ?? 1 }}" readonly/>
                                                                <span class="plus" data-limit="100"><img
                                                                        src="{{ asset('icons/plus.svg') }}"
                                                                        height="auto" width="auto" loading="lazy"
                                                                        alt="plus icon"></span>
                                                            </div>
                                                        </div>
                                                        <div class="total-guest">
                                                            <div class="content">
                                                                <h4>{{ customTrans('header.children') }}</h4>
                                                                <p>{{ customTrans('header.less_than_13_y') }}</p>
                                                            </div>
                                                            <div class="guest-counter">
                                                                <span class="minus"><img
                                                                        src="{{ asset('icons/minus.svg') }}"
                                                                        height="auto" width="auto" loading="lazy"
                                                                        alt="minus icon"></span>
                                                                <input type="number" class="clickdisabled"
                                                                       name="child_guest"
                                                                       value="{{ $child_guest ?? 0 }}" readonly/>
                                                                <span class="plus" data-limit="100"><img
                                                                        src="{{ asset('icons/plus.svg') }}"
                                                                        loading="lazy" height="auto" width="auto"
                                                                        alt="plus icon"></span>
                                                            </div>
                                                            <input type="hidden" name="guest"
                                                                   id='front-search-guests' value="{{ $guest ?? 1 }}">
                                                        </div>

                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        {{-- popular location end --}}
                                        <div class="browse-language">

                                            @foreach ($language as $key => $value)
                                                <a class="language_footer {{ Session::get('language') == $key ? 'ar' : '' }}"
                                                   data-lang="{{ $key }}">
                                                    <div class="br-icon">
                                                        <i class="fas fa-globe-americas"></i>
                                                    </div>
                                                    <div class="br-cont">
                                                        @if ($value == 'English')
                                                            <h5 class="mb-0">Browse in English</h5>
                                                            {{-- <h5>{{ customTrans('account_mobile.browse_in') }} {{ $value }}</h5> --}}
                                                        @else
                                                            <h5 class="mb-0">تصفح بالعربية</h5>
                                                        @endif
                                                        {{-- <span>{{ customTrans('account_mobile.browse_in') }}</span>
                                                        <h5>{{ $value }}</h5> --}}

                                                    </div>
                                                </a>
                                            @endforeach

                                        </div>


                                        <div class="ui-search">
                                            <button type="button"
                                                    class="grey-btn rst-form">
                                                <span>{{ customTrans('header.clear_filter') }}</span></button>
                                            <button type="submit"
                                                    class="theme-btn"><span>{{ customTrans('header.explore') }}</span>
                                            </button>
                                        </div>
                                    </div>

                                </div>
                                {{-- swipe up menu for mobile end --}}


                            </form>
                            <ul class="product-category">
                                @php
                                    $ptype = $_GET['property_type'] ?? null;
                                    // dd($ptype);
                                @endphp
                                @forelse ($property_type as $type)
                                    @if (request()->route()->getName() == 'search' || request()->route()->getName() == 'lp.riyadh')
                                        <li class='property_types_image inr-ck'>
                                            <label class="listing-checkbox-wrapper inner-chck">
                                                <input hidden type="checkbox" class='listing-checkbox-input'
                                                       name="property_type[]" value="{{ $type->id }}"
                                                    {{ $ptype == $type->id ? 'checked' : '' }} id="property_type_{{ $type->id }}" />
                                                <div class="listing-checkbox-tile">
                                                    <div class="listing-checkbox-icon">
                                                        <img src="{{ asset('icons/' . $type->icon_image) }}"
                                                             alt="">
                                                    </div>
                                                    <div class="listing-checkbox-label">
                                                        <p>{{ app()->getLocale() == 'ar' ? $type->name_ar : $type->name }}
                                                        </p>
                                                    </div>
                                                </div>
                                            </label>
                                        </li>
                                    @else
                                        <a href="{{ URL::to('/') }}/search?property_type={{ $type->id }}">
                                            <li class='property_types_image'>
                                                <span>
                                                    <img src="{{ asset('icons/' . $type->icon_image) }}"
                                                         height="auto" width="auto" alt="...">
                                                    <input hidden type="checkbox"
                                                           class='property_types_checkbox listing-checkbox-input'
                                                           name="property_type[]" value="{{ $type->id }}" />
                                                    <p>{{ app()->getLocale() == 'ar' ? $type->name_ar : $type->name }}
                                                    </p>
                                                </span>
                                            </li>
                                        </a>
                                    @endif
                                @empty
                                    <p>No types available</p>
                                @endforelse
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </header>
    @else
        <header id="headerforPDF" class="nav-header inner-header">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-2">
                        <div class="page-back">
                            <a href="{{ url()->previous() }}">
                                <button class="go-back">
                                    <img src="{{ asset('../icons/go-back.svg') }}" alt=""
                                         class="rtl-leftArrowRotat">
                                </button>
                            </a>
                        </div>
                    </div>
                    <div class="col-8">
                        <div class="head-content">
                            @php
                                $title = isset($title) ? $title : 'Darent';
                            @endphp
                            <h3>{{ $title }}</h3>
                        </div>
                    </div>
                    <div class="col-2 p-0">
                        @if ($title == 'Profile')
                            <a href="{{ route('user.profile.view') }}"
                               class="edit-btn">{{ customTrans('host_dashboard.edit') }}</a>
                        @elseif($title == customTrans('wishlist.wishlist_listing'))
                            @if ($wishlist->creater_id == $user_id)
                                <div class="wl-right-btn">
                                    <button class="transparent-btn">
                                        <img src="{{ asset('icons/share-icon.svg') }}" alt=""
                                             id="share_link" data-bs-toggle="modal"
                                             data-bs-target="#wishlist-share-link">
                                    </button>
                                    <button class="transparent-btn btn-delete" data-bs-toggle="modal"
                                            data-bs-target="#delete-wishlist" data-wishlist-id="{{ $wishlist->id }}"
                                            data-wishlist-name="{{ $wishlist->name }}">
                                        <img src="{{ asset('icons/dots.svg') }}" alt="">
                                    </button>
                                </div>
                            @endif
                            {{-- @elseif($title == "Profile Edit")
                            <a href="">prof edit icon</a> --}}
                        @else
                        @endif
                    </div>
                </div>
            </div>
        </header>
    @endif
@endif
@if($group[0] == 'managehost' || (isset($type) && $type == 'host'))
    @if (!request()->is('managehost/instruction'))
        @php($unVerifiedProperties = getUnverifiedPropertiesCount('web'))
        {{-- @if ($unVerifiedProperties > 0)
            <div class="property-license-alert">
                <div class="container">
                    <div class="d-flex align-items-center gap-4 flex-column">
                        <img src="{{ asset('icons/alert-black.svg') }}" alt="">
                        <div class="">
                            <h5>{{ $unVerifiedProperties }} {{ customTrans('host_listing.properties_need_license') }}</h5>
                            <p class="mb-0">
                                {{ str_replace(':unVerifiedProperties', $unVerifiedProperties, customTrans('host_listing.please_ensure_verifying_properties')) }}<a
                                    href="{{ route('managehost.instruction') }}" class="">{{ customTrans('host_listing.learn_more') }}</a>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        @endif --}}
    @endif
@endif
<script>
    var darentKey = "in~311c4723";
</script>
@if (strtolower(env('APP_ENV')) == 'prod')
    <script>
        darentKey = "in~58adcbb5";
    </script>
@endif
{{-- WebEngage --}}
<script id='_webengage_script_tag' type='text/javascript'>
    var webengage;
    !function (w, e, b, n, g) {
        function o(e, t) {
            e[t[t.length - 1]] = function () {
                r.__queue.push([t.join("."),
                    arguments
                ])
            }
        }

        var i, s, r = w[b],
            z = " ",
            l = "init options track screen onReady".split(z),
            a = "webPersonalization feedback survey notification notificationInbox".split(z),
            c = "options render clear abort".split(z),
            p = "Prepare Render Open Close Submit Complete View Click".split(z),
            u = "identify login logout setAttribute".split(z);
        if (!r || !r.__v) {
            for (w[b] = r = {
                __queue: [],
                __v: "6.0",
                user: {}
            }, i = 0; i < l.length; i++) o(r, [l[i]]);
            for (i = 0; i < a.length; i++) {
                for (r[a[i]] = {}, s = 0; s < c.length; s++) o(r[a[i]], [a[i], c[s]]);
                for (s = 0; s < p.length; s++) o(r[a[i]], [a[i], "on" + p[s]])
            }
            for (i = 0; i < u.length; i++) o(r.user, ["user", u[i]]);
            setTimeout(function () {
                var f = e.createElement("script"),
                    d = e.getElementById("_webengage_script_tag");
                f.type = "text/javascript", f.async = !0, f.src = ("https:" == e.location.protocol ?
                        "https://widgets.in.webengage.com" : "http://widgets.in.webengage.com") +
                    "/js/webengage-min-v-6.0.js", d.parentNode.insertBefore(f, d)
            })
        }
    }(window, document, "webengage");
    webengage.init(darentKey);

    const search_form = document.getElementById('float-form')
if(search_form){
    search_form.addEventListener('submit', function(e) {
        e.preventDefault()

        let checkin = new Date($('#startDate').val())
        let checkout = new Date($('#endDate').val())

        // trackEvent('search', {
        //     search_term: $('#front-search-field').val(),
        //     search_month: checkin.toLocaleString('default', { month: 'long' }),
        //     search_duration: Math.round((checkout.getTime() - checkin.getTime()) / (1000 * 3600 * 24)),
        //     email: '{{ auth()->user()?->email }}',
        //     phone: '{{ auth()->user()?->phone }}'
        // }, 'ga')

        // trackEvent('Search', {
        //     contents: [],
        //     currency: '{{Session::get('currency')}}',
        //     search_month: checkin.toLocaleString('default', { month: 'long' }),
        //     search_duration: Math.round((checkout.getTime() - checkin.getTime()) / (1000 * 3600 * 24)),
        //     item_category: 'product',
        //     query: $('#front-search-field').val(),
        //     email: '{{ auth()->user()?->email }}',
        //     phone: '{{ auth()->user()?->phone }}'
        // }, ['tik'])

        // trackEvent('SEARCH', {
        //     search_month: checkin.toLocaleString('default', { month: 'long' }),
        //     search_duration: Math.round((checkout.getTime() - checkin.getTime()) / (1000 * 3600 * 24)),
        //     item_category: 'product',
        //     search_string: $('#front-search-field').val(),
        //     user_email: '{{ auth()->user()?->email }}',
        //     user_phone_number: '{{ auth()->user()?->phone }}'
        // }, ['snap'])

        search_form.submit()
    })
}
</script>
{{-- WebEngage --}}

<!--================Header Menu Area =================-->
<div class="flash-container">
    @if (Session::has('message'))
        <div class="alert {{ Session::get('alert-class') }} text-center mb-0" role="alert">
            {{ Session::get('message') }}
            <a href="#" class="pull-right" class="alert-close" data-bs-dismiss="alert">&times;</a>
        </div>
    @endif

    <div class="alert alert-success text-center mb-0 d-none" id="success_message_div" role="alert">
        <a href="#" class="pull-right" class="alert-close" data-bs-dismiss="alert">&times;</a>
        <p id="success_message"></p>
    </div>

    <div class="alert alert-danger text-center mb-0 d-none" id="error_message_div" role="alert">
        <p><a href="#" class="pull-right" class="alert-close" data-bs-dismiss="alert">&times;</a></p>
        <p id="error_message"></p>
    </div>
</div>
