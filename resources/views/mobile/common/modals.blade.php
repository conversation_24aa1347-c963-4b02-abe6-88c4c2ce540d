@push('css')
@endpush

@push('css')
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-select@1.13.14/dist/css/bootstrap-select.min.css">
@endpush

<div class="modal fade modal-dr-bottom {{ request()->route()->getName() == 'paymentCallback' ? 'after-payment-ilmyakeen' : '' }}"
    id="ilmyakeen" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="cancelLabel"
    aria-hidden="true">
    <div class="modal-dialog custom-modal-dialog modal-dialog-centered">
        <div class="modal-content custom-modal-content">
            <div class="modal-header  custom-small-modal-header">
                @if(isset($hide_close) && !$hide_close)
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                    </button>
               @endif
            </div>
            <div class="modal-body">
                <div class="alert-modal">
                    <img src="{{ asset('icons/yaqeen_logo.jpg') }}" alt="" class="mb-5">
                    <form onsubmit="verifyUser(event)">
                        @csrf
                        <div class="row mb-4">
                            <div class="col-12">
                                <label for="forarabic" class="hs-ls-it ilq-chk">
                                    <div>
                                        <input class="form-check-input cust-form-check-radio-input forarabic"
                                            type="radio" value="{{ customTrans('listing.saudi') }}" name="ilmeyakeen"
                                            id="forarabic" checked data-target="hijri-calendar-saudi"
                                            onclick="changeTypeCal()">
                                    </div>
                                    <div class="hs-ls-item">
                                        <h4 class="ilm-chk-title">
                                            {{ customTrans('listing.saudi') }}
                                        </h4>
                                    </div>
                                </label>
                            </div>
                            <div class="col-12 mt-2">
                                <label for="nonearabic" class="hs-ls-it ilq-chk">
                                    <div>
                                        <input class="form-check-input cust-form-check-radio-input nonearabic"
                                            type="radio" value="{{ customTrans('listing.nonsaudi') }}"
                                            name="ilmeyakeen" id="nonearabic" data-target="hijri-calendar-saudi"
                                            onclick="changeTypeCal(false)">
                                    </div>
                                    <div class="hs-ls-item">
                                        <h4 class="ilm-chk-title">
                                            {{ customTrans('listing.nonsaudi') }}
                                        </h4>

                                    </div>
                                </label>
                            </div>
                        </div>
                        <div id="hijri-calendar-saudi" class="hijri-calendar-check">
                            <input type="text" id="elm-code" name="code"
                                onKeyDown="if(this.value.length==10 && event.keyCode!=8) return false;"
                                class="form-control numberInput" placeholder="{{ customTrans('listing.niniqama') }}">
                            <div class="single-check mb-4 mt-2">
                                <input type="text" id="elm-dob" name="dob"
                                    class="form-control hij-cln hijri-date-input"
                                    placeholder="{{ customTrans('listing.hijricalendar') }}" autocomplete="off">
                                <div class="check-icon cy-positon md-clndr-icon">
                                    <img src="{{ asset('icons/calender.svg') }}" alt="">
                                </div>
                            </div>
                        </div>
                        <p class="mb-4 iqama-support-contact">
                            {{ customTrans('general.having_difficulty_to_verify') }}
                            <a href="https://api.whatsapp.com/send?phone=966920033870&text=Hi" target="_blank">
                                <i class="fab fa-whatsapp"></i>
                                {{ customTrans('header.contact_us') }}
                            </a>
                        </p>
                        <p class="mb-4"> {{ customTrans('listing.providing_your_id') }} <a
                                href="{{ route('privacyPolicy', ['lang' => App::getLocale()]) }}">{{ customTrans('footer.privacy_policy') }}</a>
                        </p>

                        <div>
                            <button type="submit"
                                class="theme-btn w-100  verifyIqamaBtn">{{ customTrans('listing.save') }}</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- verify elm with fields -->

@if (!auth()->check())

    {{-- New Sign Up --}}
    <div class="modal fade dubai-ff modal-dr-bottom" id="signup" data-bs-keyboard="false" tabindex="-1"
        aria-labelledby="signupLabel" aria-hidden="true">
        <div class="modal-dialog custom-modal-dialog modal-dialog-centered">
            <div class="modal-content custom-modal-content cm-simple-content">
                <div class="modal-header cm-simple-header">
                    <button type="button" class="transparent-btn md-back-btn" id="" data-bs-toggle="modal"
                        data-bs-target="#enterotp">
                        <img src="{{ asset('icons/black-larrow.svg') }}" alt="" class="rtl-leftArrowRotat">
                    </button>
                    <h3 class="w-100 text-center" id="signupLabel">
                        {{ customTrans('sign_up.sign_up') }}
                    </h3>
                </div>
                <div class="modal-body cm-simple-body">
                    <div class="phone-verification">
                        <form id="signUpForm" action="" method="POST">
                            {{ csrf_field() }}
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="after-end">
                                        <input type="text" id="reg-first-name" name="first_name"
                                            class="form-control md-input"
                                            placeholder="{{ customTrans('sign_up.first_name') }}">
                                    </div>
                                    <div class="reg-first-name er-mg"></div>
                                    <span id="reg-first-nameError" class="text-danger"></span>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="after-end">
                                        <input type="text" id="reg-last-name" name="last_name"
                                            class="form-control md-input"
                                            placeholder="{{ customTrans('sign_up.last_name') }}">
                                    </div>
                                    <div class="reg-last-name er-mg"></div>
                                    <span id="reg-last-nameError" class="text-danger"></span>
                                </div>
                                <div class="col-md-12 mb-3">
                                    <div class="after-end">
                                        <input type="email" id="reg-email" name="email"
                                            class="form-control md-input"
                                            placeholder="{{ customTrans('sign_up.your_email') }}">
                                    </div>
                                    <div class="reg-email er-mg"></div>
                                    <span id="reg-emailError" class="text-danger"></span>

                                </div>
                                <div class="col-md-12 mb-3">
                                    <div class="after-end">
                                        <div class="phone-number">
                                            <input id="phone-input-signup-mobile" name="phone" type="tel"
                                                value="" disabled />
                                            <input type="hidden" name="cohost" id="cohost_token" />
                                            <input type="hidden" name="token" id="phone_token" />
                                        </div>
                                        <div class="reg-phone phone-er" id="reg-phone">
                                        </div>
                                        <span id="reg-phoneError" class="text-danger"></span>

                                    </div>
                                </div>
                                {{-- <div class="col-md-12 mb-3">
                                    <div class="after-end">
                                        <input type="password" id="reg-password" name="password"
                                            class="form-control md-input"
                                            placeholder="{{ customTrans('sign_up.password') }}">
                                    </div>
                                    <div class="reg-password er-mg"></div>
                                    <span id="reg-passwordError" class="text-danger"></span>

                                </div> --}}
                                {{-- <div class="col-md-12 mb-0">
                                    <div class="after-end">
                                        <input type="password" id="reg-password-confirmation"
                                            name="password_confirmation" class="form-control md-input"
                                            placeholder="{{ customTrans('login.confirm_password') }}">
                                    </div>
                                    <div class="reg-password-confirmation er-mg"></div>
                                    <span id="reg-passwordConfirmError" class="text-danger"></span>
                                </div> --}}
                            </div>
                            <div class="col-md-12  text-center">
                                <div class="loadergif btn-ld mg-20 d-none">
                                    {{-- <img src="{{ asset('icons/loader.gif') }}" class=""> --}}
                                    <div class="dots"></div>
                                </div>
                                <p class="text-start mx-2 my-3">By submitting, you admit that you agree Our <a
                                        href="{{ route('privacyPolicy', ['lang' => App::getLocale()]) }}">
                                        {{ customTrans('sign_up.term_condition') }}</a>
                                </p>
                                <button id="signup_btn" type="submit"
                                    class="theme-btn w-100 signup_submit">{{ customTrans('sign_up.submit') }}</button>
                            </div>

                            {{-- <div class="social-signup-btn social-btn-login bd-top">
                                <a href="{{ route('user.google.redirect') }}" class="mb-2">
                                    <button type="button" class="bg-transparent-btn">
                                        <img src="{{ asset('icons/google.svg') }}" alt="">
                                        <div class="content">
                                            <h6>
                                                {{ customTrans('sign_up.login_with') }}
                                                <span>{{ customTrans('sign_up.google') }}</span>
                                            </h6>
                                        </div>
                                    </button>
                                </a>
                                <a href="#">
                                    <button type="button" class="bg-transparent-btn" data-bs-dismiss="modal"
                                        aria-label="Close" data-bs-toggle="modal" data-bs-target="#staticBackdrop">
                                        <img src="{{ asset('icons/user.svg') }}" class="al-account log-user-wd"
                                            alt="">
                                        <div class="content">
                                            <h6>
                                                {{ customTrans('sign_up.already_have') }}
                                                <span>{{ customTrans('header.login') }}</span>
                                            </h6>
                                        </div>
                                    </button>
                                </a>
                            </div> --}}
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {{-- Login & Veification Modal --}}
    {{-- <div class="modal fade dubai-ff" id="staticBackdrop" data-bs-backdrop="static" data-bs-keyboard="false"
        tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
        <div class="modal-dialog custom-modal-dialog modal-dialog-centered">
            <div class="modal-content cm-simple-content">
                <div class="modal-header cm-simple-header">
                    <h3 class="w-100 mb-0" id="staticBackdropLabel">
                        {{ customTrans('sign_up.welcome_darent') }}</h3>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                    </button>
                </div>
                <div class="modal-body cm-simple-body">
                    <div class="phone-verification">
                        <form onSubmit="onLogin(event)">
                            @csrf
                            <div id="fields" class="after-end">
                                <div class="phone-number ht" id="phone">
                                    <select class="" id="phone-prepend" aria-label="Default select example">
                                        <option value="966" selected>KSA (+966)</option>

                                    </select>
                                    <input type="number" name="phone" class="numberInput"
                                        placeholder="000 000 000"
                                        onKeyDown="if(this.value.length==9 && event.keyCode!=8) return false;">
                                </div>
                                <span class="invalid-feedback">phone is required.</span>
                            </div>
                            <div class="col-md-12  text-center">
                                <div class="loadergif btn-ld mg-20 d-none">
                                    <img src="{{ asset('icons/loader.gif') }}" class="">
                                </div>
                                <button type="submit"
                                    class="theme-btn w-100 phone-btn signinbtn">{{ customTrans('sign_up.submit') }}</button>
                            </div>
                            <p class="mb-2">{{ customTrans('sign_up.by_submitting_your_number') }} <a
                                    href="{{ route('privacyPolicy') }}">{{ customTrans('sign_up.term_condition') }}</a>
                            </p>
                            <div class="social-signup-btn social-btn-login bd-top">
                                <a href="{{ route('user.google.redirect') }}" class="mb-2">
                                    <button type="button" class="bg-transparent-btn">
                                        <img src="{{ asset('icons/google.svg') }}" alt="">
                                        <div class="content">
                                            <h6>
                                                {{ customTrans('sign_up.login_with') }}
                                                <span>{{ customTrans('sign_up.google') }}</span>
                                            </h6>
                                        </div>
                                    </button>
                                </a>
                                <a href="#">
                                    <button type="button" class="bg-transparent-btn" onClick="changeFields()">
                                        <i class="far fa-envelope"></i>
                                        <div class="content">
                                            <h6>
                                                {{ customTrans('sign_up.login_with') }}
                                                <span id="btn-txt">{{ customTrans('sign_up.email') }}</span>
                                            </h6>
                                        </div>
                                    </button>
                                </a>

                                 <a href=""
                                    class="guest-btn d-flex align-items-center justify-content-center mt-1">Complete as
                                    a guest</a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div> --}}

    {{-- New Login With Otp --}}
    <div class="modal fade dubai-ff modal-dr-bottom" id="staticBackdrop" data-bs-backdrop="static"
        data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
        <link rel="stylesheet" href="{{ asset('intlInput/css/intlTelInput.css') }}">
        <style>
            .iti {
                --iti-border-color: #5b5b5b;
                --iti-dialcode-color: #999999;
                --iti-dropdown-bg: #ffffff;
                --iti-arrow-color: #aaaaaa;
                --iti-hover-color: #eeeeee;
                --iti-path-globe-1x: url("../img/globe_light.webp");
                --iti-path-globe-2x: url("../img/<EMAIL>");
            }

            #phone-input-mobile,
            #phone-input-signup-mobile {
                border: none;
            }
        </style>
        <div class="modal-dialog custom-modal-dialog modal-dialog-centered">
            <div class="modal-content cm-simple-content">
                <div class="modal-header cm-simple-header">
                    <h3 class="w-100 mb-0" id="staticBackdropLabel">
                        {{ customTrans('sign_up.welcome_darent') }}</h3>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                    </button>
                </div>
                <div class="modal-body cm-simple-body">
                    <div class="phone-verification">
                        <form onSubmit="sendOtp(event)">
                            @csrf
                            <div id="fields" class="after-end">
                                <div class="phone-number ht" id="phone">
                                    <input id="phone-input-mobile" name="phone" type="tel" value="" />
                                    {{--                                    @if (strtolower(env('APP_ENV')) == 'local') --}}
                                    {{--                                        <select class="" id="phone-prepend" --}}
                                    {{--                                            aria-label="Default select example"> --}}
                                    {{--                                            --}}{{-- <option value="+92" selected>PK (+92)</option> --}}
                                    {{--                                        </select> --}}
                                    {{--                                        <div id="phone-container"> --}}
                                    {{--                                            <input type="number" name="phone" class="numberInput" --}}
                                    {{--                                                placeholder="56 966 6966" --}}
                                    {{--                                                onKeyDown="if(this.value.length==10 && event.keyCode!=8) return false;"> --}}
                                    {{--                                        </div> --}}
                                    {{--                                    @else --}}
                                    {{--                                        <select class="" id="phone-prepend" --}}
                                    {{--                                            aria-label="Default select example"> --}}
                                    {{--                                            --}}{{-- <option value="966" selected>KSA (+966)</option> --}}
                                    {{--                                        </select> --}}
                                    {{--                                        <div id="phone-container"> --}}
                                    {{--                                            --}}{{-- <input type="number" name="phone" class="numberInput" --}}
                                    {{--                                                placeholder="56 966 6966" --}}
                                    {{--                                                onKeyDown="if(this.value.length==9 && event.keyCode!=8) return false;"> --}}
                                    {{--                                        </div> --}}
                                    {{--                                    @endif --}}
                                </div>
                                <span id="phoneError"></span>
                                <p class="mb-3">{{ customTrans('sign_up.by_submitting_your_number') }} <a
                                        href="{{ route('privacyPolicy', ['lang' => App::getLocale()]) }}"
                                        target="_blank">{{ customTrans('sign_up.term_condition') }}</a>
                                </p>
                            </div>
                            {{-- <span class="" id="error-span"></span> --}}
                            <div class="col-md-12  text-center">
                                <div class="loadergif btn-ld mg-20 d-none">
                                    {{-- <img src="{{ asset('icons/loader.gif') }}" class=""> --}}
                                    <div class="dots"></div>
                                </div>
                                {{-- <button type="submit"
                                    class="theme-btn w-100 phone-btn sendotpbtn"> {{ customTrans('sign_up.submit') }}
                                </button> --}}

                                <button type="submit" class="theme-btn w-100 phone-btn sendotpbtn">
                                    {{ customTrans('property.continue') }}
                                </button>
                            </div>
                            {{-- <div class="social-signup-btn social-btn-login bd-top">
                                <a href="{{ route('user.google.redirect') }}" class="mb-2">
                                    <button type="button" class="bg-transparent-btn">
                                        <img src="{{ asset('icons/google.svg') }}" alt="">
                                        <div class="content">
                                            <h6>
                                                {{ customTrans('sign_up.login_with') }}
                                                <span>{{ customTrans('sign_up.google') }}</span>
                                            </h6>
                                        </div>
                                    </button>
                                </a>
                                <a href="#">
                                    <button type="button" class="bg-transparent-btn" onClick="changeFields()">
                                        <i class="far fa-envelope"></i>
                                        <div class="content">
                                            <h6>
                                                {{ customTrans('sign_up.login_with') }}
                                                <span id="btn-txt">{{ customTrans('sign_up.email') }}</span>
                                            </h6>
                                        </div>
                                    </button>
                                </a>
                                 <a href=""
                                    class="guest-btn d-flex align-items-center justify-content-center mt-1">Complete as
                                    a guest</a>
                            </div> --}}
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {{-- User Inactive From Social Login Modal --}}
    <div class="modal fade" id="inactiveuser" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1"
        aria-labelledby="inactiveuserLabel" aria-hidden="true">
        <div class="modal-dialog custom-modal-dialog custom-small-modal-width modal-dialog-centered">
            <div class="modal-content custom-modal-content">
                <div class="modal-header  custom-small-modal-header">

                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                    </button>
                </div>
                <div class="modal-body">
                    <div class="alert-modal">
                        <img src="{{ asset('icons/cancel.svg') }}" alt="" class="mb-3">
                        <h3 class="text-danger mb-4 fw-600 ">Error</h3>
                        <p class="mb-4">User Is Inactive</p>
                        {{-- <a href="#">
                        <button class="theme-btn btn-danger w-100">Confirm</button>
                    </a> --}}
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Veification ENTER OTP --}}
    <div class="modal fade modal-dr-bottom" id="enterotp" data-bs-keyboard="false" tabindex="-1"
        aria-labelledby="enterotpLabel" aria-hidden="true">
        <div class="modal-dialog custom-modal-dialog modal-dialog-centered">
            <div class="modal-content cm-simple-content">
                <div class="modal-header cm-simple-header">
                    <button type="button" class="transparent-btn md-back-btn er-null-btn" id=""
                        data-bs-toggle="modal" data-bs-target="#staticBackdrop">
                        <img src="{{ asset('icons/black-larrow.svg') }}" alt="" class="rtl-leftArrowRotat">
                    </button>
                    <h4 class="w-100 mb-0 text-center" id="enterotpLabel">
                        {{ customTrans('sign_up.confirm_your_phone') }}
                    </h4>
                </div>
                <div class="modal-body cm-simple-body">
                    <div class="phone-verification">
                        <form onSubmit="verifyOtp(event)">
                            {{ csrf_field() }}
                            <p class="otp-in-text">
                                {{ customTrans('sign_up.enter_code_we_sent') }}: <span id="display_phone"></span>
                            </p>
                            {{-- <fieldset class="verification-field" id="code">
                           <input type="text" class="verification-input" id="otp-1" name="otpfield"
                               placeholder="-" maxlength="1" />
                           <input type="text" class="verification-input" id="otp-2" name="otpfield"
                               placeholder="-" maxlength="1" />
                           <input type="text" class="verification-input" id="otp-3" name="otpfield"
                               placeholder="-" maxlength="1" />
                           <input type="text" class="verification-input" id="otp-4" name="otpfield"
                               placeholder="-" maxlength="1" />
                           <input type="hidden" name="phone" id="otp_phone" />
                           <input type="hidden" name="otp" id="otp" />
                       </fieldset> --}}


                            <div class="verification-code" id="code">
                                <div class="otp-input-wrapper">
                                    <input type="number" maxlength="4" class="otp-input" pattern="[0-9]*"
                                        name="otpfield" autocomplete="on" placeholder="- - - -">

                                </div>
                                {{-- <div class="verification-code--inputs">
                               <input type="number"  id="otp-1" name="otpfield"
                               placeholder="-" maxlength="1" />
                           <input type="number"  id="otp-2" name="otpfield"
                               placeholder="-" maxlength="1" />
                           <input type="number"  id="otp-3" name="otpfield"
                               placeholder="-" maxlength="1" />
                           <input type="number"  id="otp-4" name="otpfield"
                               placeholder="-" maxlength="1" />
                           </div> --}}
                                <input type="hidden" name="phone" id="otp_phone" />
                                <input type="hidden" name="otp" id="otp" />
                            </div>
                            <span id="otpError" class="er-null"></span>
                            <div class="col-md-12  text-center">
                                <div class="loadergif btn-ld mg-20 d-none">
                                    {{-- <img src="{{ asset('icons/loader.gif') }}" class=""> --}}
                                    <div class="dots"></div>
                                </div>
                                <button type="submit"
                                    class="theme-btn w-100 phone-btn otpverify mt-3">{{ customTrans('property.verify_otp_continue') }}</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endif

<!-- Error  Modal -->
<div class="modal fade" id="verifyelm" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1"
    aria-labelledby="cancelLabel" aria-hidden="true">
    <div class="modal-dialog custom-modal-dialog custom-small-modal-width modal-dialog-centered">
        <div class="modal-content custom-modal-content">
            <div class="modal-header  custom-small-modal-header">

                <button type="button"
                    @if (Request::segment(1) == 'reservation' || Request::segment(1) == 'payment') class="btn-close"  @else class="btn-close iqama-close-btn" @endif
                    data-bs-dismiss="modal" aria-label="Close">
                </button>
            </div>
            <div class="modal-body">
                <div class="alert-modal">
                    <img src="{{ asset('icons/yaqeen_logo.jpg') }}" alt="" class="mb-3">
                    <h3 class="text-yellow mb-4 fw-600 ">{{ customTrans('general.warning') }}</h3>
                    <p class="mb-4">{{ customTrans('general.please_verify_your_elm_yaqeen') }}</p>
                    <p class="mb-4">{{ customTrans('general.verify_your_identity_to_protect_elm') }}</p>
                    <p class="mb-4">{{ customTrans('general.if_you_have_any_question_about_privacy_policy') }}</p>
                    <a
                        href="{{ route('privacyPolicy', ['lang' => App::getLocale()]) }}">{{ customTrans('footer.privacy_policy') }}</a>
                    <a href="{{ route('managehost.personal_info', ['is_yaqeen' => 'true']) }}">
                        <button class="theme-btn w-100">{{ customTrans('general.verify') }}</button>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

{{-- Email  OTP Verification Modal --}}
<div class="modal fade modal-dr-bottom" id="enteremailotp" data-bs-keyboard="false" tabindex="-1"
    aria-labelledby="enteremailotpLabel" aria-hidden="true">
    <div class="modal-dialog custom-modal-dialog  modal-dialog-centered">
        <div class="modal-content cm-simple-content">
            <div class="modal-header cm-simple-header">
                <h5 class="w-100 mb-0" id="enteremailotpLabel">
                    {{ customTrans('sign_up.welcome_darent') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                </button>
            </div>
            <div class="modal-body pt-0">
                <div class="phone-verification">
                    <form onSubmit="verifyEmailOtp(event)">
                        {{ csrf_field() }}

                        <p class="mb-2 otp-in-text"> Enter the code we sent over Email </p>
                        <fieldset class="verification-field" id="code">
                            <input type="text" class="verification-input" id="otp-a" name="emailotpfield[]"
                                placeholder="-" maxlength="1" />
                            <input type="text" class="verification-input" id="otp-b" name="emailotpfield[]"
                                placeholder="-" maxlength="1" />
                            <input type="text" class="verification-input" id="otp-c" name="emailotpfield[]"
                                placeholder="-" maxlength="1" />
                            <input type="text" class="verification-input" id="otp-d" name="emailotpfield[]"
                                placeholder="-" maxlength="1" />
                            <input type="hidden" name="otp" id="otp" />

                        </fieldset>
                        <span id="emailOtpError"></span>
                        <div class="col-md-12  text-center">
                            <div class="loadergif btn-ld mg-20 d-none">
                                <img src="{{ asset('icons/loader.gif') }}" class="">
                            </div>
                            <button type="submit"
                                class="theme-btn w-100 phone-btn emailotpverify mt-3 mb-2">{{ customTrans('sign_up.submit') }}</button>
                        </div>
                    </form>
                    <button class="btn-info theme-btn w-100" id="resendEmailOtpBtn">Resend Email</button>
                </div>
            </div>
        </div>
    </div>
</div>
{{-- FILTER MODAL --}}
@if (request()->route()->getName() == 'search' || request()->route()->getName() == 'lp.riyadh' || request()->route()->getName() == 'v6.search')
    {{-- <div class="modal fade" id="filter" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg custom-modal-dialog  modal-dialog-centered">
        <div class="modal-content custom-modal-content">
            <div class="modal-header  custom-modal-header">
                <h5 class="modal-title modal-title w-100 text-center" id="staticBackdropLabel">Filter</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                </button>
            </div>
            <div class="modal-body custom-modal-body">
                <div class="row">
                    <div class="col-xl-6">
                        <div class="range-main">
                            <p class="text-left mb-3">
                                {{ customTrans('search.price_range') }}
                            </p>
                            <div class="fltr-rang row">
                                <div class="col-12">
                                    <div class="flt-in">
                                        <input id="price-range" data-provide="slider"
                                            data-slider-min="{{ $min_price }}"
                                            data-slider-max="{{ $max_price }}"
                                            data-slider-value="[{{ $min_price }},{{ $max_price }}]" />
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="price-btn">
                                        <span>{!! $currency_symbol !!}</span>
                                        <span id="minPrice"></span>
                                    </div>
                                </div>

                                    <div class="col-4">
                                        <div class="price-btn">
                                            <span>{!! $currency_symbol !!}</span>
                                            <span id="maxPrice"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="filter-check-inner pt-3">
                                <p>Booking Type</p>
                                <div class="row">
                                    <div class="col-xl-6 col-lg-6 col-sm-6 col-xs-6 mb-2">
                                        <div class="form-check cust-check">
                                            <input class="form-check-input cust-form-check-input" type="checkbox"
                                                name="book_type[]" value="request">
                                            <label class="form-check-label" for="amenities1">
                                                {{ customTrans('property_single.request_book') }}
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-xl-6 col-lg-6 col-sm-6 col-xs-6 mb-2">
                                        <div class="form-check cust-check">
                                            <input class="form-check-input cust-form-check-input" type="checkbox"
                                                name="book_type[]" value="instant">
                                            <label class="form-check-label" for="amenities1">
                                                {{ customTrans('property_single.instant_book') }}
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-6">
                            <div class="filter-check">
                                <div class="filter-check-inner">
                                    <p>{{ customTrans('filter.types_of_place') }}</p>
                                    <div class="row">


                                        @foreach ($space_type as $space)
                                            <div class="col-xl-6 col-lg-6 col-sm-6 col-xs-6 mb-2">
                                                <div class="form-check cust-check">
                                                    <input class="form-check-input cust-form-check-input"
                                                        type="checkbox" value="{{ $space->id }}"
                                                        name="space_type[]" id="space_type_{{ $space->id }}"
                                                        {{ in_array($space->id, $space_type_selected) ? 'checked' : '' }}>
                                                    <label class="form-check-label"
                                                        for="space_type_{{ $space->id }}">
                                                        {{ app()->getLocale() == 'ar' ? $space->name_ar : $space->name }}
                                                    </label>
                                                </div>
                                            </div>
                                        @endforeach

                                </div>
                            </div>
                            <div class="filter-check-inner">
                                <p>{{ customTrans('listing_sidebar.amenities') }}</p>
                                <div class="row">
                                    @foreach ($amenities as $am)
                                        <div class="col-xl-6 col-lg-6 col-sm-6 col-xs-6 mb-2">
                                            <div class="form-check cust-check">
                                                <input class="form-check-input cust-form-check-input"
                                                    type="checkbox" name="amenities[]"
                                                    id="amenities{{ $am->id }}"
                                                    value="{{ $am->id }}"
                                                    {{ in_array($am->id, $amenities_selected) ? 'checked' : '' }}>
                                                <label class="form-check-label" for="amenities1">
                                                    {{ app()->getLocale() == 'ar' ? $am->title_ar : $am->title }}
                                                </label>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                        <button class="filter-apply theme-btn fltr-chk-btn">Show results</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div> --}}

    <div class="modal fade dubai-ff modal-lg-bottom" id="filter" tabindex="-1"
        aria-labelledby="exampleModalLabel" aria-hidden="true" data-easein="slideUpBigIn">
        <div
            class="modal-dialog custom-modal-dialog  modal-dialog-centered cm-lg-width modal-dialog-scrollable bt-grey-scroller">
            <div class="modal-content cm-bd-content bbr-zero">
                <div class="modal-header cm-bd-header">
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                    </button>
                    <h5 class=" w-100 text-center mb-0" id="staticBackdropLabel">{{ customTrans('filter.filter') }}
                    </h5>
                </div>
                <div class="modal-body cm-bd-body p-0">
                    {{-- <div class="type-place-main filter-padding">
                        <h5 class="main-title">Type of Place</h5>
                        <ul class="type-place">
                            <li class="input-container">
                                <input id="all-type" class="radio-button" type="radio" name="radio" checked />
                                <div class="radio-tile">
                                    <h6 class="radio-tile-label mb-1">All type</h6>
                                    <label for="all-type" class="radio-tile-label">2000 <span>SAR</span></label>
                                </div>
                            </li>
                            <li class="input-container">
                                <input id="room" class="radio-button" type="radio" name="radio" />
                                <div class="radio-tile">
                                    <h6 class="radio-tile-label mb-1">Room</h6>
                                    <label for="room" class="radio-tile-label">700 <span>SAR</span></label>
                                </div>
                            </li>
                            <li class="input-container">
                                <input id="home" class="radio-button" type="radio" name="radio" />
                                <div class="radio-tile">
                                    <h6 class="radio-tile-label mb-1">Home</h6>
                                    <label for="home" class="radio-tile-label">4500 <span>SAR</span></label>
                                </div>
                            </li>
                        </ul>
                        <p class="">Browse rooms, homes and more. Average nightly prices don't include fees or
                            taxes.</p>
                    </div> --}}
                    <div class="recommended-main filter-padding">
                        <h5 class="text-left main-title">
                           {{ customTrans('filter.recommended_properties') }}
                        </h5>
                        <div class="row" id="recommended-amenities">

                        </div>
                    </div>
                    <div class="flter-type-place filter-padding">
                        <h5 class="text-left main-title">
                            {{ customTrans('filter.type_of_place') }}
                        </h5>
                        @php
                            $ptype = request()->query('property_type') ?? null;
                        @endphp
                        <div class="type-place-btn-group row">
                        @foreach ($property_type as $key => $property)
                            <div class="type-place-toggle-btn col-4 mb-3">
                                <input type="radio" name="property_type[]" {{ ($property->id == $ptype) ? 'checked' : ($key == 0 ? 'checked' : '') }} value="{{$property->id}}" id="property_type{{$property->id}}" class="property-type-radio">
                                <label for="property_type{{$property->id}}">{{ app()->getLocale() == 'ar' ? $property->name_ar : $property->name }}</label>
                            </div>
                        @endforeach
                        </div>
                    </div>
                     <div class="rb-fillter-main filter-padding">
                        <h5 class="main-title">{{ customTrans('header.guest') }}</h5>
                        <div class="rb-fillter-inner">
                            <div class="swiper filterSlide">
                                <div class="swiper-wrapper mb-3">
                                    <div class="swiper-slide">
                                        <li class="rb-input-container">
                                            <input id="any-two" class="rb-radio-button" type="radio"
                                                name="rb-fillter-two" value="Any" checked />
                                            <div class="rb-radio-tile">
                                                <label for="any-two"
                                                    class="rb-radio-tile-label">{{ customTrans('filter.any') }}</label>
                                            </div>
                                        </li>
                                    </div>
                                    <div class="swiper-slide">
                                        <div class="rb-input-container">
                                            <input id="bd-two-1" class="rb-radio-button" type="radio"
                                                name="rb-fillter-two" value="1" />
                                            <div class="rb-radio-tile">
                                                <label for="bd-two-1" class="rb-radio-tile-label">1</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="swiper-slide">
                                        <div class="rb-input-container">
                                            <input id="bd-two-2" class="rb-radio-button" type="radio"
                                                name="rb-fillter-two" value="2" />
                                            <div class="rb-radio-tile">
                                                <label for="bd-two-2" class="rb-radio-tile-label">2</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="swiper-slide">
                                        <div class="rb-input-container">
                                            <input id="bd-two-3" class="rb-radio-button" type="radio"
                                                name="rb-fillter-two" value="3"/>
                                            <div class="rb-radio-tile">
                                                <label for="bd-two-3" class="rb-radio-tile-label">3</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="swiper-slide">
                                        <div class="rb-input-container">
                                            <input id="bd-two-4" class="rb-radio-button" type="radio"
                                                name="rb-fillter-two" value="4"/>
                                            <div class="rb-radio-tile">
                                                <label for="bd-two-4" class="rb-radio-tile-label">4</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="swiper-slide">
                                        <div class="rb-input-container">
                                            <input id="bd-two-5" class="rb-radio-button" type="radio"
                                                name="rb-fillter-two" value="5"/>
                                            <div class="rb-radio-tile">
                                                <label for="bd-two-5" class="rb-radio-tile-label">5</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="swiper-slide">
                                        <div class="rb-input-container">
                                            <input id="bd-two-6" class="rb-radio-button" type="radio"
                                                name="rb-fillter-two" value="6"/>
                                            <div class="rb-radio-tile">
                                                <label for="bd-two-6" class="rb-radio-tile-label">6</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="swiper-slide">
                                        <div class="rb-input-container">
                                            <input id="bd-two-7" class="rb-radio-button" type="radio"
                                                name="rb-fillter-two" value="7"/>
                                            <div class="rb-radio-tile">
                                                <label for="bd-two-7" class="rb-radio-tile-label">7</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="swiper-slide">
                                        <div class="rb-input-container">
                                            <input id="bd-two-8" class="rb-radio-button" type="radio"
                                                name="rb-fillter-two" value="8"/>
                                            <div class="rb-radio-tile">
                                                <label for="bd-two-8" class="rb-radio-tile-label">+8</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="range-main filter-padding">
                        <h5 class="text-left main-title">
                            {{ customTrans('search.price_range') }}
                        </h5>
                        <div class="fltr-rang row">
                            <div class="col-12 py-4">
                                <div class="flt-in">
                                    @php
                                        $max_price = 5000;
                                        if ($max_price > 5000) {
                                        }
                                    @endphp
                                    <input id="price-range" data-provide="slider" data-bs-placement="top"
                                        data-slider-min="{{ $min_price }}" data-slider-max="{{ $max_price }}"
                                        data-slider-value="[{{ $min_price }},{{ $max_price }}]" />
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="price-btn">
                                    <p class="mb-1">{{ customTrans('search.minimum') }}</p>
                                    <span>{!! $currency_symbol !!}</span>
                                    {{-- <span id="minPrice"></span> --}}
                                    <input type="text" id="min-price" class="editable-rang" pattern="[0-9]*" />
                                </div>
                            </div>

                            <div class="col-6">
                                <div class="price-btn">
                                    <p class="mb-1">{{ customTrans('search.maximum') }}</p>
                                    <span>{!! $currency_symbol !!}</span>
                                    {{-- <span id="maxPrice"></span> --}}
                                    <input type="text" id="max-price" class="editable-rang" pattern="[0-9]*" />
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="location-search-fltr-main filter-padding">
                        <h5 class="main-title">{{ customTrans('filter.search_property') }}</h5>
                        <div class="row">
                            <div class="col-lg-12 mb-2">
                                <div class="ls-fltr-feild">
                                    <p class="mb-0">{{ customTrans('listing_location.district') }}</p>
                                    <div class="district-slt-picker-main">
                                        <select id="district-picker" name="districts"
                                            class="selectpicker district-slt-picker" data-width="100%" data-size="5"
                                            data-live-search="true" data-live-search-placeholder="Search"
                                            data-selected-text-format="count > 2" multiple>
                                            {{-- <option>Mustard</option> --}}
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="ls-fltr-feild">
                                    <p class="mb-0">{{ customTrans('header.unit_code') }}</p>
                                    <input type="text" name="property_code" id="property_code"
                                        placeholder="{{ customTrans('header.example') }}: 00000"
                                        class="form-control">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="rb-fillter-main filter-padding">
                        <h5 class="main-title">{{ customTrans('search.rooms_and_beds') }}</h5>
                        <div class="rb-fillter-inner">
                            <h5 class="inner-main-title">{{ customTrans('property_single.bedroom') }}</h5>
                            <div class="swiper filterSlide mb-3">
                                <div class="swiper-wrapper">
                                    <div class="swiper-slide">
                                        <div class="rb-input-container">
                                            <input id="any" class="rb-radio-button" type="radio"
                                                name="rb-fillter" value="Any" checked />
                                            <div class="rb-radio-tile">
                                                <label for="any"
                                                    class="rb-radio-tile-label">{{ customTrans('filter.any') }}</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="swiper-slide">
                                        <div class="rb-input-container">
                                            <input id="bd-1" class="rb-radio-button" type="radio"
                                                name="rb-fillter" value="1"/>
                                            <div class="rb-radio-tile">
                                                <label for="bd-1" class="rb-radio-tile-label">1</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="swiper-slide">
                                        <div class="rb-input-container">
                                            <input id="bd-2" class="rb-radio-button" type="radio"
                                                name="rb-fillter" value="2"/>
                                            <div class="rb-radio-tile">
                                                <label for="bd-2" class="rb-radio-tile-label">2</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="swiper-slide">
                                        <div class="rb-input-container">
                                            <input id="bd-3" class="rb-radio-button" type="radio"
                                                name="rb-fillter" value="3"/>
                                            <div class="rb-radio-tile">
                                                <label for="bd-3" class="rb-radio-tile-label">3</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="swiper-slide">
                                        <div class="rb-input-container">
                                            <input id="bd-4" class="rb-radio-button" type="radio"
                                                name="rb-fillter" value="4"/>
                                            <div class="rb-radio-tile">
                                                <label for="bd-4" class="rb-radio-tile-label">4</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="swiper-slide">
                                        <div class="rb-input-container">
                                            <input id="bd-5" class="rb-radio-button" type="radio"
                                                name="rb-fillter" value="5"/>
                                            <div class="rb-radio-tile">
                                                <label for="bd-5" class="rb-radio-tile-label">5</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="swiper-slide">
                                        <div class="rb-input-container">
                                            <input id="bd-6" class="rb-radio-button" type="radio"
                                                name="rb-fillter" value="6"/>
                                            <div class="rb-radio-tile">
                                                <label for="bd-6" class="rb-radio-tile-label">6</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="swiper-slide">
                                        <div class="rb-input-container">
                                            <input id="bd-7" class="rb-radio-button" type="radio"
                                                name="rb-fillter" value="7"/>
                                            <div class="rb-radio-tile">
                                                <label for="bd-7" class="rb-radio-tile-label">7</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="swiper-slide">
                                        <div class="rb-input-container">
                                            <input id="bd-8" class="rb-radio-button" type="radio"
                                                name="rb-fillter" value="8"/>
                                            <div class="rb-radio-tile">
                                                <label for="bd-8" class="rb-radio-tile-label">+8</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {{-- <div class="rb-fillter-inner">
                            <h5 class="inner-main-title">{{ customTrans('property_single.bed') }}</h5>
                            <div class="swiper filterSlide">
                                <div class="swiper-wrapper mb-3">
                                    <div class="swiper-slide">
                                        <li class="rb-input-container">
                                            <input id="any-two" class="rb-radio-button" type="radio"
                                                name="rb-fillter-two" checked />
                                            <div class="rb-radio-tile">
                                                <label for="any-two"
                                                    class="rb-radio-tile-label">{{ customTrans('filter.any') }}</label>
                                            </div>
                                        </li>
                                    </div>
                                    <div class="swiper-slide">
                                        <div class="rb-input-container">
                                            <input id="bd-two-1" class="rb-radio-button" type="radio"
                                                name="rb-fillter-two" />
                                            <div class="rb-radio-tile">
                                                <label for="bd-two-1" class="rb-radio-tile-label">1</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="swiper-slide">
                                        <div class="rb-input-container">
                                            <input id="bd-two-2" class="rb-radio-button" type="radio"
                                                name="rb-fillter-two" />
                                            <div class="rb-radio-tile">
                                                <label for="bd-two-2" class="rb-radio-tile-label">2</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="swiper-slide">
                                        <div class="rb-input-container">
                                            <input id="bd-two-3" class="rb-radio-button" type="radio"
                                                name="rb-fillter-two" />
                                            <div class="rb-radio-tile">
                                                <label for="bd-two-3" class="rb-radio-tile-label">3</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="swiper-slide">
                                        <div class="rb-input-container">
                                            <input id="bd-two-4" class="rb-radio-button" type="radio"
                                                name="rb-fillter-two" />
                                            <div class="rb-radio-tile">
                                                <label for="bd-two-4" class="rb-radio-tile-label">4</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="swiper-slide">
                                        <div class="rb-input-container">
                                            <input id="bd-two-5" class="rb-radio-button" type="radio"
                                                name="rb-fillter-two" />
                                            <div class="rb-radio-tile">
                                                <label for="bd-two-5" class="rb-radio-tile-label">5</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="swiper-slide">
                                        <div class="rb-input-container">
                                            <input id="bd-two-6" class="rb-radio-button" type="radio"
                                                name="rb-fillter-two" />
                                            <div class="rb-radio-tile">
                                                <label for="bd-two-6" class="rb-radio-tile-label">6</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="swiper-slide">
                                        <div class="rb-input-container">
                                            <input id="bd-two-7" class="rb-radio-button" type="radio"
                                                name="rb-fillter-two" />
                                            <div class="rb-radio-tile">
                                                <label for="bd-two-7" class="rb-radio-tile-label">7</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="swiper-slide">
                                        <div class="rb-input-container">
                                            <input id="bd-two-8" class="rb-radio-button" type="radio"
                                                name="rb-fillter-two" />
                                            <div class="rb-radio-tile">
                                                <label for="bd-two-8" class="rb-radio-tile-label">+8</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div> --}}
                        <div class="rb-fillter-inner">
                            <h5 class="inner-main-title">{{ customTrans('property_single.bathroom') }}</h5>
                            <div class="swiper filterSlide">
                                <div class="swiper-wrapper">
                                    <div class="swiper-slide">
                                        <div class="rb-input-container">
                                            <input id="any-three" class="rb-radio-button" type="radio"
                                                name="rb-fillter-three" value="Any" checked />
                                            <div class="rb-radio-tile">
                                                <label for="any-three"
                                                    class="rb-radio-tile-label">{{ customTrans('filter.any') }}</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="swiper-slide">
                                        <div class="rb-input-container">
                                            <input id="bd-three-1" class="rb-radio-button" type="radio"
                                                name="rb-fillter-three" value="1" />
                                            <div class="rb-radio-tile">
                                                <label for="bd-three-1" class="rb-radio-tile-label">1</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="swiper-slide">
                                        <div class="rb-input-container">
                                            <input id="bd-three-2" class="rb-radio-button" type="radio"
                                                name="rb-fillter-three" value="2"/>
                                            <div class="rb-radio-tile">
                                                <label for="bd-three-2" class="rb-radio-tile-label">2</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="swiper-slide">
                                        <div class="rb-input-container">
                                            <input id="bd-three-3" class="rb-radio-button" type="radio"
                                                name="rb-fillter-three" value="3"/>
                                            <div class="rb-radio-tile">
                                                <label for="bd-three-3" class="rb-radio-tile-label">3</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="swiper-slide">
                                        <div class="rb-input-container">
                                            <input id="bd-three-4" class="rb-radio-button" type="radio"
                                                name="rb-fillter-three" value="4"/>
                                            <div class="rb-radio-tile">
                                                <label for="bd-three-4" class="rb-radio-tile-label">4</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="swiper-slide">
                                        <div class="rb-input-container">
                                            <input id="bd-three-5" class="rb-radio-button" type="radio"
                                                name="rb-fillter-three" value="5"/>
                                            <div class="rb-radio-tile">
                                                <label for="bd-three-5" class="rb-radio-tile-label">5</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="swiper-slide">
                                        <div class="rb-input-container">
                                            <input id="bd-three-6" class="rb-radio-button" type="radio"
                                                name="rb-fillter-three" value="6"/>
                                            <div class="rb-radio-tile">
                                                <label for="bd-three-6" class="rb-radio-tile-label">6</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="swiper-slide">
                                        <div class="rb-input-container">
                                            <input id="bd-three-7" class="rb-radio-button" type="radio"
                                                name="rb-fillter-three" value="7"/>
                                            <div class="rb-radio-tile">
                                                <label for="bd-three-7" class="rb-radio-tile-label">7</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="swiper-slide">
                                        <div class="rb-input-container">
                                            <input id="bd-three-8" class="rb-radio-button" type="radio"
                                                name="rb-fillter-three" value="8"/>
                                            <div class="rb-radio-tile">
                                                <label for="bd-three-8" class="rb-radio-tile-label">+8</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="filter-check filter-padding">
                        <div class="filter-check-inner">
                            <h5 class="main-title">{{ customTrans('listing_sidebar.amenities') }}</h5>
                            <h5 class="inner-main-title">{{ customTrans('host_dashboard.essential') }}</h5>
                            <div class="row">
                                @foreach ($amenities as $am)
                                    <div class="col-xl-6 col-lg-6 col-sm-6 col-xs-6 mb-3">
                                        <div class="form-check cust-check">
                                            <input class="form-check-input thm-check cust-form-check-input amenity-sync"
                                                type="checkbox" name="amenities[]" id="amenities{{ $am->id }}"
                                                value="{{ $am->id }}"
                                                {{ in_array($am->id, $amenities_selected) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="amenities{{ $am->id }}">
                                                {{ app()->getLocale() == 'ar' ? $am->title_ar : $am->title }}
                                            </label>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                        {{-- <div class="filter-check-inner">

                            <h5 class="main-title">Type Of Property</h5>
                            <div class="row">


                                @foreach ($space_type as $space)
                                    <div class="col-xl-6 col-lg-6 col-sm-6 col-xs-6 mb-2">
                                        <div class="form-check cust-check">
                                            <input class="form-check-input thm-check cust-form-check-input"
                                                type="checkbox" value="{{ $space->id }}" name="space_type[]"
                                                id="space_type_{{ $space->id }}"
                                                {{ in_array($space->id, $space_type_selected) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="space_type_{{ $space->id }}">
                                                {{ app()->getLocale() == 'ar' ? $space->name_ar : $space->name }}
                                            </label>
                                        </div>
                                    </div>
                                @endforeach

                            </div>
                        </div> --}}
                    </div>
                </div>
                <div class="modal-footer cm-bd-footer">
                    <div class="row w-100 g-2">
                        <div class="col-6">
                            <button class="grey-btn rst-btn" type="reset"><i class="ri-refresh-line"></i>
                                {{ customTrans('filter.reset') }}</button>

                        </div>
                        <div class="col-6">
                            <div class="loadergif btn-ld mg-20 m-0 search_loader d-none">
                                <img src="{{ asset('icons/loader.gif') }}" class="">
                            </div>
                            <button
                                class="filter-apply theme-btn fltr-chk-btn" id="search_text">{{ customTrans('filter.show_results') }}</button>
                        </div>
                    </div>
                    {{-- <button
                        class="filter-apply theme-btn fltr-chk-btn">{{ customTrans('filter.show_results') }}</button> --}}
                </div>
            </div>
        </div>
    </div>
@endif
{{-- Reservation  --}}
@if (isset($res_modal))
    <div class="modal fade modal-dr-bottom" id="reservationModal" tabindex="-1" aria-labelledby="reservationModal"
        aria-hidden="true">
        <div class="modal-dialog modal-lg custom-modal-dialog  modal-dialog-centered custmodal-lg-wd">
            <div class="modal-content custom-modal-content">
                <div class="modal-header  custom-modal-header">
                    <h5 class="modal-title modal-title w-100 text-center" id="staticBackdropLabel">
                        {{ customTrans('sign_up.welcome_darent') }}
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                    </button>
                </div>
                <div class="modal-body custom-modal-body">
                    <div class="property-gallery">
                        <div class="pg-slide">
                            <div class="mb-slid">

                            </div>
                            <span class="pagingInfo"></span>
                        </div>
                    </div>

                    <div class="property-detail">
                        <div class="property-inner-detail">
                            <div class="property-description">
                                <h5 class="mb-0 fw-400 popup-font ">{{ $result->name }}
                                    {{-- <span>Entire place</span> --}}
                                </h5>
                                <ul class="d-flex m-0 p-0 popup-font">
                                    <li>{{ $result->bedrooms . customTrans('property_single.bedroom') }}, </li>
                                    <li>{{ $result->beds . customTrans('property_single.bed') }}, </li>
                                    <li>{{ $result->accommodates . customTrans('property_single.guest') }}, </li>
                                    <li>{{ $result->bathrooms . customTrans('property_single.bathroom') }} </li>
                                </ul>
                            </div>
                            <div class="popup-user mt-3">
                                <img class="profile-user" src="{{ $result->users->ProfileSrc }}" alt="">
                                <div class="user-detail">
                                    <h6 class="mb-0 fw-400 popup-font">{{ $result->users->full_name }}</h6>
                                    <p class="popup-font mb-0">{{ customTrans('property_single.joined_in') }}
                                        {{ $result->users->created_at->format('F Y') }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-xl-6 col-lg-6">
                            <div class="popup-check d-flex justify-content-between">
                                <img src="{{ asset('icons/right-arrow.svg') }}" alt="">
                                <div class="check-date text-center">
                                    <p class="mb-1">{{ customTrans('property_single.check_in') }}</p>
                                    <h6 class="mb-0 modal_checkin">20 june 2022</h6>
                                </div>
                                <div class="check-date text-center">
                                    <p class="mb-1">{{ customTrans('property_single.check_out') }}</p>
                                    <h6 class="mb-0 modal_checkout">20 june 2022</h6>
                                </div>
                            </div>


                        </div>
                        <div class="col-xl-6 col-lg-6">
                            <div class="popup-pricing">
                                <div class="table-responsive  m-0 custom-subtotal">
                                    <table class="table table-bordered popuptable mb-0">

                                    </table>
                                </div>

                                <div class="loadergif request-loader btn-ld d-none text-center mt-3">
                                    <img src="{{ asset('icons/loader.gif') }}" class="">
                                </div>
                                <button type="submit" class="theme-btn reserve w-100 mt-3 rq-booking"
                                    id="go_to_payment">{{ $result->booking_type != 'instant'
                                        ? customTrans('property_single.request_book')
                                        : customTrans('property_single.instant_book') }}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endif

<!-- Verified_first -->
<div class="modal fade modal-dr-bottom" id="Verified_first" data-bs-backdrop="static" data-bs-keyboard="false"
    tabindex="-1" aria-labelledby="Verified_first" aria-hidden="true">
    <div class="modal-dialog custom-modal-dialog custom-small-modal-width cm-lg-width w-m-100 modal-dialog-centered">
        <div class="modal-content custom-modal-content">
            <div class="modal-header  custom-small-modal-header">

                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                </button>
            </div>
            <div class="modal-body">
                <div class="alert-modal">
                    <img src="{{ asset('icons/cancel.svg') }}" alt="" class="mb-3">
                    <h3 class="text-danger mb-3 fw-600 ">Verified First</h3>
                    <p class="mb-4 fw-500">You have to verified your account first</p>
                    <a href="{{ route('user.login-security') }}">
                        <button class="theme-btn btn-danger w-100">Verified Now</button>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Verified_first -->


{{-- add card modal --}}
@if (Request::segment(2) == 'wallet' ||
        Request::segment(1) == 'booking_history' ||
        Request::segment(1) == 'reservation' ||
        Request::segment(1) == 'booking_payment')
    <div class="modal fade dubai-ff modal-dr-bottom" id="add-card" data-bs-backdrop="add-cardlabel"
        data-bs-keyboard="false" tabindex="-1" aria-labelledby="calendarLabel" aria-hidden="true">
        <div class="modal-dialog custom-modal-dialog modal-dialog-centered">
            <div class="modal-content cm-simple-content">
                <div class="modal-header cm-simple-header justify-content-center">
                    <button type="button" class="cancel-addcard transparent-btn" data-bs-dismiss="modal"
                        aria-label="Close">
                        <img src="{{ asset('icons/black-larrow.svg') }}" alt="">
                    </button>
                    <h3 class="mb-0 card-modal-heading">{{ customTrans('wallet.add_card') }}</h3>
                </div>
                <div class="modal-body cm-simple-body">
                    <div id="custom_error" class="text-danger error text-center mb-2"></div>
                    <div class="add-card-main">
                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <div class="input-withlable">
                                    <label for=""
                                        class="">{{ customTrans('wallet.holder_name') }}</label>
                                    <input type="text" name="card_name" id="name" placeholder=""
                                        class="form-control only-character-valid">
                                </div>
                                <div id="name_error" class="text-danger error text-left"></div>
                            </div>
                            <div class="col-md-12 mb-3">
                                <div class="input-withlable">
                                    <label for=""
                                        class="">{{ customTrans('payment.card_number') }}</label>
                                    {{-- <input type="text"  maxlength="14" name="card_number" id="card" placeholder="0000 0000 0000 0000" class="form-control" onKeyDown="if(this.value.length==16 && event.keyCode!=8) return false;"> --}}
                                    <input type="text" maxlength="16" name="card_number" id="card"
                                        placeholder="----  ---- ---- ----" class="form-control text-center"
                                        oninput="this.value=this.value.replace(/[^0-9]/g,'');" pattern="[0-9]{16}"
                                        required>
                                </div>
                                <div id="number_error" class="text-danger error text-left"></div>
                            </div>
                            <div class="col-md-12 mb-3">
                                <div class="input-withlable">
                                    <label for=""
                                        class="">{{ customTrans('payment.select_card') }}</label>
                                    <select class="form-control p-0" name="paymentMethodId" id="paymentMethodId">
                                        <option value="2" selected>Visa/Master</option>
                                        <option value="12">STC</option>
                                        <option value="101">Mada</option>
                                    </select>
                                </div>
                                <div id="" class="text-danger error text-center"></div>
                            </div>
                            <div class="col-md-12 mb-3">
                                <div class="row en-position">
                                    <div class="col-6">
                                        <div class="input-withlable en-position">
                                            <label for=""
                                                class="">{{ customTrans('payment.mm') }}</label>
                                            {{-- <input type="text"  maxlength="2" name="expiry_month" pattern="" id="expiry_month" placeholder="MM" class="form-control"> --}}
                                            <select class="form-control" name="expiry_month" pattern=""
                                                id="expiry_month">
                                                <option value="01">01</option>
                                                <option value="02">02</option>
                                                <option value="03">03</option>
                                                <option value="04">04</option>
                                                <option value="05">05</option>
                                                <option value="06">06</option>
                                                <option value="07">07</option>
                                                <option value="08">08</option>
                                                <option value="09">09</option>
                                                <option value="10">10</option>
                                                <option value="11">11</option>
                                                <option value="12">12</option>
                                            </select>
                                        </div>
                                        <div id="month_error" class="text-danger error text-center"></div>
                                    </div>
                                    <div class="col-6">
                                        <div class="input-withlable">
                                            <label for=""
                                                class="">{{ customTrans('payment.yy') }}</label>
                                            {{-- <input type="text"  maxlength="2" name="expiry_year" pattern="" id="expiry_year" placeholder="YY" class="form-control"> --}}
                                            <select class="form-control year" name="expiry_year" pattern=""
                                                id="expiry_year">


                                            </select>
                                        </div>
                                        <div id="year_error" class="text-danger error text-center"></div>
                                    </div>
                                </div>
                                <input type="hidden" name="bid" id="bid">
                            </div>
                            <div class="col-md-12 mb-3">
                                <div class="input-withlable">
                                    <label for="" class="">CVV</label>
                                    <input type="number" name="cvv" id="cvv" maxlength="4"
                                        placeholder="----" class="form-control numberInput text-center"
                                        onKeyDown="if(this.value.length==4 && event.keyCode!=8) return false;">
                                </div>
                                <div id="cvv_error" class="text-danger error text-left"></div>
                            </div>
                            <div class="col-md-12 mt-3 text-center">

                                <p class="text-start mb-3">
                                    {{ customTrans('payment.your_card_info_secure_no_one_reach') }}</p>
                                <div class="loadergif btn-ld d-none">
                                    <img src="{{ asset('icons/loader.gif') }}" class="">
                                </div>
                                <a href="#" class="add-card-btn">
                                    <button class="card-svae-btn theme-btn w-100">Save</button>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endif

@if (Request::segment(2) == 'wallet' ||
        Request::segment(1) == 'booking_history' ||
        Request::segment(1) == 'reservation' ||
        Request::segment(1) == 'booking_payment')
    <div class="modal fade dubai-ff modal-dr-bottom" id="wallet-deposit" data-bs-backdrop="add-cardlabel"
        data-bs-keyboard="false" tabindex="-1" aria-labelledby="calendarLabel" aria-hidden="true">
        <div class="modal-dialog custom-modal-dialog modal-dialog-centered">
            <div class="modal-content cm-simple-content">
                <div class="modal-header cm-simple-header justify-content-center">
                    <button type="button" class="cancel-addcard transparent-btn" data-bs-dismiss="modal"
                        aria-label="Close">
                        <img src="{{ asset('icons/black-larrow.svg') }}" alt="">
                    </button>
                    <h3 class="mb-0 card-modal-heading">Wallet Deposit</h3>
                </div>
                <div class="modal-body cm-simple-body">
                    <div id="custom_error" class="text-danger error text-center mb-2 custom_error"></div>
                    <input type="hidden" name="paymentMethodId" value="2">
                    <div class="add-card-main">
                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <div class="pay-sec mt-0">
                                    <div class="prop-payment-option">
                                        <label class="pay-sec-label" for="ch-card-pay">
                                            <input type="radio" name="pay" id="ch-card-pay" class="cardPay"
                                                checked />
                                            <div class="pay-sl-content">
                                                <img src="{{ asset('images/visa-master-pay.png') }}" alt="">
                                            </div>
                                        </label>
                                        <label class="pay-sec-label" for="ch-stc-pay">
                                            <input type="radio" name="pay" id="ch-stc-pay" class="stcPay" />
                                            <div class="pay-sl-content">
                                                <img src="{{ asset('images/stc-pay.png') }}" alt="">
                                            </div>
                                        </label>
                                        <label class="pay-sec-label" for="ch-mada-pay">
                                            <input type="radio" name="pay" id="ch-mada-pay" class="madaPay" />
                                            <div class="pay-sl-content">
                                                <img src="{{ asset('images/mada-pay.png') }}" alt="">
                                            </div>
                                        </label>
                                        @if ($showPayOpts)
                                            <label class="pay-sec-label" for="ch-apple-pay">
                                                <input type="radio" id="ch-apple-pay" name="pay"
                                                    class="applePay" />
                                                <div class="pay-sl-content">
                                                    <img src="{{ asset('images/apple_pay.png') }}" alt="">
                                                </div>
                                            </label>
                                        @endif
                                    </div>
                                </div>
                            </div>
                            <div class="cardfields-div">
                                <!-- <div class="col-md-12 mb-3" {{ $verified_card ? 'style=display:none;' : '' }}>
                                    <div class="input-withlable">
                                        <label for=""
                                            class="">{{ customTrans('wallet.holder_name') }}</label>
                                            <input type="text" name="card_name" id="card_name" placeholder="" class="form-control only-character-valid"
                                            value="{{ $verified_card ? $verified_card->card_name : '' }}" >
                                    </div>
                                    <div id="card_name_error" class="text-danger error text-left"></div>
                                </div>
                                <div class="col-md-12 mb-3" {{ $verified_card ? 'style=display:none;' : '' }}>
                                    <div class="input-withlable">
                                        <label for=""
                                            class="">{{ customTrans('payment.card_number') }}</label>
                                        <input type="text" maxlength="16" name="card_number" id="card_number"
                                            value="{{ $verified_card ? $verified_card->card_number : '' }}"
                                            placeholder="----  ---- ---- ----" class="form-control text-center"
                                            oninput="this.value=this.value.replace(/[^0-9]/g,'');" pattern="[0-9]{16}"
                                            required>
                                    </div>
                                    <div id="card_number_error" class="text-danger error text-left"></div>
                                </div>
                                <div class="col-md-12 mb-3"  {{ $verified_card ? 'style=display:none;' : '' }}>
                                    <div class="row en-position">
                                        <div class="col-6">
                                            <div class="input-withlable">
                                                <label for=""
                                                    class="">{{ customTrans('payment.mm') }}</label>
                                                    @if ($verified_card)
<select class="form-control" name="expiry_month_wallet" pattern=""
                                                    id="expiry_month_wallet">
                                                    <option value="{{ $verified_card->card_month }}" selected>{{ $verified_card->card_month }}</option>
                                                </select>
@else
<select class="form-control" name="expiry_month_wallet" pattern=""
                                                    id="expiry_month_wallet">
                                                    <option value="01">01</option>
                                                    <option value="02">02</option>
                                                    <option value="03">03</option>
                                                    <option value="04">04</option>
                                                    <option value="05">05</option>
                                                    <option value="06">06</option>
                                                    <option value="07">07</option>
                                                    <option value="08">08</option>
                                                    <option value="09">09</option>
                                                    <option value="10">10</option>
                                                    <option value="11">11</option>
                                                    <option value="12">12</option>
                                                </select>
@endif
                                            </div>
                                            <div id="expiry_month_wallet_error" class="text-danger error text-center"></div>
                                        </div>
                                        <div class="col-6">
                                            <div class="input-withlable">
                                                <label for=""
                                                    class="">{{ customTrans('payment.yy') }}</label>
                                                    @if ($verified_card)
<select class="form-control" name="expiry_year_wallet" pattern=""
                                                        id="expiry_year_wallet">
                                                        <option value="{{ $verified_card->card_year }}" selected>{{ $verified_card->card_year }}</option>
                                                    </select>
@else
<select class="form-control year" name="expiry_year_wallet" pattern=""
                                                        id="expiry_year_wallet">
                                                    </select>
@endif
                                            </div>
                                            <div id="expiry_year_wallet_error" class="text-danger error text-center"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12 mb-3"  {{ $verified_card ? 'style=display:none;' : '' }}>
                                    <div class="input-withlable">
                                        <label for="" class="">CVV</label>
                                        <input type="number" name="card_cvv" id="card_cvv" maxlength="4"
                                            value="{{ $verified_card ? $verified_card->card_cvv : '' }}"
                                            placeholder="----" class="form-control numberInput text-center"
                                            onKeyDown="if(this.value.length==4 && event.keyCode!=8) return false;">
                                    </div>
                                    <div id="card_cvv_error" class="text-danger error text-left"></div>
                                </div> -->
                            </div>
                            <div class="col-md-12 mb-3">
                                <div class="input-withlable">
                                    <label for="" class="">Deposit Amount</label>
                                    <input type="number" name="amount" id="amount" maxlength="4"
                                        placeholder="----" class="form-control numberInput text-center"
                                        onKeyDown="if(this.value.length==4 && event.keyCode!=8) return false;">
                                </div>
                                <div id="amount_error" class="text-danger error text-left"></div>
                            </div>
                            <div class="col-md-12 mt-3 text-center">
                                <p class="text-start mb-3">
                                    {{ customTrans('payment.your_card_info_secure_no_one_reach') }}</p>
                                <div class="loadergif btn-ld d-none">
                                    <img src="{{ asset('icons/loader.gif') }}" class="">
                                </div>
                                <a href="javascript:;" class="wallet-deposit-btn">
                                    <button class="card-svae-btn theme-btn w-100">Deposit</button>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endif

{{-- logout modal --}}
<div class="modal dubai-ff fade" id="logout-modal" data-bs-keyboard="false" tabindex="-1"
    aria-labelledby="logout-modal" aria-hidden="true">
    <div class="modal-dialog custom-modal-dialog modal-dialog-centered">
        <div class="modal-content cm-bd-content">
            <div class="modal-header cm-bd-header">
                <h5 class="w-100 text-center mb-0" id="logout-modal">Logout</h5>
            </div>
            <div class="modal-body cm-bd-body">
                <div class="single-modal-content text-center">
                    <p class="mb-0 text-center">Are you sure you want to <span
                            class="fw-500 text-dark">Logout</span>
                    </p>
                    <a class="btn btn-primary logout-btn" href="#" id="logout_btn"
                        onclick="event.preventDefault(); document.getElementById('logout-form').submit();">Logout
                    </a>
                    <form id="logout-form" action="{{ route('user.logout', ['noToken' => true]) }}"
                        method="POST" style="display: none;">
                        @csrf
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>


<!-- Delete  Modal -->
<div class="modal fade modal-dr-bottom" id="delete-promo" data-bs-backdrop="static" data-bs-keyboard="false"
    tabindex="-1" aria-labelledby="cancelLabel" aria-hidden="true">
    <div class="modal-dialog custom-modal-dialog custom-small-modal-width">
        <div class="modal-content custom-modal-content">
            <div class="modal-header  custom-small-modal-header">

                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                </button>
            </div>
            <div class="modal-body">
                <div class="alert-modal">
                    <div class="sure"><i class="ri-information-line"></i></div>
                    <h3 class="text-danger mb-4 fw-600 ">{{ customTrans('general.warning') }} </h3>
                    <p class="mb-4">{{ customTrans('jquery_validation.are_you_sure_to_delete') }} <br>
                        {{ customTrans('promocode.promo') }}</p>
                    <div class="delete-pr">
                        <a href="#">
                            <button class="theme-btn btn-success w-100 no-del" data-bs-dismiss="modal"
                                aria-label="Close">{{ customTrans('general.no') }}</button>
                        </a>
                        <a href="javascript:;">
                            <button class="theme-btn btn-danger w-100 del">{{ customTrans('general.yes') }}</button>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Delete  Modal -->

{{-- Success Modal --}}
<div class="modal fade modal-dr-bottom" id="success" data-bs-backdrop="static" data-bs-keyboard="false"
    tabindex="-1" aria-labelledby="successLabel" aria-hidden="true">
    <div class="modal-dialog custom-modal-dialog modal-dialog-centered">
        <div class="modal-content custom-modal-content">
            <div class="modal-body">
                <div class="alert-modal">
                    <img src="{{ asset('icons/check-success.svg') }}" alt="" class="mb-3">
                    <h3 class="text-success mb-4 fw-600 ">{{ customTrans('general.success') }}</h3>
                    <p class="mb-4">{{ customTrans('wishlist.wishlist_created_successfully') }}</p>
                    <button class="theme-btn btn-success w-100 successmodalbtn" data-bs-dismiss="modal"
                        aria-label="Close">{{ customTrans('general.ok') }}</button>
                </div>
            </div>
        </div>
    </div>
</div>
{{-- success modal end --}}

{{-- wishlist modal --}}

@if (isset($wishlist_modal))
    <div class="modal fade dubai-ff modal-dr-bottom" id="create-whishlist" data-bs-backdrop="static"
        data-bs-keyboard="false" tabindex="-1" aria-labelledby="create-whishlist" aria-hidden="true">
        <div class="modal-dialog custom-modal-dialog modal-dialog-centered">
            <div class="modal-content cm-bd-content">
                <div class="modal-header cm-bd-header">
                    <h5 class="w-100 text-center mb-0" id="create-whishlist">
                        {{ customTrans('wishlist.name_this_wishlist') }}</h5>
                    <p class="calendar-m-msg" id="wishlist-model-message"></p>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                    </button>
                </div>

                <form>
                    {{ csrf_field() }}
                    <div class="modal-body cm-bd-body">
                        <div class="cw-content">
                            <input type="hidden" name="property_id" id="item_id" value="">
                            <input type="hidden" name="before_discount" id="item_before_discount" value="">
                            <input type="hidden" name="total_price" id="item_total_price" value="">
                            <input type="hidden" name="property_type_name" id="item_property_type_name" value="">
                            <input type="hidden" name="city_name" id="item_city_name" value="">
                            <input type="hidden" name="host_id" id="item_host_id" value="">
                            <input type="hidden" name="day_price" id="item_day_price" value="">
                            <input type="hidden" name="number_of_days" id="item_number_of_days" value="">
                            <input type="hidden" name="property_code" id="item_property_code" value="">

                            <input type="text" name="name" id="name"
                                placeholder="{{ customTrans('login.name') }}"
                                class="modal-form-control input-content createWishlistName" id="cw-title name"
                                minlength="0" maxlength="50">
                            <span class="character-count-display input-mx-content">0/50
                                {{ customTrans('wishlist.character_maximum') }}</span>
                        </div>
                    </div>
                    <div class="modal-footer cm-bd-footer">
                        <button type="submit" id="wishlistBtn"
                            class="btn btn-primary enable-md-btn disabled">{{ customTrans('wishlist.create') }}</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endif

{{-- delete modal --}}
<div class="modal fade dubai-ff modal-dr-bottom" id="delete-wishlist" data-bs-backdrop="static"
    data-bs-keyboard="false" tabindex="-1" aria-labelledby="delete-wishlist" aria-hidden="true">
    <div class="modal-dialog custom-modal-dialog modal-dialog-centered">
        <div class="modal-content cm-bd-content">
            <div class="modal-header cm-bd-header">
                <button class="modal-delete-btn transparent-btn" data-bs-toggle="modal"
                    data-bs-target="#confirm-delete-wishlist"
                    data-id="">{{ customTrans('general.delete') }}</button>
                <h5 class="w-100 text-center mb-0" id="delete-wishlist">{{ customTrans('footer.setting') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                </button>
            </div>
            {{-- <form> --}}
            {{ csrf_field() }}
            <div class="modal-body cm-bd-body">
                <div class="delete-wishlist-content">
                    <div class="dwc-inner">
                        <label for=""
                            class="wh-name-lb">{{ customTrans('wishlist.wishlist_name') }}</label>
                        <input type="hidden" name="wishlist_id" id="wishlist-id" value="">
                        <input type="text" placeholder="{{ customTrans('login.name') }}"
                            class="modal-form-control input-content" id="rename-title" minlength="0"
                            maxlength="50">
                        <span class="character-count-display input-mx-content">0/50
                            {{ customTrans('wishlist.character_maximum') }}</span>
                    </div>


                </div>
            </div>
            <div class="modal-footer cm-bd-footer cm-footer-two">
                <button type="button" class="transparent-btn text-decoration-underline fw-500 sm-fc-black"
                    data-bs-dismiss="modal">{{ customTrans('host_dashboard.cancel') }}</button>
                <button type="button" class="btn btn-primary" id="updateWishlistName" data-bs-toggle="modal"
                    data-bs-target="#create-whishlist">{{ customTrans('users_profile.save') }}</button>
            </div>
            {{-- </form> --}}
        </div>
    </div>
</div>

{{-- confirm delete modal --}}
<div class="modal fade dubai-ff modal-dr-bottom" id="confirm-delete-wishlist" data-bs-backdrop="static"
    data-bs-keyboard="false" tabindex="-1" aria-labelledby="confirm-delete-wishlist" aria-hidden="true">
    <div class="modal-dialog custom-modal-dialog modal-dialog-centered">
        <div class="modal-content cm-bd-content">
            <div class="modal-header cm-bd-header">
                <h5 class="w-100 text-center mb-0" id="confirm-delete-wishlist">
                    {{ customTrans('wishlist.delete_the_wishlist') }}</h5>
                <button type="button" class="btn-close" data-bs-target="#delete-wishlist"
                    data-bs-toggle="modal" data-bs-dismiss="modal">
                </button>
            </div>
            <form>
                {{ csrf_field() }}
                <input type="hidden" name="id" id="wishlist-id" value="">
                <div class="modal-body cm-bd-body">
                    <div class="cdw-content">
                        <p class="mb-0">{{ customTrans('jquery_validation.are_you_sure_to_delete') }}
                            {{-- <span>Chalet?</span> --}}
                        </p>
                    </div>
                </div>
                <div class="modal-footer cm-bd-footer cm-footer-two">
                    <button type="button"
                        class="transparent-btn text-decoration-underline fw-500 cdw-btn-close sm-fc-black"
                        data-bs-dismiss="modal">{{ customTrans('host_dashboard.cancel') }}</button>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal"
                        data-bs-target="#confirm-create-whishlist"
                        id="confirmDeleteBtn">{{ customTrans('wishlist.yes_delete') }}</button>
                </div>
            </form>
        </div>
    </div>
</div>

{{-- collaborate modal --}}
@if (isset($wishlist))
    <div class="modal fade dubai-ff" id="collaborator-wishlist" data-bs-backdrop="static"
        data-bs-keyboard="false" tabindex="-1" aria-labelledby="collaborator-wishlist" aria-hidden="true">
        <div class="modal-dialog custom-modal-dialog modal-dialog-centered">
            <div class="modal-content cm-bd-content">
                <div class="modal-header cm-bd-header">
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                    </button>
                    <h5 class="w-100 text-center mb-0" id="collaborator-wishlist">
                        {{ customTrans('footer.setting') }}</h5>
                    <button class="modal-delete-btn transparent-btn" data-bs-toggle="modal"
                        data-bs-target="#leave-wishlist" data-id="{{ isset($wishlist) ? $wishlist->id : null }}"
                        id="leave-wishlist">Leave</button>
                </div>

                <div class="modal-body cm-bd-body">
                    <div class="leave-wishlist-content">
                        <div class="dwc-inner">

                            <img src="{{ asset($wishlist->user->getProfileSrcAttribute()) }}" alt="">
                            <h5>{{ $wishlist->user->getFullNameAttribute() }}</h5>
                            <span>Owner</span>

                            @php
                                $userNames = [];
                                foreach (explode(',', $wishlist->share_with) as $userId) {
                                    $user = App\Models\User::find($userId);
                                    if ($user) {
                                        $userNames[] = $user->first_name;
                                    }
                                }
                            @endphp
                            @if (!empty($userNames))
                                <span>Shared with: {{ implode(', ', $userNames) }}</span>
                            @endif

                        </div>


                    </div>
                </div>
                <div class="modal-footer cm-bd-footer cm-footer-two">
                    <button type="button" class="transparent-btn text-decoration-underline fw-500 align-center"
                        data-bs-dismiss="modal">{{ customTrans('host_dashboard.cancel') }}</button>
                </div>

            </div>
        </div>
    </div>
@endif

{{-- book  modal --}}
<div class="modal fade dubai-ff modal-lg-bottom" id="md-profile-book" data-bs-backdrop="static"
    data-bs-keyboard="false" tabindex="-1" aria-labelledby="md-profile-book" aria-hidden="true"
    data-easein="slideUpBigIn">
    <div class="modal-dialog custom-modal-dialog modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content cm-bd-content profile-modal-main">
            <div class="modal-header cm-bd-header">
                <button type="button" class="btn-close" data-bs-toggle="modal" data-bs-dismiss="modal">
                </button>
                <h5 class="w-100 text-center mb-0" id="md-profile-book">Profile</h5>
            </div>
            <div class="modal-body cm-bd-body bt-grey-scroller">
                <div class="">
                    <a href="{{ route('user.profile.view') }}" class="pe-auto">
                        <div class="md-profile-box">
                            <div class="row align-items-center">
                                <div class="col-md-8">
                                    <div class="md-pb-profile">
                                        <div class="md-pb-pimg">
                                            <img src="{{ asset('icons/user.svg') }}" class="up hostimage"
                                                alt="">
                                            <img src="{{ asset('images/verify-icon.png') }}"
                                                class="circle-check md-pb-verfy" alt="">
                                        </div>
                                        <div class="md-pb-pcontent">
                                            <h3 class="mb-0 hostName">{{-- Dynamic Data --}}</h3>
                                            <p class="mb-0">Host</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <ul class="md-pb-pstatus">
                                        <li>
                                            <h6 class="mb-1 hostReviewsCount">{{-- Dynamic Data --}}</h6>
                                            <p class="mb-0">Reviews</p>
                                        </li>
                                        <li>
                                            <h6 class="mb-1 hostRating">
                                                {{-- Dynamic Data --}}
                                                <img src="{{ asset('icons/Rate.svg') }}" alt="">
                                            </h6>
                                            <p class="mb-0">Rating</p>
                                        </li>
                                        <li>
                                            <h6 class="mb-1 duration">{{-- Dynamic Data --}}</h6>
                                            <p class="mb-0">Hosting period</p>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </a>
                    {{-- <div class="md-pb-bio">
                        <ul>
                            <li>
                                <span class="md-pb-bioicon"><i class="fas fa-graduation-cap"></i></span>
                                <span class="md-pb-biocontent">Where I went to school: Reggio Calabria</span>
                            </li>
                            <li>
                                <span class="md-pb-bioicon"><i class="fas fa-globe"></i></span>
                                <span class="md-pb-biocontent">Speaks English, Italian</span>
                            </li>
                            <li>
                                <span class="md-pb-bioicon"><i class="fas fa-globe-africa"></i></span>
                                <span class="md-pb-biocontent">Lives in Turin, Italy</span>
                            </li>
                            <li>
                                <span class="md-pb-bioicon"><i class="ri-music-2-line"></i></span>
                                <span class="md-pb-biocontent">Favorite song in high school: Músicas de Rick Astley e
                                    da Madonna</span>
                            </li>
                        </ul>
                        <p class="">
                            Here I am on this adventure! I've always loved sharing my spaces with friends or family, so
                            I said why not make it a business ? I am originally from a small town near Reggio Calabria
                            but Turin has become my city for 30 years and I am really excited about it. I live with my
                            son Emanuele and my super cat Whisky
                        </p>
                    </div> --}}
                </div>
                <hr>
                <div class="md-review-box">
                    <div class="md-rb-head">
                        <h4 class="mt-4 mb-3 ellipsis-oneline"><span
                                class="hostName">{{-- Dynamic Data --}}</span>'s reviews</h4>
                    </div>
                    <div class="md-rb-body">
                        <div class="headBtn">


                            <div class="swiper mdReviewSlider">
                                <div class="swiper-wrapper reviewsContent">
                                    {{-- Dynamic Data --}}
                                </div>
                            </div>


                            <div class="swiper-button-prev"></div>
                            <div class="swiper-button-next"></div>
                        </div>
                        @if (app()->environment() != 'prod')
                            <button class="transparent-btn" data-bs-toggle="modal" data-bs-target="#md-all-review"
                                data-id="">Show all <span>8</span> Review</button>
                        @endif
                    </div>
                </div>
                <hr>

                {{-- <div class="md-user-identity">
                    <h5 class="mb-3 mt-4"><span class="hostName">  </span>'s confirmed information</h5>
                    <ul>
                        <li>
                            <i class="ri-check-double-line"></i>
                            <p class="mb-0">Identity</p>
                        </li>
                        <li>
                            <i class="ri-check-double-line"></i>
                            <p class="mb-0">Email address</p>
                        </li>
                        <li>
                            <i class="ri-check-double-line"></i>
                            <p class="mb-0">Phone number</p>
                        </li>
                    </ul>
                </div>
                <hr>
                <div class="md-user-about">
                    <h4 class="mb-3 mt-4">Ask User about</h4>
                    <ul>
                        <li>
                            <i class="ri-pencil-ruler-fill"></i>
                            <span class="mb-0">Identity</span>
                        </li>
                        <li>
                            <i class="ri-walk-line"></i>
                            <span class="mb-0">Phone number</span>
                        </li>
                        <li>
                            <i class="ri-walk-line"></i>
                            <span class="mb-0">Phone number</span>
                        </li>
                    </ul>
                </div>
                <hr> --}}
                <div class="md-user-listing">
                    <div class="md-ul-head">
                        <h4 class="mb-3 mt-4">User listings</h4>
                    </div>
                    <div class="md-ul-body">
                        <div class="property headBtn">
                            <div class="swiper mdUlSlider">
                                <div class="swiper-wrapper listingdata">

                                </div>
                            </div>
                            <div class="swiper-button-prev-pro"></div>
                            <div class="swiper-button-next-pro"></div>
                        </div>

                    </div>
                </div>
            </div>
            {{-- <div class="modal-footer cm-bd-footer">
                <button type="button" class="transparent-btn rprt-btn d-flex align-items-center"
                    data-bs-toggle="modal" data-bs-target="#md-profile-report" data-id="">
                    <i class="fas fa-flag"></i>
                    <span class="text-decoration-underline">Report this profile</span>
                </button>
            </div> --}}
        </div>
    </div>
</div>
{{-- book  modal report --}}
<div class="modal fade dubai-ff" id="md-profile-report" data-bs-backdrop="static" data-bs-keyboard="false"
    tabindex="-1" aria-labelledby="md-profile-report" aria-hidden="true">
    <div class="modal-dialog custom-modal-dialog modal-dialog-centered">
        <div class="modal-content cm-bd-content">
            <div class="modal-header cm-bd-header">
                <button type="button" class="btn-close" data-bs-target="#md-profile-book"
                    data-bs-toggle="modal" data-bs-dismiss="modal">
                </button>
                <h5 class="w-100 text-center mb-0" id="md-profile-report">Report Profile</h5>
            </div>
            <div class="modal-body cm-bd-body">
                <div class="pr-content">
                    <h4 class="mb-0">What's happening?</h4>
                    <p>This will only be shared with Airbnb.</p>
                    <ul>
                        <li>
                            <div class="form-check cust-check listing-ql pr-radiobtn">
                                <label class="form-check-label" for="preport-1">
                                    <p class="mb-0">I think they're scamming or spamming me</p>
                                </label>
                                <input class="form-check-input cust-form-check-radio-input" type="radio"
                                    value="request" name="pr-radiobtn" id="preport-1">
                            </div>
                        </li>
                        <li>
                            <div class="form-check cust-check listing-ql pr-radiobtn">
                                <label class="form-check-label" for="preport-2">
                                    <p class="mb-0">They're being offensive</p>
                                </label>
                                <input class="form-check-input cust-form-check-radio-input" type="radio"
                                    value="request" name="pr-radiobtn" id="preport-2">
                            </div>
                        </li>
                        <li>
                            <div class="form-check cust-check listing-ql pr-radiobtn">
                                <label class="form-check-label" for="preport-3">
                                    <p class="mb-0">Something else</p>
                                </label>
                                <input class="form-check-input cust-form-check-radio-input" type="radio"
                                    value="request" name="pr-radiobtn" id="preport-3">
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="modal-footer cm-bd-footer">
                <button type="button" class="btn btn-primary" id="" data-bs-toggle="modal"
                    data-bs-target="#t">Submit</button>
            </div>
        </div>
    </div>
</div>
{{-- book  modal review --}}
<div class="modal fade dubai-ff" id="md-all-review" data-bs-backdrop="static" data-bs-keyboard="false"
    tabindex="-1" aria-labelledby="md-all-review" aria-hidden="true">
    <div class="modal-dialog custom-modal-dialog modal-dialog-centered cm-lg-width">
        <div class="modal-content cm-bd-content">
            <div class="modal-header cm-bd-header">
                <button type="button" class="btn-close" data-bs-target="#md-profile-book"
                    data-bs-toggle="modal" data-bs-dismiss="modal">
                </button>
                <h5 class="w-100 text-center mb-0" id="md-all-review">Review</h5>
            </div>
            <div class="modal-body cm-bd-body bt-grey-scroller">
                <div class="side-list">
                    <div class="account-side">
                        {{-- <div class="row align-items-center">
                            <div class="col-md-6">
                                <div class="">
                                    <div class="pr-img">
                                        <img src="{{ auth()->user()->ProfileSrc }}">
                                    </div>
                                    <div class="">
                                        <h5>{{ auth()->user()->full_name }}</h5>
                                        <p>{{customTrans('property_single.joined_in')}} {{ auth()->user()->account_since }} </p>
                                    </div>
                                    <div class="">
                                        <h6>Total Review : <span>8</span></h6>
                                    </div>
                                </div>
                            </div>
                        </div> --}}
                        <h3 class="mb-4"><span>5</span> Reviews</h3>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="account-detail">
                                    <div class="log-inner">
                                        <div class="tabs-sc grey-tabs shadow-tabs account-tab">
                                            <ul class="nav nav-pills mb-4 md-gr-tb" id="pills-tab" role="tablist">
                                                <li class="nav-item" role="presentation">
                                                    <button class="nav-link active" id="pills-about-tab"
                                                        data-bs-toggle="pill" data-bs-target="#pills-about"
                                                        type="button" role="tab" aria-controls="pills-about"
                                                        aria-selected="true">From guest <span
                                                            class="single-review-count"> 3</span></button>
                                                </li>
                                                <li class="nav-item" role="presentation">
                                                    <button class="nav-link" id="pills-your-reviews-tab"
                                                        data-bs-toggle="pill" data-bs-target="#pills-your-reviews"
                                                        type="button" role="tab"
                                                        aria-controls="pills-your-reviews"
                                                        aria-selected="false">From Host <span
                                                            class="single-review-count"> 2</span></button>
                                                </li>
                                            </ul>
                                            <div class="tab-content" id="pills-tabContent">
                                                <div class="tab-pane fade show active md-gr-tbContent"
                                                    id="pills-about" role="tabpanel"
                                                    aria-labelledby="pills-about-tab">
                                                    <div class="reviews mb-4">
                                                        <div class="d-flex align-items-center">
                                                            <div class="pr-mini-detail d-flex align-items-end">
                                                                <div class="pr-img">
                                                                    <img src="{{ asset('icons/user.svg') }}">
                                                                </div>
                                                                <div class="">
                                                                    <h5 class="4 mb-0 ellipsis-oneline">Jennifer</h5>
                                                                    <p>{{ customTrans('property_single.joined_in') }}
                                                                        October 2022</p>
                                                                </div>
                                                            </div>
                                                            <div class="md-rev-tb-img">
                                                                <img src="{{ asset('images/p2-webp') }}"
                                                                    alt="">
                                                            </div>
                                                        </div>
                                                        <div class="mt-3">
                                                            <p class="dark-grey fs-17">Lorem ipsum dolor sit amet,
                                                                consectetur adipisicing elit. Corrupti eveniet aliquam
                                                                repellendus cum repudiandae id hic tenetur consequatur
                                                                dolor ullam. Dolore, incidunt! Temporibus consequuntur
                                                                recusandae delectus laboriosam est ullam fuga.
                                                            </p>
                                                        </div>
                                                    </div>
                                                    <div class="reviews mb-4">
                                                        <div class="d-flex align-items-center">
                                                            <div class="pr-mini-detail d-flex align-items-end">
                                                                <div class="pr-img">
                                                                    <img src="{{ asset('icons/user.svg') }}">
                                                                </div>
                                                                <div class="">
                                                                    <h5 class="4 mb-0 ellipsis-oneline">Jennifer</h5>
                                                                    <p>{{ customTrans('property_single.joined_in') }}
                                                                        October 2022</p>
                                                                </div>
                                                            </div>
                                                            <div class="md-rev-tb-img">
                                                                <img src="{{ asset('images/p2-webp') }}"
                                                                    alt="">
                                                            </div>
                                                        </div>
                                                        <div class="mt-3">
                                                            <p class="dark-grey fs-17">Lorem ipsum dolor sit amet,
                                                                consectetur adipisicing elit. Corrupti eveniet aliquam
                                                                repellendus cum repudiandae id hic tenetur consequatur
                                                                dolor ullam. Dolore, incidunt! Temporibus consequuntur
                                                                recusandae delectus laboriosam est ullam fuga.
                                                            </p>
                                                        </div>
                                                    </div>
                                                    <div class="reviews mb-4">
                                                        <div class="d-flex align-items-center">
                                                            <div class="pr-mini-detail d-flex align-items-end">
                                                                <div class="pr-img">
                                                                    <img src="{{ asset('icons/user.svg') }}">
                                                                </div>
                                                                <div class="">
                                                                    <h5 class="4 mb-0 ellipsis-oneline">Jennifer</h5>
                                                                    <p>{{ customTrans('property_single.joined_in') }}
                                                                        October 2022</p>
                                                                </div>
                                                            </div>
                                                            <div class="md-rev-tb-img">
                                                                <img src="{{ asset('images/p2-webp') }}"
                                                                    alt="">
                                                            </div>
                                                        </div>
                                                        <div class="mt-3">
                                                            <p class="dark-grey fs-17">Lorem ipsum dolor sit amet,
                                                                consectetur adipisicing elit. Corrupti eveniet aliquam
                                                                repellendus cum repudiandae id hic tenetur consequatur
                                                                dolor ullam. Dolore, incidunt! Temporibus consequuntur
                                                                recusandae delectus laboriosam est ullam fuga.
                                                            </p>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="tab-pane fade md-hr-tbContent md-gr-tbContent"
                                                    id="pills-your-reviews" role="tabpanel"
                                                    aria-labelledby="pills-your-reviews-tab">
                                                    <div class="reviews">
                                                        <div class="row">
                                                            <div class="col-md-8">
                                                                <div class="ls-content">
                                                                    <h5 class="ellipsis-twoline">
                                                                        Apartment In Makkah Province, Jeddah
                                                                    </h5>
                                                                    <div class="mini-profile reviews">
                                                                        <div class="pr-img">
                                                                            <img
                                                                                src="{{ asset('icons/user.svg') }}">
                                                                        </div>
                                                                        <div class="pr-mini-detail">
                                                                            <h4>
                                                                                Mohamed Ahmed
                                                                            </h4>
                                                                            <p>{{ customTrans('property_single.joined_in') }}
                                                                                October 2022</p>
                                                                        </div>
                                                                    </div>

                                                                </div>
                                                            </div>
                                                            <div class="col-md-4">
                                                                <div class="md-rev-tb-img img text-end">
                                                                    <img src="{{ asset('images/p6.webp') }}"
                                                                        alt="image">
                                                                </div>
                                                            </div>
                                                            <div class="col-md-12">
                                                                <p class="dark-grey fs-17 mt-3">
                                                                    Lorem ipsum dolor sit amet consectetur
                                                                    adipisicing elit. Nam soluta a consequuntur
                                                                    illum odit magni maiores ab enim deserunt,
                                                                    aperiam, voluptatum quisquam odio aliquam
                                                                    repellat! Ut vel velit, architecto id
                                                                    aperiam neque totam molestias, fuga sed a,
                                                                    non nostrum. Excepturi.
                                                                </p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="reviews">
                                                        <div class="row">
                                                            <div class="col-md-8">
                                                                <div class="ls-content">
                                                                    <h5 class="ellipsis-twoline">
                                                                        Apartment In Makkah Province, Jeddah
                                                                    </h5>
                                                                    <div class="mini-profile reviews">
                                                                        <div class="pr-img">
                                                                            <img
                                                                                src="{{ asset('icons/user.svg') }}">
                                                                        </div>
                                                                        <div class="pr-mini-detail">
                                                                            <h4>
                                                                                Mohamed Ahmed
                                                                            </h4>
                                                                            <p>{{ customTrans('property_single.joined_in') }}
                                                                                October 2022</p>
                                                                        </div>
                                                                    </div>

                                                                </div>
                                                            </div>
                                                            <div class="col-md-4">
                                                                <div class="md-rev-tb-img img text-end">
                                                                    <img src="{{ asset('images/p6.webp') }}"
                                                                        alt="image">
                                                                </div>
                                                            </div>
                                                            <div class="col-md-12">
                                                                <p class="dark-grey fs-17 mt-3">
                                                                    Lorem ipsum dolor sit amet consectetur
                                                                    adipisicing elit. Nam soluta a consequuntur
                                                                    illum odit magni maiores ab enim deserunt,
                                                                    aperiam, voluptatum quisquam odio aliquam
                                                                    repellat! Ut vel velit, architecto id
                                                                    aperiam neque totam molestias, fuga sed a,
                                                                    non nostrum. Excepturi.
                                                                </p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


{{-- share property modal --}}
@if (isset($booking))
    {{-- <div class="modal fade dubai-ff" id="share-property" data-bs-backdrop="static" data-bs-keyboard="false"
    tabindex="-1" aria-labelledby="share-property" aria-hidden="true">
    <div class="modal-dialog custom-modal-dialog modal-dialog-centered">
        <div class="modal-content cm-bd-content">
            <div class="modal-header cm-bd-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                </button>
                <h5 class="w-100 text-center mb-0" id="share-property">{{ customTrans('footer.setting') }}</h5>
            </div>

            <div class="modal-body cm-bd-body">
                <div class="leave-wishlist-content">
                    <div class="dwc-inner">

                        <img src="" alt="">
                        <div class="df-align-center">

                            <input type="text" value="{{ url('/').'/properties/'. $booking->properties->slug }}" id="copyUrl" class="modal-form-control input-content" readonly>
                            <button onclick="copyToClipboard('propertyurl')" class="transparent-btn">
                                <i class="ri-file-copy-line"></i>
                            </button>
                        </div>


                    </div>


                </div>
            </div>
            <div class="modal-footer cm-bd-footer cm-footer-two">
                <button type="button" class="transparent-btn text-decoration-underline fw-500 align-center"
                    data-bs-dismiss="modal">{{ customTrans('host_dashboard.cancel') }}</button>
            </div>

        </div>
    </div>
</div> --}}
    {{-- Share Link Modal --}}
    <div class="modal fade dubai-ff modal-dr-bottom" id="share-property" data-bs-backdrop="static"
        data-bs-keyboard="false" tabindex="-1" aria-labelledby="share-property" aria-hidden="true">
        <div class="modal-dialog custom-modal-dialog modal-dialog-centered">
            <div class="modal-content cm-bd-content">
                <div class="modal-header cm-bd-header">
                    <h5 class="w-100 text-center mb-0" id="share-property">
                        {{ customTrans('reservation.how_do_you_share') }}
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                    </button>
                </div>
                <div class="modal-body cm-bd-body">
                    <div class="share-link">
                        <input type="text" value="{{ url('/') . '/properties/' . $booking->properties->slug }}"
                            id="copyUrl" class="modal-form-control input-content mb-2" readonly>
                        <button class="transparent-btn sl-btn" id="shareLinkBtn"
                            onclick="copyToClipboard('propertyurl')">
                            <img src="{{ asset('icons/link-icon.svg') }}" alt="">
                            <div class="sl-content">
                                <h5>{{ customTrans('search.copy_link') }}</h5>
                                <p class="mb-0">{{ customTrans('reservation.anyone_with_the_link') }}</p>
                            </div>

                        </button><br />
                        <div class="share-si">
                            <h4>{{ customTrans('reservation.Send_via_social') }}</h4>
                            <ul>
                                <li>
                                    <a href="https://www.facebook.com/profile.php?id=100083206208439&is_tour_dismissed=true" target="_blank">
                                        <img src="{{ asset('images/facebook.png') }}" alt="">
                                    </a>
                                </li>
                                <li>
                                    <a href="https://www.instagram.com/darentapp" target="_blank">
                                        <img src="{{ asset('images/instagram.png') }}" alt="">
                                    </a>
                                </li>
                                <li>
                                    <a href="https://api.whatsapp.com/send?phone=966533318409&text={{ url('/') . '/properties/' . $booking->properties->slug }}" target="_blank">
                                        <img src="{{ asset('images/whatsapp.png') }}" alt="">
                                    </a>
                                </li>
                                <li>
                                    <a href="#">
                                        <img src="{{ asset('images/snapchat.png') }}" alt="">
                                    </a>
                                </li>
                                <li>
                                    <a href="https://www.tiktok.com/@darentapp" target="_blank">
                                        <img src="{{ asset('images/tik-tok.png') }}" alt="">
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endif
{{-- Block Words Alert --}}
<div class="modal fade dubai-ff modal-dr-bottom" id="blockAlert" data-bs-backdrop="static"
    data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
    <div class="modal-dialog custom-modal-dialog modal-dialog-centered">
        <div class="modal-content cm-simple-content">
            <div class="modal-header cm-simple-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body cm-simple-body">
                <div class="alert-modal">
                    <h3 class="  mb-4 fw-600 ">{{ customTrans('host_dashboard.sorry_cant_send_msg') }}</h3>
                    <p class="mb-4">{{ customTrans('host_dashboard.links_cant_share') }}
                    </p>
                    <p class="mb-4">{{ customTrans('host_dashboard.remove_info') }}:</p>
                    <div id="blockWordRender"></div>
                    <button onclick="editMessage()" class="theme-btn w-100" data-bs-dismiss="modal"
                        aria-label="Close">{{ customTrans('host_dashboard.edit_message') }}</button>
                </div>
            </div>
        </div>
    </div>
</div>
{{-- share property modal ends --}}
{{-- tabby modal --}}
<div class="modal fade dubai-ff modal-dr-bottom" id="tabby" data-bs-backdrop="static"
    data-bs-keyboard="false" tabindex="-1" aria-labelledby="tabby" aria-hidden="true">
    <div class="modal-dialog custom-modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content custom-modal-content">
            <div class="modal-header  custom-small-modal-header">

                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                </button>
            </div>
            <div class="modal-body">
                <div class="alert-modal">
                    <img src="{{ asset('icons/tabby-main.svg') }}" alt="" class="mb-3 mt-4">
                    <h1 class="tb-hd">{{ customTrans('tabby.payin_interest') }}</h1>
                    <p>{{ customTrans('tabby.shariah_compliant') }}</p>
                    <div class="tb-d">
                        <div class="tb-dt">
                            <div class="tb-circle">
                                <img src="{{ asset('images/tabby-chart-1.png') }}" alt="">
                            </div>
                            <p><span><b class="tabby-instal-amount">0.00<br>SAR</b></span><br>
                                <span>{{ customTrans('tabby.today') }}</span>
                            </p>
                        </div>
                        <div class="tb-dt">
                            <div class="tb-circle">
                                <img src="{{ asset('images/tabby-chart-2.webp') }}" alt="">
                            </div>
                            <p><span><b class="tabby-instal-amount">0.00<br>SAR</b></span><br>
                                <span>{{ customTrans('tabby.one_month') }}</span>
                            </p>
                        </div>
                        <div class="tb-dt">
                            <div class="tb-circle">
                                <img src="{{ asset('images/tabby-chart-3.webp') }}" alt="">
                            </div>
                            <p><span><b class="tabby-instal-amount">0.00<br>SAR</b></span><br>
                                <span>{{ customTrans('tabby.two_month') }}</span>
                            </p>
                        </div>
                        <div class="tb-dt">
                            <div class="tb-circle ls-child">
                                <img src="{{ asset('images/tabby-chart-4.webp') }}" alt="">
                            </div>
                            <p><span><b class="tabby-instal-amount">0.00<br>SAR</b></span><br>
                                <span>{{ customTrans('tabby.three_month') }}</span>
                            </p>
                        </div>
                    </div>
                    <div class="tb-how mt-4">
                        <h3 class="mb-3">{{ customTrans('tabby.how_it_work') }}</h3>
                        <p><span class="tb-num">1</span><span
                                class="tb-cont">{{ customTrans('tabby.choose_tabby') }}</span></p>
                        <p><span class="tb-num">2</span><span
                                class="tb-cont">{{ customTrans('tabby.information') }}</span></p>
                        <p><span class="tb-num">3</span><span
                                class="tb-cont">{{ customTrans('tabby.first_payment') }}</span></p>
                        <p><span class="tb-num">4</span><span
                                class="tb-cont">{{ customTrans('tabby.reminder') }}</span></p>
                        <div class="tb-card">
                            <img src="{{ asset('icons/visa-card.svg') }}" alt="">
                            <img src="{{ asset('icons/master-card.svg') }}" alt="">
                            <img src="{{ asset('icons/mada-card.svg') }}" alt="">
                            <img src="{{ asset('icons/apple-card.svg') }}" alt="">
                            <img src="{{ asset('icons/american-card.svg') }}" alt="">

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{{-- tabby modal end --}}

{{-- host property alert modal --}}
<div class="modal fade prop-verify-modal modal-dr-bottom" id="prop-verify-alert" data-bs-backdrop="static"
    data-bs-keyboard="false" tabindex="-1" aria-labelledby="alertLabel" aria-hidden="true">
    <div class="modal-dialog custom-modal-dialog modal-dialog-centered">
        <div class="modal-content custom-modal-content">
            <div class="modal-header  custom-small-modal-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert-modal">
                    <img src="{{ asset('icons/theme-property-alert.svg') }}" alt="" class="mb-3">
                    <h3 class="mt-3 mb-2 fw-600">{{ customTrans('host_listing.action_needed') }}</h3>
                    <p class="mb-3">{{ customTrans('host_listing.license_warning') }}</p>
                    <a href="{{ route('managehost.instruction') }}" class="theme-btn">
                        {{ customTrans('host_listing.learn_more') }}
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{{-- host property alert modal --}}


@push('scripts')
    {{-- <script src="https://cdn.jsdelivr.net/npm/swiper@10/swiper-element-bundle.min.js"></script> --}}
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-select/1.14.0-beta2/js/bootstrap-select.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/moment-hijri@2.1.2/moment-hijri.min.js"></script>
    <script src="{{ asset('js/bootstrap-hijri-datetimepicker.js') }}"></script>
    <script src="{{ asset('intlInput/js/intlTelInputWithUtils.min.js') }}"></script>

    <script>
        const iti_mobile = intlTelInput(document.getElementById("phone-input-mobile"), {
            initialCountry: "sa",
            nationalMode: false,
            formatAsYouType: true,
            formatOnDisplay: true,
            onlyCountries: ['sa', 'ae', 'kw', 'qa', 'om', 'bh', 'iq', 'jo', 'lb', 'sy', 'ps', 'ye', 'dz', 'eg', 'ly', 'ma', 'tn', 'sd', 'bf'],
        });
        const iti_signup_mobile = intlTelInput(document.getElementById("phone-input-signup-mobile"), {
            initialCountry: "sa",
            nationalMode: false,
            formatAsYouType: true,
            formatOnDisplay: true,
            onlyCountries: ['sa', 'ae', 'kw', 'qa', 'om', 'bh', 'iq', 'jo', 'lb', 'sy', 'ps', 'ye', 'dz', 'eg', 'ly', 'ma', 'tn', 'sd', 'bf'],
        });
        document.addEventListener("DOMContentLoaded", function () {
    setTimeout(() => {
        const searchBox = document.querySelector(".iti__country-list input.iti__search-input");
        if (searchBox) {
            searchBox.style.display = "none";
        }
    }, 500); // Wait for dropdown to initialize
});
    </script>
    <script>
        async function verifyUser(e) {
            e.preventDefault();
            $(".iqamaloader").removeClass('d-none');
            $(".verifyIqamaBtn").addClass('d-none');
            const is_vistor = e.target.querySelector("[name='ilmeyakeen']:checked").value == "visitor"
            try {
                const formData = new FormData()
                formData.append("_token", e.target.querySelector("input[name='_token']").value);
                if (!is_vistor) {
                    const dob = e.target.querySelector("#elm-dob").value
                    formData.append("code", e.target.querySelector("#elm-code").value);
                    formData.append("dob", (dob.split("-").reverse()).join("-"));
                } else {
                    formData.append("code", e.target.querySelector("#elm-passport").value);
                    formData.append("nationality", e.target.querySelector("#elm-nationality").value);
                }
                formData.append("cal", !is_vistor ? typeCal : 2);
                const response = await fetch("{{ route('user.elm.verify') }}", {
                    headers: {
                        'accept': 'application/json'
                    },
                    method: "POST",
                    body: formData
                });
                const data = await response.json();
                if (!!data.errors && !!data.errors.code && data.errors.code[0] == @json(customTrans('users_profile.invalid_credential'))) {
                    //-----------WebEngage Integration------------
                    let user_id = @json(auth()->id());
                    let payload = {
                        "NIN Number": $("#elm-code").val(),
                        "Birth Date": $('#elm-dob').val(),
                        "Status": 'Rejected',
                    }
                    if ("{{ Session('user_mode') }}" == 'host') {
                        payload["Host ID"] = user_id;
                    } else {
                        payload["Guest ID"] = user_id;
                    }
                    webEngageTracking(YAQEEN_VERIFICATION, payload);
                    //-----------WebEngage Integration------------
                }
                removeErrors()
                if (!response.ok) throw data;
                if (data.status) {
                    //-----------WebEngage Integration------------
                    let user_id = @json(auth()->id());
                    let payload = {
                        "NIN Number": $("#elm-code").val(),
                        "Birth Date": $('#elm-dob').val(),
                        "Status": 'Verified',
                    }
                    if ("{{ Session('user_mode') }}" == 'host') {
                        payload["Host ID"] = user_id;
                    } else {
                        payload["Guest ID"] = user_id;
                    }
                    webEngageTracking(YAQEEN_VERIFICATION, payload);
                    //-----------WebEngage Integration------------
                    location.reload()
                }
            } catch (errorRes) {
                $(".iqamaloader").addClass('d-none');
                $(".verifyIqamaBtn").removeClass('d-none');
                if (errorRes.hasOwnProperty("errors")) {
                    for (const [key, errors] of Object.entries(errorRes.errors)) {
                        setErrors("elm-" + (is_vistor && key == "code" ? "passport" : key), errors, e.target, key ==
                            "dob");
                    }
                } else {
                    console.log(errorRes);
                }
            } finally {
                $(".iqamaloader").addClass('d-none');
                $(".verifyIqamaBtn").removeClass('d-none');
            }
        }

        let elm_dob;
        let hij = false

        function changeTypeCal(hijri = true) {
            if (hij == hijri) {
                return
            }
            if (!!elm_dob) {
                let newClass = !hijri ? "geo-date-input" : "hijri-date-input"
                let oldClass = !hijri ? "hijri-date-input" : "geo-date-input"
                const placeHolder = hijri ? @json(customTrans('listing.hijricalendar')) : @json(customTrans('listing.georgiancalendar'));
                elm_dob.data("HijriDatePicker").destroy();
                const dobEl = document.getElementById("elm-dob")
                if (dobEl.classList.contains(oldClass)) {
                    dobEl.classList.remove(oldClass)
                }
                dobEl.classList.add(newClass)
                dobEl.value = ""
                dobEl.placeholder = placeHolder
            }
            elm_dob = $("#elm-dob").hijriDatePicker({
                locale: "{{ app()->getLocale() == 'en' ? 'en-US' : 'ar-SA' }}",
                hijriFormat: "iDD-iMM-iYYYY",
                maxDate: moment(),
                showSwitcher: false,
                hijri: hijri,
            })
            hij = hijri
        }
        changeTypeCal()

        function toggleContent(radioName, contentClass) {
            function showContent(selected) {
                let contents = document.querySelectorAll('.' + contentClass);
                contents.forEach(function(content) {
                    content.classList.remove("active");
                });

                let prevSelected = document.querySelector('input[name="' + radioName + '"]:checked');
                if (prevSelected) {
                    let prevTarget = prevSelected.getAttribute('data-target');
                    document.getElementById(prevTarget).querySelector('input[type="text"]').value = "";
                }

                document.getElementById(selected).classList.add("active");
            }

            document.addEventListener("DOMContentLoaded", function() {
                let radioButtons = document.querySelectorAll('input[name="' + radioName + '"]');
                radioButtons.forEach(function(radioButton) {
                    radioButton.addEventListener("change", function() {
                        showContent(this.getAttribute('data-target'));
                    });
                });

                let checkedRadioButton = document.querySelector('input[name="' + radioName + '"]:checked');
                if (checkedRadioButton) {
                    showContent(checkedRadioButton.getAttribute('data-target'));
                }
            });
        }
        toggleContent('ilmeyakeen', 'hijri-calendar-check');
        toggleContent('ilmeyakeen-payement', 'hijri-calendar-check-pay');



        const phoneElement = document.getElementById("phone-prepend");
        if (phoneElement !== null) {
            getCountries() // Get Countries is being used for Dynamic phone numbers
            phoneElement.addEventListener("change", function() {
                countryChangeImpact()
            });
        }
        $('.district-slt-picker').selectpicker();
        // wishlist modal title count
        $('#name').on('input', function() {
            let characterCount = $(this).val().length,
                minimum = 0,
                maximum = 50,
                remaining = maximum - characterCount,
                theCount = $(this).siblings('.character-count-display');

            theCount.text(characterCount + '/' + maximum + ' characters maximum');
        });
        $('#rename-title').on('input', function() {
            let characterCount = $(this).val().length,
                minimum = 0,
                maximum = 50,
                remaining = maximum - characterCount,
                theCount = $(this).siblings('.character-count-display');

            theCount.text(characterCount + '/' + maximum + ' characters maximum');
        });

        // btn enable validation
        $(document).ready(function() {
            $(".input-content").on("input", function() {
                if ($.trim($(this).val()).length > 0) {
                    $(".enable-md-btn").removeClass("disabled")
                } else {
                    $(".enable-md-btn").addClass("disabled")
                }
            });
        });

        // listing slider
        var swiper = new Swiper(".mdReviewSlider", {
            slidesPerView: 1.1,
            spaceBetween: 10,
            freeMode: true,
            navigation: {
                nextEl: '.swiper-button-next',
                prevEl: '.swiper-button-prev',
            },
        });

        // review slider
        var swiper = new Swiper(".mdUlSlider", {
            slidesPerView: 1.1,
            spaceBetween: 10,
            freeMode: true,

            navigation: {
                nextEl: '.swiper-button-next-pro',
                prevEl: '.swiper-button-prev-pro',
            },
        });

        // review slider
        var swiper = new Swiper(".filterSlide", {
            slidesPerView: 5.3,
            spaceBetween: 10,
            freeMode: true,
        });



        // filter modal digit limit
        $(document).ready(function() {
            // Function to allow only numeric input with up to 4 digits for the specified input fields
            $(".editable-rang").on("input", function(e) {
                this.value = this.value.replace(/\D/g,
                    '');
                if (this.value.length > 4) {
                    this.value = this.value.slice(0, 4);
                }
            });

            // Adding and removing "editable-border" class on focus and blur events
            $(".editable-rang").on("focus blur", function() {
                $(this).closest(".price-btn").toggleClass("editable-border", this.focus);
            });
        });

        // OTP new modal
        $(".otp-input").keydown(function(event) {
            // Allow backspace, delete, tab, escape, enter, and numbers
            if (
                event.key === "Backspace" ||
                event.key === "Delete" ||
                event.key === "Tab" ||
                event.key === "Escape" ||
                event.key === "Enter" ||
                !isNaN(parseInt(event.key))
            ) {
                // Allow backspace and delete without restrictions
                if (event.key === "Backspace" || event.key === "Delete") {
                    return;
                }

                // Prevent input if it's a space
                if (event.key === " " || event.key === "Spacebar") {
                    event.preventDefault();
                } else {
                    // Add 'active' class if input length is less than 1
                    if ($(this).val().length < 1) {
                        $(this).addClass('active');
                    }

                    // Allow only up to 4 numbers
                    if ($(this).val().length >= 4) {
                        event.preventDefault();
                    }
                }
            } else {
                event.preventDefault(); // Prevent input for other keys
            }
        });



        // OTP modal end



        $('.verification-code input').on("paste", function(event, pastedValue) {

            $('#txt').val($content)
        });

        // $editor.on('paste, keydown', function() {
        // var $self = $(this);
        //               setTimeout(function(){
        //                 var $content = $self.html();
        //                 $clipboard.val($content);
        //             },100);
        //      });
        // otp input modal js end

        // filter modal digit limit
        $(document).ready(function() {
            // Function to allow only numeric input with up to 4 digits for the specified input fields
            $(".editable-rang").on("input", function(e) {
                this.value = this.value.replace(/\D/g,
                    '');
                if (this.value.length > 4) {
                    this.value = this.value.slice(0, 4);
                }
            });

            // Adding and removing "editable-border" class on focus and blur events
            $(".editable-rang").on("focus blur", function() {
                $(this).closest(".price-btn").toggleClass("editable-border", this.focus);
            });
        });
        // on click back button error going empty not remove or hide
        $(document).ready(function() {
            $(".er-null-btn").click(function() {
                $(".er-null").empty();
            });
        });
        let countries = ''

        function getCountries() { // Fetching Countries
            fetch("{{ route('getCountries') }}")
                .then(response => {
                    if (!response.ok) {
                        throw new Error("Network response was not ok");
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        countries = data.countries
                        const selectElement = document.getElementById("phone-prepend");
                        const option = document.createElement("option");

                        data.countries.forEach(country => {
                            const option = document.createElement("option");
                            option.value = country.phone_code;
                            option.textContent = `${country.short_name} (${country.phone_code})`;
                            selectElement.appendChild(option);
                        });

                        if ('{{ strtolower(env('APP_ENV')) }}' == 'local') {
                            // Code to run when in a local environment
                            option.value = 92;
                            option.textContent = `${'PK'} (${'92'})`;
                            selectElement.appendChild(option);
                        }


                        countryChangeImpact()

                    } else {
                        console.error('Failed to get Countries');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                });
        }

        // const selectedCity = document.getElementById("front-search-field"); // Fetching Selected City
        // getDistricts(selectedCity.value) // Fetching Districts based on cities

        // function getDistricts(city) { // Fetching Districts based on cities
        //     const routeURL = 'getDistricts/' + city;

        //     fetch(routeURL)
        //         .then(response => {
        //             if (!response.ok) {
        //                 throw new Error('Network response was not ok');
        //             }
        //             return response.json();
        //         })
        //         .then(data => {
        //             // Handle the response data here
        //             console.log('Districts:', data);
        //             const selectDistrict = document.getElementById("district-picker");
        //             console.log('selectDistrict', selectDistrict);
        //             selectDistrict.innerHTML = '';

        //             data.districts.forEach(district => {
        //                 const option = document.createElement("option");
        //                 option.value = district;
        //                 option.textContent = district;
        //                 selectDistrict.appendChild(option);
        //             });
        //             $(selectDistrict).selectpicker('refresh');
        //         })
        //         .catch(error => {
        //             console.error('District Error:', error);
        //         });
        // }

        function countryChangeImpact() { // Impact when user change country

            const selectElement = document.getElementById("phone-prepend");
            var selectedCountry = selectElement.options[selectElement.selectedIndex].text;
            selectedLength = selectElement.value.length;

            const regex = /\(.*?\)/g; // Regular expression to match text within parentheses
            selectedCountry = selectedCountry.replace(regex, '');
            selectedCountry = selectedCountry.replace(/\s/g, ''); // Remove spaces

            const phoneInput = document.getElementById("phone-container");
            let content = ''

            if (selectedCountry == "PK") {
                content = `
<input type="number" name="phone" id="phoneNumberInput" class="numberInput" placeholder="56 966 6966" onKeyDown="if(this.value.length==10 && event.keyCode!=8) return false;" onKeyUp="validatePhoneNumber(this);">
        `;
            } else {
                let filteredCountry = countries.filter(function(country) {
                    return country.short_name == selectedCountry
                });
                filteredCountry = filteredCountry[0]
                content = `
<input type="number" name="phone" id="phoneNumberInput" class="numberInput" placeholder="56 966 6966" onKeyDown="if(this.value.length==${filteredCountry.limit} && event.keyCode!=8) return false;" onKeyUp="validatePhoneNumber(this);">
        `;

                const setLimit = document.getElementById("phonelimit");

                if (setLimit) {
                    setLimit.value = filteredCountry.limit;
                }
            }
            phoneInput.innerHTML = content;
        }

        $('#signup_btn').on('click', function(e) {
            var first_name = $('input[name="first_name"]').val();
            var last_name = $('input[name="last_name"]').val();
            if (first_name != '' && last_name != '') {

                //-----------WebEngage Integration------------(Verified)
                payload = {
                    "User": first_name + " " + last_name,
                }
                webEngageTracking(SIGNUP, payload);
                //-----------WebEngage Integration------------
            }

        });

        $('#logout_btn').on('click', function(e) { // Logout for WebEnage
            webengage.user.logout();
        });
        $(document).ready(function() {
            $('.rst-btn').on('click', function() {
                $('#property_code').val('');
                $('#min-price').val(1);
                $('#max-price').val('5000+');
                $('input[name="rb-fillter"]:first').prop('checked', true);
                $('input[name="rb-fillter-two"]:first').prop('checked', true);
                $('input[name="rb-fillter-three"]:first').prop('checked', true);
                if ($('.filter-error').length) {
                    $('.filter-error').remove();
                }
                $('.fltr-chk-btn').prop('disabled', false)
                $('.cust-form-check-input').prop('checked', false);
                $(".slider-handle:first")
                    .attr('aria-valuemin', '1')
                    .attr('aria-valuemax', '5000')
                    .css('left', '0');
                $(".slider-handle:last")
                    .attr('aria-valuemin', '100')
                    .attr('aria-valuemax', '5000')
                    .css('left', '100%');
                $('#price-range').slider('setValue', [1, 5000]);
                $('#district-picker').val('').selectpicker('refresh');
                updateQueryParams('sort', '')
                updateQueryParams('a', '')
                updateQueryParams('pc', '')
                updateQueryParams('d', '')
                updateQueryParams('rbthree', '')
                updateQueryParams('rbtwo', '')
                updateQueryParams('rb', '')
                updateQueryParams('range', '1,5000')
                $("#search_text").html("{{  customTrans('filter.show_results_count', ['num' => '1000+'])}}")
                $('#search_text').prop('disabled', false);

            });
        });

        @if (request()->is('managehost/*') && !request()->is('managehost/instruction'))
            console.log('{{ checkPropertiesVerified('web') }}');

            @if (checkPropertiesVerified('web') == 0)
                document.addEventListener("DOMContentLoaded", function() {
                    var myModal = new bootstrap.Modal(document.getElementById('prop-verify-alert'));
                    // myModal.show();
                });
            @endif
        @endif
    </script>
@endpush
