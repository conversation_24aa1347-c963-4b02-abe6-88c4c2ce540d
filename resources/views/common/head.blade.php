<!DOCTYPE html>
<html {{ Session::get('language') == 'ar' ? 'lang=ar dir=rtl' : 'lang=en dir=ltr' }}>

<head>
    {{-- Load Events Providers --}}
    @if(config('services.google_analytics.enable'))
        <script async src="https://www.googletagmanager.com/gtag/js?id={{config('services.google_analytics.id')}}"></script>
        <script>
            window.dataLayer = window.dataLayer || [];

            function gtag() {
                dataLayer.push(arguments);
            }

            const enable_google_analytics = true

            gtag('js', new Date());
            gtag('config', '{{config('services.google_analytics.id')}}', { debug_mode: true });
        </script>

        <!-- Google Tag Manager -->
        <script>(function (w, d, s, l, i) {
                w[l] = w[l] || []; w[l].push({
                    'gtm.start':
                        new Date().getTime(), event: 'gtm.js'
                }); var f = d.getElementsByTagName(s)[0],
                    j = d.createElement(s), dl = l != 'dataLayer' ? '&l=' + l : ''; j.async = true; j.src =
                        'https://www.googletagmanager.com/gtm.js?id=' + i + dl; f.parentNode.insertBefore(j, f);
            })(window, document, 'script', 'dataLayer', 'GTM-MWWQT4N');</script>
        <!-- End Google Tag Manager -->
    @endif
    @if(config('services.meta_pixel.enable'))
        <script>
            !function (f, b, e, v, n, t, s) {
                if (f.fbq) return; n = f.fbq = function () {
                    n.callMethod ?
                    n.callMethod.apply(n, arguments) : n.queue.push(arguments)
                };
                if (!f._fbq) f._fbq = n; n.push = n; n.loaded = !0; n.version = '2.0';
                n.queue = []; t = b.createElement(e); t.async = !0;
                t.src = v; s = b.getElementsByTagName(e)[0];
                s.parentNode.insertBefore(t, s)
            }(window, document, 'script',
                'https://connect.facebook.net/en_US/fbevents.js');

            const enable_meta_pixel = true

            fbq('init', '{{config('services.meta_pixel.id')}}');
            fbq('track', 'PageView');
        </script>
        <noscript>
            <img height="1" width="1" style="display:none"
                src="https://www.facebook.com/tr?id={{config('services.meta_pixel.id')}}&ev=PageView&noscript=1" />
        </noscript>
    @endif
    @if(config('services.snapchat.enable'))
        <!-- Snap Pixel Code -->
        <script type='text/javascript'>
            (function (e, t, n) {
                if (e.snaptr) return; var a = e.snaptr = function () { a.handleRequest ? a.handleRequest.apply(a, arguments) : a.queue.push(arguments) };
                a.queue = []; var s = 'script'; r = t.createElement(s); r.async = !0;
                r.src = n; var u = t.getElementsByTagName(s)[0];
                u.parentNode.insertBefore(r, u);
            })(window, document,
                'https://sc-static.net/scevent.min.js');

            const enable_snapchat = true

            snaptr('init', '{{config('services.snapchat.id')}}', {
                'user_email': '{{auth()?->user()?->email}}'
            });

            snaptr('track', 'PAGE_VIEW');
        </script>
        <!-- End Snap Pixel Code -->
    @endif
    @if(config('services.tiktok.enable'))
        <!-- TikTok Pixel Code Start -->
        <script>
            const enable_tiktok = true

            !function (w, d, t) {
                w.TiktokAnalyticsObject = t; var ttq = w[t] = w[t] || []; ttq.methods = ["page", "track", "identify", "instances", "debug", "on", "off", "once", "ready", "alias", "group", "enableCookie", "disableCookie", "holdConsent", "revokeConsent", "grantConsent"], ttq.setAndDefer = function (t, e) { t[e] = function () { t.push([e].concat(Array.prototype.slice.call(arguments, 0))) } }; for (var i = 0; i < ttq.methods.length; i++)ttq.setAndDefer(ttq, ttq.methods[i]); ttq.instance = function (t) {
                    for (
                        var e = ttq._i[t] || [], n = 0; n < ttq.methods.length; n++)ttq.setAndDefer(e, ttq.methods[n]); return e
                }, ttq.load = function (e, n) {
                    var r = "https://analytics.tiktok.com/i18n/pixel/events.js", o = n && n.partner; ttq._i = ttq._i || {}, ttq._i[e] = [], ttq._i[e]._u = r, ttq._t = ttq._t || {}, ttq._t[e] = +new Date, ttq._o = ttq._o || {}, ttq._o[e] = n || {}; n = document.createElement("script")
                        ; n.type = "text/javascript", n.async = !0, n.src = r + "?sdkid=" + e + "&lib=" + t; e = document.getElementsByTagName("script")[0]; e.parentNode.insertBefore(n, e)
                };
                ttq.load('{{config('services.tiktok.id')}}');
                ttq.page();
            }(window, document, 'ttq');

            ttq.identify({
                "email": null,
                "phone_number": null,
                "external_id": "{{ Str::uuid()->toString() }}" // string. Any unique identifier, such as loyalty membership IDs, user IDs, and external cookie IDs.It must be hashed with SHA-256 on the client side.
            });
        </script>
        <!-- TikTok Pixel Code End -->
    @endif



    <script src="{{ config('app.url') . '/js/events.js' . '?' . now()->timestamp }}"></script>

    {{-- if the route is property details --}}
    {{-- fire view content event --}}
    @if(request()->route()->getName() === 'property.single')
        <script>
            trackEvent('VIEW_CONTENT', {
                price: {{ number_format($result->total_price, 2, '.', '') }},
                currency: '{{Session::get('currency')}}',
                item_ids: ['{{$result->property_code}}'],
                item_category: 'product',
                number_items: {{ (int) $result->number_of_days}},
                user_email: '{{ auth()->user()?->email }}',
                user_phone_number: '{{ auth()->user()?->phone }}'
            }, ['snap'])
        </script>
    @endif

    @if(in_array(request()->route()->getName(), ['paymentCallbackWallet', 'paymentCallback']) && !empty($snapPixelData))
        <script>
            trackEvent('PURCHASE', JSON.parse({!! json_encode($snapPixelData) !!}), ['snap'])
            trackEvent('PlaceAnOrder', JSON.parse({!! json_encode($tikPixelData) !!}), ['tik'])
        </script>
    @endif

    <script src="//rum-static.pingdom.net/pa-67580d5a7a5fd400120002b0.js" async></script>

    {{-- END Load Events Providers --}}
    <meta name="google-site-verification" content="HgzAaJPFG-8s5KBUHQY-eLxTyoHcGUf0t2Hj8ITIr3Y" />
    <meta name="apple-itunes-app" content="app-id=**********, app-argument=https://static.darent.com">
    @if (isset($en_description) && Session::get('language') == 'en')
        <meta name="description" content="{{ $en_description }}">
    @elseif (isset($ar_description) && Session::get('language') == 'ar')
        <meta name="description" content="{{ $ar_description }}">
    @else
    @endif
    @if (isset($en_keywords) && Session::get('language') == 'en')
        <meta name="keywords" content="{{ $en_keywords }}">
    @elseif (isset($ar_keywords) && Session::get('language') == 'ar')
        <meta name="keywords" content="{{ $ar_keywords }}">
    @else
    @endif

    @if (isset($meta_canonical) && $meta_canonical !== 'NULL' && $meta_canonical !== '')
        <link rel="canonical" href="{{ $meta_canonical }}">
    @else
        <link rel="canonical" href="{{ url()->current() }}">
    @endif

    @auth
        <script>
            var check_card = "{{ auth()->user()->DefaultCard }}";
        </script>
    @endauth

    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- Metas For sharing property in social media -->
    <meta property="og:url" content="{{ isset($shareLink) ? $shareLink : url('/') }}" />
    <meta property="og:type" content="article" />
    {{--
    <meta property="og:title"
        content="{{ $title ?? Helpers::meta(!isset($exception) ? Route::current()->uri() : '', 'title') }}" /> --}}
    <meta property="og:description"
        content="{{ isset($result->property_description->summary) ? $result->property_description->summary : Helpers::meta(!isset($exception) ? Route::current()->uri() : '', 'description') }}" />
    <meta property="og:image"
        content="{{ isset($property_id) && !empty($property_id && isset($property_photos[0]->photo)) ? asset('images/property/' . $property_id . '/' . $property_photos[0]->photo) : (defined('BANNER_URL') ? BANNER_URL : '') }}" />

    <meta name="facebook-domain-verification" content="sk256mnffjvrdijn292cylpopyiyc7" />
    <meta name="csrf-token" content="{{ csrf_token() }}">


    <link rel="stylesheet preload" href="{{ asset('css/bootstrap-slider.min.css') }}" as="style" type="text/css" />

    @if (!empty($favicon))
        <link rel="shortcut icon" href="{{ $favicon }}" />
    @endif

    {{-- <title>{{ $title ?? Helpers::meta(!isset($exception) ? Route::current()->uri() : '', 'title') }}
        {{ $additional_title ?? '' }} </title> --}}
    @if (isset($en_title) && Session::get('language') == 'en')
        <title>{{ $en_title }}</title>
    @elseif (isset($ar_title) && Session::get('language') == 'ar')
        <title>{{ $ar_title }}</title>
    @else
        <title>{{ $title ?? Helpers::meta(!isset($exception) ? Route::current()->uri() : '', 'title') }}
            {{ $additional_title ?? '' }}
        </title>
    @endif
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="csrf-token" content="{{ csrf_token() }}" />

    <!-- CSS  new version start-->
    {{-- @vite('resources/js/app.js') --}}

    @stack('css')
    <link rel="shortcut icon" type="image/png" href="{{ asset('fav-icon/favicon.png') }}" />
    <link rel="stylesheet preload" href="{{ asset('bootstrap/css/bootstrap-grid.min.css') }}" as="style" crossorigin />
    <link rel="stylesheet preload" href="{{ asset('bootstrap/css/bootstrap.min.css') }}" as="style" crossorigin />
    <link rel="stylesheet preload" href="{{ asset('css/daterangepicker.css') }}" as="style" crossorigin />
    <link rel="stylesheet preload" href="{{ asset('css/slick.css') }}" as="style" crossorigin />
    <link rel="stylesheet preload" href="{{ asset('css/slick-theme.css') }}" as="style" crossorigin />
    <link rel="stylesheet preload" href="{{ asset('cdns/css/all.min.css') }}" as="style" crossorigin />


    @php
        $helper = new App\Http\Helpers\Common();
    @endphp

    {{-- ============ All CDN Local Start ============ --}}


    <link rel="stylesheet preload" href="{{ asset('cdns/css/aos.css') }}" as="style" crossorigin />
    <link rel="stylesheet preload" href="{{ asset('cdns/css/remixicon.css') }}" as="style" crossorigin />
    <link rel="stylesheet preload" href="{{ asset('cdns/css/swipper-bundle.min.css') }}" as="style" crossorigin />
    {{--
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.css" /> --}}
    <link rel="stylesheet preload" href="{{ asset('cdns/css/dropzone.min.css') }}" as="style" crossorigin />
    {{--
    <link rel="stylesheet" href="https://unpkg.com/dropzone@5/dist/min/dropzone.min.css" type="text/css" /> --}}
    <link rel="stylesheet preload" href="{{ asset('cdns/css/bootstrap-select.min.css') }}" as="style" crossorigin />
    {{--
    <link rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-select/1.14.0-beta2/css/bootstrap-select.min.css"> --}}

    {{-- ============ All CDN Local End ============ --}}


    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/Swiper/10.3.1/swiper-bundle.css"
        integrity="sha512-cAtZ0Luj6XlQ7YGgi5mPW0szI2z/2+btPjOqVEqK3z4h1/qojUwvQyTcocgKKOFv8noUFH5GOuhheX7PeDwwPA=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link rel="stylesheet preload" href="{{ asset('css/style.css') }}?v={{ $currentAppVersion}}" as="style"
        crossorigin />

    <link rel="stylesheet preload" href="{{ asset('css/responsive.css') }}?v={{ $currentAppVersion}}" as="style"
        crossorigin />

    @if(config('services.recaptcha_v3.enable'))
    <script src="https://www.google.com/recaptcha/api.js?render={{config('services.recaptcha_v3.siteKey')}}"></script>
    @endif


    <!--CSS new version end-->

    @if (app()->getLocale() == 'ar')
        <link rel="stylesheet preload" href="{{ asset('css/style_arabic.css') }}?v={{ $currentAppVersion}}" as="style"
            crossorigin />
    @endif

    {{--
    <link rel="conanical" href="https://www.darent.com"> --}}
    {{-- @vite('resources/js/app.js') --}}
    <noscript>
        <img height="1" width="1" style="display:none"
            src="https://www.facebook.com/tr?id=868683851060478&ev=PageView&noscript=1" />
    </noscript>


    <script>
        let citySelection;
        let TypeName;

        let appEnvironment = "{{ app()->environment() }}";
    </script>




</head>

<body>
    <!-- Google Tag Manager (noscript) -->
    <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-MWWQT4N" height="0" width="0"
            style="display:none;visibility:hidden"></iframe></noscript>
    <!-- End Google Tag Manager (noscript) -->

    {{-- <a class="blkd"></a> --}}