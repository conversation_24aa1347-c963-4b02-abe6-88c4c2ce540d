@php
    function loadImage($path, $property_id) {

        if (is_null($path)) return '/images/default-image.png';

        if (str_starts_with($path, 'http'))
            return $path;

        if (str_contains($path, 'images/property'))
            return asset($path);

        $base = '/images/property';
        if (!is_null($property_id)) $base .= '/' . $property_id;
        return  asset($base . (str_starts_with($path, '/') ? $path : '/' . $path));
    }
@endphp

{{-- Property Gallery Component --}}
<div class="property-gallery for-desktop">
    <div class="row">
        <div class="col-6">
            <div class="main-image">
                <a data-fancybox="gallery-desktop"
                    href="{{ isset($photos[0]) ? loadImage($photos[0]->photo, property_id: $property->id) : asset('images/default-image.png') }}">
                    <img src="{{ asset('images/loader.gif') }}"
                        data-src="{{ isset($photos[0]) ? loadImage($photos[0]->photo,  property_id: $property->id) : asset('images/default-image.png') }}"
                        class="main-image lazy" alt="" loading="lazy">
                </a>
                @if(in_array($property->property_code,\App\Models\Properties::EXCLUSIVE_PROPERTIES))
                    <span class="custom-tag exclusive-tag">{{ customTrans('property_single.exclusive') }}</span>
                @endif
            </div>
        </div>

        <div class="col-6 px-2">
            <div class="row">
                @for ($i = 1; $i < (count($photos) > 5 ? 5 : count($photos)); $i++)
                    <div class="col-6 px-2">
                        <div class="inner-image">
                            <a class="open-gallery">
                                <img src="{{ asset('images/loader.gif') }}"
                                    data-src="{{ loadImage($photos[$i]->photo,  property_id: $property->id) }}"
                                    class="inner-image image-show lazy" rel="album-1" alt="">
                            </a>
                        </div>
                    </div>
                @endfor
                @for ($i = 1; $i < count($photos); $i++)
                    <a data-fancybox="gallery-desktop" class="get-gallery-click"
                        href="{{ loadImage($photos[$i]->photo,  property_id: $property->id) }}" style="display: none;">
                        <img src="{{ asset('images/loader.gif') }}"
                            data-src="{{ loadImage($photos[$i]->photo,  property_id: $property->id) }}"
                            class="inner-image image-show lazy" style="display: none;" rel="album-1"
                            alt="">
                    </a>
                @endfor
            </div>
        </div>
    </div>
</div>

<div class="property-gallery for-mobile">
    <div class="pg-slide">
        <div class="mb-slid">
            @for ($i = 0; $i < count($photos); $i++)
                <div class="mb-sl-in">
                    <div class="inner-image">
                        <a data-fancybox="gallery-mobile" href="{{ loadImage($photos[$i]->photo,  property_id: $property->id) }}">
                            <img src="{{ asset('images/loader.gif') }}"
                                data-src="{{ loadImage($photos[$i]->photo,  property_id: $property->id) }}"
                                class="inner-image image-show lazy" rel="album-2" alt="">
                        </a>
                    </div>
                </div>
            @endfor
        </div>
        <span class="pagingInfo"></span>
    </div>
</div>
