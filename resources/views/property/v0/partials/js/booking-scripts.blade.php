{{-- Booking JavaScript Functionality --}}
<script type="text/javascript">
    // Ensure required variables are available (only declare if not already defined)
    if (typeof window.appEnvironment === 'undefined') {
        window.appEnvironment = "{{ app()->environment() }}";
    }

    if (typeof window.consolelog === 'undefined') {
        window.consolelog = function(content) {
            // Console logging disabled for production
        }
    }

    // Calendar content for date picker
    if (typeof window.calendarContent === 'undefined') {
        let currentLocaleValue = document.querySelector('html').lang || 'ar';
        let language = currentLocaleValue;

        function getMonthAndDayNames(language) {
            const monthNames = {
                'ar': ["يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو", "يوليو", "أغسطس", "سبتم<PERSON><PERSON>", "أكتو<PERSON><PERSON>", "نوفم<PERSON><PERSON>", "ديسمبر"],
                'en': ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"]
            };
            const daysOfWeek = {
                'ar': ["الأحد", "الاثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة", "السبت"],
                'en': ["Su", "Mo", "Tu", "We", "Th", "Fr", "Sa"]
            };
            const labels = {
                'ar': { applyLabel: 'اختار', cancelLabel: 'إلغاء' },
                'en': { applyLabel: 'Apply', cancelLabel: 'Cancel' }
            };
            return {
                monthNames: monthNames[language] || monthNames['ar'],
                daysOfWeek: daysOfWeek[language] || daysOfWeek['ar'],
                labels: labels[language] || labels['ar']
            };
        }

        window.calendarContent = getMonthAndDayNames(language);
        window.calendarLabels = window.calendarContent.labels;
    }

    var allothers = 0;
    var guest_service_charge = "{{ $guest_service_charge ?? 0 }}";

    $(function() {
        // Skip daterangepicker initialization on mobile v0 (handled by mobile adapter)
        if (window.isMobileV0) {
            return;
        }

        var unavailableDates = {!! json_encode($booked_dates ?? []) !!};
        var dates = [];
        for (var i = 0; i < unavailableDates.length; i++) {
            dates.push(unavailableDates[i].date);
        }

        // Parse initial dates from hidden inputs (MM/DD/YYYY format)
        var checkinValue = $('#url_checkin').val();
        var checkoutValue = $('#url_checkout').val();

        // Convert MM/DD/YYYY to moment objects for date picker
        var startDate = checkinValue ? moment(checkinValue, 'MM/DD/YYYY') : moment();
        var endDate = checkoutValue ? moment(checkoutValue, 'MM/DD/YYYY') : moment().add(1, 'day');

        // Ensure valid dates
        if (!startDate.isValid()) {
            startDate = moment();
        }
        if (!endDate.isValid() || endDate.isSameOrBefore(startDate)) {
            endDate = startDate.clone().add(1, 'day');
        }

        $('input[name="daterange"]').daterangepicker({
            opens: 'center',
            startDate: startDate,
            endDate: endDate,
            minDate: moment(),
            locale: {
                monthNames: window.calendarContent.monthNames,
                daysOfWeek: window.calendarContent.daysOfWeek,
                applyLabel: window.calendarLabels.applyLabel,
                cancelLabel: window.calendarLabels.cancelLabel
            },
            isInvalidDate: function(date) {
                return dates.indexOf(date.format('YYYY-MM-DD')) != -1;
            }
        }, function(start, end, label) {
            var guest = $('#number_of_guests').val();

            // Update hidden inputs with MM/DD/YYYY format (like original)
            $('#url_checkin').val(start.format('MM/DD/YYYY'));
            $('#url_checkout').val(end.format('MM/DD/YYYY'));

            // Update URL with DD-MM-YYYY format (like original)
            const newUrl = new URL(window.location.href);
            newUrl.searchParams.set('checkin', start.format('DD-MM-YYYY'));
            newUrl.searchParams.set('checkout', end.format('DD-MM-YYYY'));
            history.pushState(null, '', newUrl);

            // Send DD-MM-YYYY to API (like original)
            price_calculation(start.format('DD-MM-YYYY'), end.format('DD-MM-YYYY'), guest);
        });

        // Set initial display value for the date picker after initialization
        setTimeout(function() {
            var picker = $('input[name="daterange"]').data('daterangepicker');
            if (picker) {
                // Display in MM/DD/YYYY format like the original
                $('input[name="daterange"]').val(startDate.format('MM/DD/YYYY') + ' - ' + endDate.format('MM/DD/YYYY'));
                $('#url_checkin').val(startDate.format('MM/DD/YYYY'));
                $('#url_checkout').val(endDate.format('MM/DD/YYYY'));
            } else {
                // Fallback if daterangepicker is not initialized yet
                $('input[name="daterange"]').val(startDate.format('MM/DD/YYYY') + ' - ' + endDate.format('MM/DD/YYYY'));
                $('#url_checkin').val(startDate.format('MM/DD/YYYY'));
                $('#url_checkout').val(endDate.format('MM/DD/YYYY'));
            }
        }, 100);

        $('input[name="daterange"]').on('show.daterangepicker', function(ev, picker) {
            $('.daterangepicker').addClass('cust-double-rangepicker');
        });

        $('.applyBtn').on('click', function(ev) {
            let picker = $('input[name="daterange"]').data('daterangepicker');
            if (picker.startDate && !picker.endDate) {
                let nextDay = picker.startDate.clone().add(1, 'day');
                picker.endDate = nextDay;
                $('input[name="daterange"]').data('daterangepicker').endDate = picker.endDate;
                $('input[name="daterange"]').val(picker.startDate.format('MM-DD-YYYY') + ' - ' + picker.endDate.format('MM-DD-YYYY'));  // Match original format
            }
        });

        let csrfToken = $('meta[name="csrf-token"]').attr('content');

        $('input[name="daterange"]').on('apply.daterangepicker', function(ev, picker) {
            var startDate = picker.startDate.format('MM/DD/YYYY');  // Match original format
            var endDate = picker.endDate.format('MM/DD/YYYY');      // Match original format
            $('#url_checkin').val(startDate);
            $('#url_checkout').val(endDate);
            updatePropertyView(null, $('#url_checkin').val(), $('#url_checkout').val());

            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': csrfToken
                }
            });

            $.ajax({
                url: "{{ route('session.v0.set') }}",
                method: 'POST',
                data: {
                    startDate: startDate,
                    endDate: endDate
                },
                success: function(response) {
                    setTimeout(() => {
                        handleWalletPayChange();
                    }, 500);
                },
                error: function(xhr, status, error) {
                    console.error('Error setting session value:', error);
                }
            });
        });
    });

    $(function() {
        // Skip initial price calculation on mobile v0 (handled by mobile adapter)
        if (window.isMobileV0) {
            return;
        }

        var guest = $('#number_of_guests').val() || 1;
        var checkin = $('#url_checkin').val() || '';  // This will be MM/DD/YYYY format
        var checkout = $('#url_checkout').val() || ''; // This will be MM/DD/YYYY format

        // Function to update URL with valid dates
        function updateURL(checkinDate, checkoutDate) {
            try {
                const newUrl = new URL(window.location.href);
                newUrl.searchParams.set('checkin', checkinDate);
                newUrl.searchParams.set('checkout', checkoutDate);
                history.replaceState(null, '', newUrl);
            } catch (e) {
                console.log('Could not update URL:', e);
            }
        }

        // Set default dates if empty (in MM/DD/YYYY format to match original)
        if (!checkin || checkin.trim() === '') {
            checkin = moment().format('MM/DD/YYYY');
            $('#url_checkin').val(checkin);
        }
        if (!checkout || checkout.trim() === '') {
            checkout = moment().add(1, 'day').format('MM/DD/YYYY');
            $('#url_checkout').val(checkout);
        }

        // Convert MM/DD/YYYY to DD-MM-YYYY for URL and API
        var checkinForURL = moment(checkin, 'MM/DD/YYYY').format('DD-MM-YYYY');
        var checkoutForURL = moment(checkout, 'MM/DD/YYYY').format('DD-MM-YYYY');

        // Update URL with DD-MM-YYYY format (like original)
        updateURL(checkinForURL, checkoutForURL);

        // Run initial price calculation with DD-MM-YYYY format
        setTimeout(function() {
            price_calculation(checkinForURL, checkoutForURL, guest);
        }, 1000);
    });

    function price_calculation(checkin = null, checkout = null) {
        // Show skeleton loading when calculating prices
        showBookingSkeleton();

        // Also show mobile skeleton if mobile elements exist
        if (typeof showMobileBookingSkeleton === 'function') {
            showMobileBookingSkeleton();
        }

        var platformId = {{ $property->platform_id ?? 1 }};
        var property_id = $('#property_id').val();
        var dataURL = "{{ route('property.v0.price') }}";
        var guest = $('#number_of_guests').val() || 1;
        allothers = 0;
        $('#booking_form #go_to_payment').prop('disabled', true);

        // Function to safely format dates
        function formatDateForAPI(dateInput) {
            if (!dateInput || dateInput === 'Invalid date' || dateInput.trim() === '') {
                return moment().format('DD-MM-YYYY');
            }

            // Try to parse the date in various formats
            var date = null;
            var formats = ['DD-MM-YYYY', 'MM/DD/YYYY', 'YYYY-MM-DD', 'DD/MM/YYYY'];

            for (var i = 0; i < formats.length; i++) {
                var parsed = moment(dateInput, formats[i], true);
                if (parsed.isValid()) {
                    date = parsed;
                    break;
                }
            }

            // If no format worked, try flexible parsing
            if (!date || !date.isValid()) {
                date = moment(dateInput);
                if (!date.isValid()) {
                    // Last resort: use current date
                    date = moment();
                }
            }

            return date.format('DD-MM-YYYY');
        }

        // Get and validate dates
        var get_checkin = checkin || $('#url_checkin').val();
        var get_checkout = checkout || $('#url_checkout').val();

        // Format dates safely
        var _inDate = formatDateForAPI(get_checkin);
        var _outDate = formatDateForAPI(get_checkout);

        // Ensure checkout is after checkin
        var checkinMoment = moment(_inDate, 'DD-MM-YYYY');
        var checkoutMoment = moment(_outDate, 'DD-MM-YYYY');

        if (checkoutMoment.isSameOrBefore(checkinMoment)) {
            checkoutMoment = checkinMoment.clone().add(1, 'day');
            _outDate = checkoutMoment.format('DD-MM-YYYY');
        }



        $.ajax({
            url: dataURL,
            data: {
                '_token': "{{ csrf_token() }}",
                'checkin': _inDate,
                'checkout': _outDate,
                'guest_count': guest,
                'property_id': property_id,
                'language': "{{ app()->getLocale() }}",
                'currency': "{{ Session::get('currency', 'SAR') }}"
            },
            type: 'post',
            dataType: 'json',
            beforeSend: function() {
                show_loader();
            },
            success: function(result) {
                if (window.appEnvironment == 'local') {
                    window.consolelog(result);
                }

                // Handle error responses from backend
                if (result.status === 'error') {

                    // Show error state
                    $('#go_to_payment').prop('disabled', true);
                    $('.book_btn').addClass('d-none');
                    $('#book_it_disabled').removeClass('d-none');
                    $('#book_it_disabled_message .not-available-detail-tag').text(result.message || 'Error calculating price');
                    hide_loader();
                    return;
                }

                // Update main price display using only API data
                if (result.total_night_price_with_symbol) {
                    $('#nightPrice').text(result.total_night_price_with_symbol);
                } else if (result.per_night_price_with_symbol) {
                    $('#nightPrice').text(result.per_night_price_with_symbol);
                }

                                // Helper function to safely extract numeric value
                function getNumericValue(value) {
                    if (!value) return 0;
                    if (typeof value === 'number') return value;
                    if (typeof value === 'string') {
                        return parseFloat(value.replace(/[^\d.-]/g, '')) || 0;
                    }
                    return 0;
                }

                // Show/hide pricing rows based on values
                // Service fee
                if (result.service_fee && getNumericValue(result.service_fee) > 0) {
                    $('.service_price').show();
                    $('#service_fee').text(result.service_fee_with_symbol || result.service_fee);
                } else {
                    $('.service_price').hide();
                }

                // Additional guest fee
                if (result.additional_guest && getNumericValue(result.additional_guest) > 0) {
                    $('.additional_price').show();
                    $('#additional_guest').text(result.additional_guest_fee_with_symbol || result.additional_guest);
                } else {
                    $('.additional_price').hide();
                }

                // Security fee
                if (result.security_fee && getNumericValue(result.security_fee) > 0) {
                    $('.security_price').show();
                    $('#security_fee').text(result.security_fee_with_symbol || result.security_fee);
                } else {
                    $('.security_price').hide();
                }

                // Cleaning fee
                if (result.cleaning_fee && getNumericValue(result.cleaning_fee) > 0) {
                    $('.cleaning_price').show();
                    $('#cleaning_fee').text(result.cleaning_fee_with_symbol || result.cleaning_fee);
                } else {
                    $('.cleaning_price').hide();
                }

                // IVA tax
                if (result.iva_tax && getNumericValue(result.iva_tax) > 0) {
                    $('.iva_tax').show();
                    $('#iva_tax').text(result.iva_tax_with_symbol || result.iva_tax);
                } else {
                    $('.iva_tax').hide();
                }

                // Accommodation tax
                if (result.accomodation_tax && getNumericValue(result.accomodation_tax) > 0) {
                    $('.accomodation_tax').show();
                    $('#accomodation_tax').text(result.accomodation_tax_with_symbol || result.accomodation_tax);
                } else {
                    $('.accomodation_tax').hide();
                }

                // Wallet amount
                if (result.wallet_amount && getNumericValue(result.wallet_amount) > 0) {
                    $('.wallet_row').show();
                    $('#wallet-value').text(result.wallet_amount);
                } else {
                    $('.wallet_row').hide();
                }

                $('.append_date').remove();

                if(result.status == 'available'){
                    $('#booking_form #go_to_payment').prop('disabled', false);

                    @auth
                        $.ajax({
                            url: "{{ route('booking.v0.track') }}",
                            type: "POST",
                            data: {
                                _token: "{{ csrf_token() }}",
                                property_id: property_id,
                                booking_stage: "dates_selected",
                                price: result.per_night || "{{ numberFormat($property->propertyPrice->price ?? 0, 2) }}",
                                city: "{{ $property->property_address->city ?? '' }}"
                            }
                        });
                    @endauth
                }

                if (result.status == 'Not available') {
                    $('.pricing-breakdown').addClass('d-none');
                    $('#book_it_disabled').removeClass('d-none');
                    $('.instant-book-btn').prop('disabled', true);
                } else if (result.status == 'minimum stay') {
                    $('.pricing-breakdown').addClass('d-none');
                    $('#book_it_disabled').removeClass('d-none');
                    $('.instant-book-btn').prop('disabled', true);
                } else if (result.status == 'nights become min') {
                    $('.pricing-breakdown').addClass('d-none');
                    $('#book_it_disabled').removeClass('d-none');
                    $('.instant-book-btn').prop('disabled', true);
                } else if (result.status == 'nights become max') {
                    $('.pricing-breakdown').addClass('d-none');
                    $('#book_it_disabled').removeClass('d-none');
                    $('.instant-book-btn').prop('disabled', true);
                } else {
                    @if (!!auth()->id() && $property->host_id != auth()->id())
                        @if (!empty($is_contact))
                            checkForIquiry();
                        @endif
                    @endif

                    // Update pricing breakdown for the new simplified design
                    var append_date = '';
                    if (result.date_with_price && result.date_with_price.length > 0) {
                        for (var i = 0; i < result.date_with_price.length; i++) {
                            var dateParts = result.date_with_price[i]['date'].split('-');
                            var year = dateParts[0];
                            var month = dateParts[1];
                            var day = dateParts[2];
                            var date = new Date(year, month - 1, day);

                            if (!isNaN(date.getTime())) {
                                var formattedDate = ('0' + date.getDate()).slice(-2) + '-' +
                                                  ('0' + (date.getMonth() + 1)).slice(-2) + '-' +
                                                  date.getFullYear();

                                // Use the price as-is if it already contains currency, otherwise add currency
                                var priceValue = result.date_with_price[i]['price'];
                                var priceWithCurrency;

                                // Check if price already contains currency symbol
                                if (priceValue.toString().includes('ريال') || priceValue.toString().includes('SAR')) {
                                    priceWithCurrency = priceValue;
                                } else {
                                    priceWithCurrency = 'SAR ' + priceValue;
                                }

                                append_date += '<div class="date-row">' +
                                              '<div class="date-left">' + formattedDate + '</div>' +
                                              '<div class="date-right">' + priceWithCurrency + '</div>' +
                                              '</div>';
                            }
                        }

                        // Insert into the new date breakdown container
                        $('#append_date').html(append_date);
                    } else {
                        // Set default date breakdown
                        var checkinMoment = moment($('#url_checkin').val(), 'MM/DD/YYYY');
                        var defaultPrice = result.per_night_price_with_symbol || '250 SAR';
                        var defaultDate = checkinMoment.format('DD-MM-YYYY');

                        append_date = '<div class="date-row">' +
                                     '<div class="date-left">' + defaultDate + '</div>' +
                                     '<div class="date-right">' + defaultPrice + '</div>' +
                                     '</div>';
                        $('#append_date').html(append_date);
                    }

                    // Update night summary in the simplified design
                    $('#total_night_count').text(result.total_nights || 1);
                    $('#per_night_price').text(result.per_night_price_with_symbol || result.per_night_price || '250');
                    $('#total_night_price').text(result.total_night_price_with_symbol || result.total_night_price || '250');

                    // Handle hidden inputs for pricing
                    if ($('input[name="total_night_price"]').length > 0) {
                        $('input[name="total_night_price"]').val(result.total_night_price);
                    } else {
                        var hiddenInput = $('<input>').attr({
                            type: 'hidden',
                            name: 'total_night_price',
                            id: 'total_night_price_hidden',
                            value: result.total_night_price
                        });
                        var amountForCoupon = $('<input>').attr({
                            type: 'hidden',
                            id: 'amount_for_coupon',
                            value: result.total_for_coupon
                        });
                        $('#hosting_id').after(hiddenInput);
                        $('#total_night_price_hidden').after(amountForCoupon);
                    }

                    // Update total
                    if (result.total_with_discount_with_symbol) {
                        $('#total').text(result.total_with_discount_with_symbol);
                        $('#total').data('total', result.total_with_discount);
                        $('#total').data('wallet-total', result.total_with_discount);
                    } else if (result.total_with_symbol) {
                        $('#total').text(result.total_with_symbol);
                        $('#total').data('total', result.total);
                        $('#total').data('wallet-total', result.total);
                    } else {
                        $('#total').text(result.total || '277.50');
                    }

                    // Show pricing breakdown and enable booking
                    $('.pricing-breakdown').removeClass('d-none');
                    $('#book_it_disabled').addClass('d-none');
                    $('.instant-book-btn').prop('disabled', false);

                    if ($('#coupon').val()) {
                        applycoupon();
                    }
                }

                var host = "{{ $property->host_id == @Auth::guard('users')->user()->id ? '1' : '' }}";
                if (host == '1') $('.instant-book-btn').prop('disabled', true);

                // WebEngage Integration
                let user = window.DEFAULT_USER;
                let authcheck = '{{ auth()->check() }}';
                if (authcheck) {
                    @auth
                        var isHost = @json(auth()->user()->is_host);
                    @endauth
                    user = isHost == true ? 'Host' : window.DEFAULT_USER;
                }
                placeSelectedPayload = {
                    'Name': '{{ $property->name }}',
                    'Unit Code': '{{ $property->property_code }}',
                    'Cost Per Night': result.per_night,
                    'Category Name': '{{ $property->propertyType->name ?? '' }}',
                    'User': user
                };
                webEngageTracking(window.PLACE_SELECTED, placeSelectedPayload);

                $('#wallet-pay').trigger('change');
            },
            error: function(request, error) {
                // This callback function will trigger on unsuccessful action
                $('#go_to_payment').prop('disabled', true);
                $('.instant-book-btn').prop('disabled', true);
                // Hide skeleton even on error
                hideBookingSkeleton();
            },
            complete: function() {
                $('.pricing-breakdown').removeClass('d-none');
                hide_loader();
                // Hide skeleton and show actual content
                hideBookingSkeleton();

                // Also hide mobile skeleton if mobile functions exist
                if (typeof hideMobileBookingSkeleton === 'function') {
                    hideMobileBookingSkeleton();
                }
            }
        });
    }

    function updatePropertyView(clicked = null, checkin = null, checkout = null) {
        $.ajax({
            type: 'POST',
            url: '{{ route("property.v0.view") }}',
            data: {
                clicked: clicked,
                checkin: checkin,
                checkout: checkout,
                _token: "{{ csrf_token() }}",
                property_id: $('#property_id').val()
            },
            success: function(response) {
                // Success handled silently
            },
            error: function(xhr) {
                // Error handled silently
            }
        });
    }

    function show_loader() {
        $('#loader').removeClass('display-off');
        $('#pagination').addClass('d-none');
    }

    function hide_loader() {
        $('#loader').addClass('display-off');
        $('#pagination').removeClass('d-none');
    }

    function checkForIquiry() {
        const dates = $('input[name="daterange"]').val().split('-');
        const adult = +document.querySelector('input[name=\'guest_adult\']').value;
        const inqBtnEl = document.getElementById('inquiry-btn');
        if (dates.length == 2 && !!adult) {
            if (inqBtnEl && inqBtnEl.classList.contains('d-none')) {
                inqBtnEl.classList.remove('d-none');
            }
        } else if (inqBtnEl && !inqBtnEl.classList.contains('d-none')) {
            inqBtnEl.classList.add('d-none');
        }
    }

    // Guest count change handler
    $('#number_of_guests').on('change', function() {
        var guest = $(this).val();
        var checkin = $('#url_checkin').val();
        var checkout = $('#url_checkout').val();

        if (checkin && checkout) {
            var checkinFormatted = moment(checkin, 'MM/DD/YYYY').format('DD-MM-YYYY');
            var checkoutFormatted = moment(checkout, 'MM/DD/YYYY').format('DD-MM-YYYY');
            price_calculation(checkinFormatted, checkoutFormatted, guest);
        }
    });

    // Booking button click handler
    $(document).on('click', '#go_to_payment', function(event) {
        event.preventDefault();

        // Track start booking event for initiated stage
        // @auth
        //     $.ajax({
        //         url: "{{ route('booking.v0.track') }}",
        //         type: "POST",
        //         data: {
        //             _token: "{{ csrf_token() }}",
        //             property_id: "{{ $property->id }}",
        //             booking_stage: "initiated",
        //             price: $('#total').data('total') || "{{ numberFormat($property->propertyPrice->price ?? 0, 2) }}",
        //             city: "{{ $property->property_address->city ?? '' }}"
        //         }
        //     });
        // @endauth

        if ("{{ auth()->check() }}") {
            // proceedWithBooking();
            $('#booking_form').submit();
        } else {
            // For guest users, show login modal instead of redirecting
            const currentUrl = new URL(window.location.href);
            let checkin = $('#url_checkin').val();
            let checkout = $('#url_checkout').val();
            currentUrl.searchParams.set('checkin', checkin);
            currentUrl.searchParams.set('checkout', checkout);
            localStorage.setItem('postLoginBooking', $('#property_id').val());
            window.history.replaceState({}, '', currentUrl);

            // Show login modal
            $('#staticBackdrop').modal('show');
        }
    });

    function proceedWithBooking() {
        var formData = $('#booking_form').serialize();
        $('#go_to_payment').prop('disabled', true);

        if (!$('#booking_form').data('submitted')) {
            // Set default payment method (since we don't have payment method selection yet)
            $('#selectedPaymentId').val('default');
            $('#paymentMethodId').val(1);

            $('#booking_form').data('submitted', true);
            $('#booking_form').submit();

            // Track booking started event
            @auth
                $.ajax({
                    url: "{{ route('booking.v0.track') }}",
                    type: "POST",
                    data: {
                        _token: "{{ csrf_token() }}",
                        property_id: "{{ $property->id }}",
                        booking_stage: "started",
                        price: $('#total').data('total') || "{{ numberFormat($property->propertyPrice->price ?? 0, 2) }}",
                        city: "{{ $property->property_address->city ?? '' }}"
                    }
                });
            @endauth
        }
    }

    // WhatsApp booking functionality
    function openWhatsAppChat() {
        // Get property details
        const propertyId = '{{ $property->id }}';
        const propertyCode = '{{ $property->property_code }}';
        const propertyName = '{{ $property->name }}';
        const checkIn = $('#url_checkin').val();
        const checkOut = $('#url_checkout').val();

        const message = `Hi Darent Team:\nI want to book this property\nProperty Details:\nID: ${propertyId}\nCode: ${propertyCode}\nName: ${propertyName}\nCheck-in: ${checkIn}\nCheck-out: ${checkOut}`;

        // Copy message to clipboard
        if (navigator.clipboard) {
            navigator.clipboard.writeText(message).then(() => {
                // Message copied successfully
            }).catch(() => {
                // Failed to copy message
            });
        }

        // Check if device is Windows
        const isWindows = navigator.userAgent.indexOf('Windows') !== -1;

        if (isWindows) {
            // Show confirmation modal for Windows devices
            showWhatsAppConfirmation(message);
        } else {
            // Direct redirect for non-Windows devices
            const whatsappUrl = `https://wa.me/966920033870?text=${encodeURIComponent(message)}`;
            window.open(whatsappUrl, '_blank');
        }
    }

    function showWhatsAppConfirmation(message) {
        // Create and show the confirmation modal
        const modalHtml = `
            <div class="modal fade" id="whatsappConfirmModal" tabindex="-1" aria-labelledby="whatsappConfirmModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content" style="border-radius: 15px; border: none; box-shadow: 0 10px 30px rgba(0,0,0,0.2);">
                        <div class="modal-header" style="border-bottom: none; padding: 30px 30px 20px;">
                            <h5 class="modal-title" id="whatsappConfirmModalLabel" style="color: #25D366; font-weight: 600; font-size: 1.3rem;">
                                <i class="fab fa-whatsapp" style="margin-right: 10px; font-size: 1.5rem;"></i>
                                @if(app()->getLocale() == 'ar')
                                    تأكيد الانتقال إلى واتساب
                                @else
                                    WhatsApp Confirmation
                                @endif
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body" style="padding: 20px 30px 30px; text-align: center;">
                            <div style="background: linear-gradient(135deg, #25D366 0%, #128C7E 100%); color: white; padding: 20px; border-radius: 12px; margin-bottom: 20px;">
                                <i class="fas fa-check-circle" style="font-size: 3rem; margin-bottom: 15px; opacity: 0.9;"></i>
                                <h6 style="margin: 0; font-size: 1.1rem; font-weight: 500; color: white;">
                                    @if(app()->getLocale() == 'ar')
                                        تم نسخ الرسالة إلى الحافظة!
                                    @else
                                        The message has been copied to the clipboard!
                                    @endif
                                </h6>
                            </div>
                            <p style="color: #666; font-size: 1rem; line-height: 1.6; margin-bottom: 20px;">
                                @if(app()->getLocale() == 'ar')
                                    عند فتح المحادثة، قم بلصق الرسالة وإرسالها.
                                @else
                                    When the conversation opens, paste it and send.
                                @endif
                            </p>
                            <div style="display: flex; gap: 15px; justify-content: center;">
                                <button type="button" class="btn" data-bs-dismiss="modal" style="background: #f8f9fa; color: #666; border: 1px solid #dee2e6; padding: 12px 25px; border-radius: 8px; font-weight: 500;">
                                    @if(app()->getLocale() == 'ar')
                                        إلغاء
                                    @else
                                        Cancel
                                    @endif
                                </button>
                                <button type="button" class="btn" onclick="proceedToWhatsApp('${encodeURIComponent(message)}')" style="background: #25D366; color: white; border: none; padding: 12px 25px; border-radius: 8px; font-weight: 500; box-shadow: 0 4px 12px rgba(37, 211, 102, 0.3);">
                                    <i class="fab fa-whatsapp" style="margin-right: 8px;"></i>
                                    @if(app()->getLocale() == 'ar')
                                        فتح واتساب
                                    @else
                                        Open WhatsApp
                                    @endif
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if any
        $('#whatsappConfirmModal').remove();

        // Add modal to body and show it
        $('body').append(modalHtml);
        $('#whatsappConfirmModal').modal('show');

        // Clean up modal after it's hidden
        $('#whatsappConfirmModal').on('hidden.bs.modal', function () {
            $(this).remove();
        });
    }

    function proceedToWhatsApp(encodedMessage) {
        const whatsappUrl = `https://wa.me/966920033870?text=${encodedMessage}`;
        window.open(whatsappUrl, '_blank');
        $('#whatsappConfirmModal').modal('hide');
    }

    // Tabby installment button functionality
    $(document).on('click', '.tabby-instal-btn', function(e) {
        e.preventDefault();

        // Get the total price from the booking form
        let price = $('#total').data('total') || $('#total').text().replace(/[^\d.-]/g, '');

        if (!price || price <= 0) {
            return;
        }

        // Calculate installment amount (divide by 4 for 4 installments)
        let installmentAmount = (parseFloat(price) / 4).toFixed(2);

        // Update all tabby installment amounts in the modal
        $('.tabby-instal-amount').text(installmentAmount);

        // Show the Tabby modal
        $('#tabby').modal('show');
    });

    // Show skeleton loading
    function showBookingSkeleton() {
        $('#booking-skeleton').show().removeClass('fade-out');
        $('#booking-content').hide().removeClass('fade-in');
    }

    // Hide skeleton and show content
    function hideBookingSkeleton() {
        $('#booking-skeleton').addClass('fade-out');
        setTimeout(function() {
            $('#booking-skeleton').hide();
            $('#booking-content').show().addClass('fade-in');
        }, 300);
    }

    // Initialize booking card
    $(document).ready(function() {
        // Show skeleton initially
        showBookingSkeleton();

        // Load initial prices after a short delay
        setTimeout(function() {
            if ($('.pd-date').val()) {
                getPrice();
            } else {
                // If no dates selected, just show the content without skeleton
                hideBookingSkeleton();
            }
        }, 500);
    });

    // Original getPrice function with skeleton integration
    function getPrice() {
        // Show skeleton while loading
        showBookingSkeleton();

        var dataURL = "{{ route('property.v0.price') }}";
        var token = "{{ csrf_token() }}";
        var property_id = $('#property_id').val();
        var checkin = $('#url_checkin').val();
        var checkout = $('#url_checkout').val();
        var guest_count = $('#number_of_guests').val();

        // ... existing code ...
    }

</script>
