@extends('template')

@push('css')
<style>
    #load-more {
        display: block;
        margin: 20px auto;
        text-align: center;
    }

    .property-loader {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100px;
    }

    .property-loader img {
        width: 50px;
        height: 50px;
    }
    .new-property-card{
        height: max-content;
    }
</style>
@endpush
@php
    $lang = Session::get('language');
    $checkin_session = Session::get('header_checkin');
    $checkout_session = Session::get('header_checkout');

    $adult_guest_session = Session::get('adult_guest_session') == '' ? 1 : Session::get('adult_guest_session');
    $child_guest_session = Session::get('child_guest_session') == '' ? 0 : Session::get('child_guest_session');
    $guest = $child_guest_session + $adult_guest_session;


    $wishlist_modal = true;

    use App\Models\Settings;
    use App\Models\Properties;
    use App\Models\WishlistName;

    $wishlistExist = WishlistName::where('creater_id', Auth::id())
        ->orWhere(DB::raw("FIND_IN_SET('" . Auth::id() . "', share_with)"), '>', 0)
        ->get();
    if (isset($wishlistExist)) {
        $existWishlistModal = true;
        $wishlist_modal = true;
    } else {
        $existWishlistModal = false;
        $wishlist_modal = true;
    }
@endphp

@section('main')


    <section class="home-slider">
        @if (isset($banners))
            @if (app()->getLocale() == 'ar')
                <div class="home-slide for-ar" id="slider-ar">
                @else
                    <div class="home-slide for-eng" id="slider-en">
            @endif

            @foreach ($banners as $key => $banner)
                @if ($banner->banner_type == 'main banner')
                    <div class="slide-main" style="background-image: url({{ asset($banner->image) }});">
                        <div class="container">
                        </div>
                    </div>
                @endif
            @endforeach
            </div>
        @endif


    </section>

    <section class="filter-main">
        <div class="container">
            @include('common.search_filter')
        </div>
    </section>
    <section class="services">
        <div class="container">
            <div class="head">
                <div class="row align-items-center justify-content-between">
                    <div class="col-xl-3 col-lg-3 col-sm-4">
                        <h1 class="loadskull">{{ customTrans('homepage.explore_next_vacation') }}</h1>
                    </div>
                    <div class="col-xl-5 col-lg-6 col-sm-8">
                        <ul class="product-category">
                            @forelse ($property_type as $type)
                                <li class='property_types_image loadskull'>
                                    <a href="{{ URL::to('/') }}/search?property_type={{ $type->id }}">
                                        <span>
                                            <span class="">
                                                <img src="{{ asset('icons/' . $type->icon_image) }}" height="auto"
                                                    width="auto" alt="..." class="">
                                            </span>
                                            <input hidden type="radio" class='property_types_checkbox '
                                                name="property_type[]" value="{{ $type->id }}" />
                                            <p class="">
                                                {{ app()->getLocale() == 'ar' ? $type->name_ar : $type->name }}</p>
                                        </span>
                                    </a>
                                </li>

                            @empty
                                <p>No types available</p>
                            @endforelse
                        </ul>
                    </div>

                    <div class="col-md-2">
                        <div class="view-all text-right">
                            <a href="{{ route('search', ['discoverall' => 'discoverall']) }}">

                                <button class="grey-btn inner-btn loadskull"
                                    id="webEngageBtn">{{ customTrans('homepage.discover_all') }}</button>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="property">
                <div class="row" id="additional-properties">

                    @forelse ($properties['data'] as $property)
                        <x-property-card :property="$property" :url="''" :wishlistExist="$wishlistExist" />
                    
                    @empty
                        <p>No listing found for now</p>
                    @endforelse
                </div>

            </div>
            <div class="property-loader" id="property-loader" style="display: none;">
                <img src="{{ asset('icons/loader.gif') }}" alt="Loading...">
            </div>

            <button id="load-more" class="btn btn-primary">{{ customTrans('utility.load_more') }}</button>
            <div id="book-loader" class="d-none loader-img text-center">
                <img src="{{ asset('icons/loader.gif') }}" alt="loader">
                <h4>{{ app()->getLocale() == 'ar' ? 'Loading...' : ' Loading...' }}</h4>
            </div>
        </div>
    </section>

    {{-- dynamic banner --}}
    {{-- <section class="second-home dubai-ff">
        @if (isset($banners))
            @foreach ($banners as $banner)
                @if ($banner->banner_type == 'sub banner')
                    @if (App::getLocale() == 'ar')
        @endif
                    <img src="{{ asset('public/images/banners/' . $banner->image) }}" alt=""
                        class="sh-banner-img">
                    <div class="sc-home">
                        <h1 class="loadskull back-line pt-1">بيوت عطلات وأكثر!</h1>
                        <p class="loadskull">
                            مجموعة متنوعة من بيوت العطلات اللي تلبي
                            <br>
                            احتياجاتك، من إقامة هادئة إلى تجربة سكنية نابضة.!
                        </p>
                        <div class="sc-home-btn">
                    <a href="{{ route('search') }}">
                        <button
                            class="btn-theme-white loadskull">{{ customTrans('homepage.book_now') }}</button>
                    </a>
                </div>
                    </div>
                @endif
            @endforeach
        @endif

        <div class="container">

            <div class="row">
                <div class="col-md-7">
                    <div class="sc-home">
                        <h1 class="loadskull">{{ customTrans('homepage.second_home') }}</h1>
                        <p class="loadskull">{{ customTrans('homepage.second_home_para') }}</p>
                        <div class="sc-home-btn">
                            <a href="{{ route('search') }}">
                                <button
                                    class="btn-theme-white loadskull">{{ customTrans('homepage.book_now') }}</button>
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-md-5">
                    <div class="sh-banner">
                        <img src="{{ asset('images/appdownload2.png') }}" alt="" class="sh-banner-img">
                    </div>
                    <div class="slide-vertical">



                        <div class="vr-pack sl-1">
                            <div class="vr-slide loadskull">
                                <img src="{{ asset('../images/investor/img-1.png') }}" alt="">
                            </div>
                            <div class="vr-slide loadskull">
                                <img src="{{ asset('../images/investor/img-2.png') }}" alt="">
                            </div>
                            <div class="vr-slide loadskull">
                                <img src="{{ asset('../images/investor/img-3.png') }}" alt="">
                            </div>
                            <div class="vr-slide loadskull">
                                <img src="{{ asset('../images/investor/img-4.png') }}" alt="">
                            </div>
                            <div class="vr-slide loadskull">
                                <img src="{{ asset('../images/investor/img-5.png') }}" alt="">
                            </div>

                        </div>
                        <div class="vr-pack sl-2">
                            <div class="vr-slide loadskull">
                                <img src="{{ asset('../images/investor/img-3.png') }}" alt="">
                            </div>
                            <div class="vr-slide loadskull">
                                <img src="{{ asset('../images/investor/img-5.png') }}" alt="">
                            </div>
                            <div class="vr-slide loadskull">
                                <img src="{{ asset('../images/investor/img-2.png') }}" alt="">
                            </div>
                            <div class="vr-slide loadskull">
                                <img src="{{ asset('../images/investor/img-1.png') }}" alt="">
                            </div>
                            <div class="vr-slide loadskull">
                                <img src="{{ asset('../images/investor/img-4.png') }}" alt="">
                            </div>

                        </div>
                        <div class="vr-pack sl-3">
                            <div class="vr-slide loadskull">
                                <img src="{{ asset('../images/investor/img-1.png') }}" alt="">
                            </div>
                            <div class="vr-slide loadskull">
                                <img src="{{ asset('../images/investor/img-4.png') }}" alt="">
                            </div>
                            <div class="vr-slide loadskull">
                                <img src="{{ asset('../images/investor/img-3.png') }}" alt="">
                            </div>
                            <div class="vr-slide loadskull">
                                <img src="{{ asset('../images/investor/img-5.png') }}" alt="">
                            </div>
                            <div class="vr-slide loadskull">
                                <img src="{{ asset('../images/investor/img-2.png') }}" alt="">
                            </div>

                        </div>


                    </div>
                </div>
            </div>
        </div>
    </section> --}}
    {{-- dynamic banner end --}}
    <section class="second-home dubai-ff loadskull">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <div class="sc-home">
                        <h1 class="loadskull back-line pt-1">
                            <div class="bk-inner loadskull">{{ customTrans('homepage.vacation_rentals_and_more') }}</div>
                        </h1>
                        <p class="loadskull">
                            {{ customTrans('homepage.variety_of_vacation') }}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="what-can-do">
        <div class="container">
            <div class="mb-4">
                <h2 class="fs-30 mb-3 loadskull">{{ customTrans('homepage.what_we_can') }}</h2>
                <h5 class="fw-400 loadskull">{{ customTrans('homepage.we_will_help') }}</h5>

            </div>
            <div>
                <div class="row list-itm">
                    <div class="col-md-4">
                        <div class="do-for-box">
                            <div class="fd-icon loadskull">
                                <img src="{{ asset('icons/fd-check.svg') }}" alt="" class="">
                            </div>
                            <div class="fd-content">
                                <h3 class="loadskull">{{ customTrans('homepage.Instant_guaranteed') }}</h3>
                                <p class="loadskull">{{ customTrans('homepage.Instant_guaranteed_description') }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="do-for-box">
                            <div class="fd-icon loadskull">
                                <img src="{{ asset('icons/fd-hand.svg') }}" alt="" class="">
                            </div>
                            <div class="fd-content">
                                <h3 class="loadskull">{{ customTrans('homepage.safe_payment_method') }}</h3>
                                <p class="loadskull">{{ customTrans('homepage.safe_payment_method_description') }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="do-for-box">
                            <div class="fd-icon loadskull">
                                <img src="{{ asset('icons/fd-outstanding.svg') }}" alt="">
                            </div>
                            <div class="fd-content">
                                <h3 class="loadskull">{{ customTrans('homepage.outstanding_customer_service') }}</h3>
                                <p class="loadskull">
                                    {{ customTrans('homepage.outstanding_customer_service_description') }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="do-for-box">
                            <div class="fd-icon loadskull">
                                <img src="{{ asset('icons/fd-trust.svg') }}" alt="">
                            </div>
                            <div class="fd-content">
                                <h3 class="loadskull">{{ customTrans('homepage.trusted_views') }}</h3>
                                <p class="loadskull">{{ customTrans('homepage.trusted_views_description') }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="do-for-box">
                            <div class="fd-icon loadskull">
                                <img src="{{ asset('icons/fd-advance.svg') }}" alt="">
                            </div>
                            <div class="fd-content">
                                <h3 class="loadskull">{{ customTrans('homepage.advance_intelligent_serach') }}</h3>
                                <p class="loadskull">
                                    {{ customTrans('homepage.advance_intelligent_serach_description') }}</p>
                            </div>
                        </div>
                    </div>
                    {{-- <div class="col-md-4">
                        <div class="do-for-box">
                            <div class="fd-icon loadskull">
                                <img src="{{ asset('icons/fd-photos.svg') }}" alt="">
                            </div>
                            <div class="fd-content">
                                <h3 class="loadskull">{{ customTrans('homepage.photos_videos') }}</h3>
                                <p class="loadskull">{{ customTrans('homepage.photos_videos_description') }}</p>
                            </div>
                        </div>
                    </div> --}}

                </div>
            </div>
        </div>




        {{-- Wishlist Form --}}


        {{-- Wishlits Listing Modal --}}
        <div class="modal fade dubai-ff" id="whishlist-listing" data-bs-backdrop="static" data-bs-keyboard="false"
            tabindex="-1" aria-labelledby="whishlist-listing" aria-hidden="true">
            <div class="modal-dialog custom-modal-dialog modal-dialog-centered modal-dialog-scrollable bt-grey-scroller">
                <div class="modal-content cm-bd-content">
                    <div class="modal-header cm-bd-header">
                        <h5 class="w-100 text-center mb-0" id="whishlist-listing">
                            {{ customTrans('wishlist.your_wishlists') }}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                        </button>
                    </div>
                    <div class="modal-body cm-bd-body">
                        <form>
                            {{ csrf_field() }}
                            <input type="hidden" name="property_id" id="wishlist-property-id" value="">

                            {{-- {{dd($wishlistExist)}} --}}
                            @forelse ($wishlistExist as $wishlist)
                                <div class="wishlist-listing">
                                    @php
                                        $wishlistProperties = $wishlist->wishlistProperties;
                                    @endphp
                                    @if (count($wishlistProperties) > 0)
                                        <div class="wlisting-inner" id="existWishlist"
                                            data-wishlist-name-id="{{ $wishlist->id }}">
                                            <img src="{{ asset($wishlistProperties[0]->property?->getCoverPhotoAttribute()) }}"
                                                alt="">
                                            <h5 class="mb-0 ellipsis-oneline">{{ $wishlist->name }}</h5>
                                        </div>
                                    @else
                                        <div class="wlisting-inner" id="existWishlist"
                                            data-wishlist-name-id="{{ $wishlist->id }}">
                                            <img src="{{ asset('images/default-image-not-exist.png') }}" alt="">
                                            <h5 class="mb-0 ellipsis-oneline">{{ $wishlist->name }}</h5>
                                        </div>
                                    @endif
                                </div>
                            @empty
                                <p class="mb-0 text-center">To save your favorite places
                                    and Experiences to a wishlist.</p>
                            @endforelse
                        </form>
                    </div>
                    <div class="modal-footer cm-bd-footer">
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal"
                            data-bs-target="#create-whishlist">{{ customTrans('wishlist.create_new_wishlist') }}</button>
                    </div>
                </div>
            </div>
            {{-- Wishlits Listing Modal End --}}

    </section>


    <section class="download-app dubai-ff">
        <div class="container">
            <div class="download-app-container">
                <div class="row">
                    <div class="col-md-5">
                        <div class="downApp-img">
                            <img src="{{ asset('images/appdownload.webp') }}" alt="">
                        </div>
                    </div>
                    <div class="col-md-7">
                        <div class="downApp">
                            <div class="downApp-inner">
                                <img src="{{ asset('images/sh-darnt-logo.png') }}" alt="" class="sh-dlogo">
                                {{-- <h2>
                                    <div class="blk-bar"></div><span class="fc-white">DA</span>RENT APP
                                </h2> --}}
                                <h1 class="back-line">{{ customTrans('home.download_app') }} </h1>
                                </h1>
                                <p>{{ customTrans('home.start_your_trip') }} </p>
                                <div class="fea-btn">
                                    <a href="https://apps.apple.com/us/app/darent/id1661536049" class="desktop_bottom_banner" target="_blank">
                                        <button class="ms-0">
                                            <img loading="lazy" src="{{ asset('images/app-store2.png') }}"
                                                height="auto" width="auto" alt="app store icon">
                                        </button>
                                    </a>

                                    <a href="https://play.google.com/store/apps/details?id=com.darent" class="desktop_bottom_banner" target="_blank">
                                        <button>
                                            <img loading="lazy" src="{{ asset('images/googleplay.png') }}"
                                                height="auto" width="auto" alt="play store icon">
                                        </button>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- share modal start-->
    <div class="modal fade" id="single-share-modal" data-bs-backdrop="static"
            data-bs-keyboard="false" tabindex="-1" aria-labelledby="singleShareModalLabel" aria-hidden="true">
            <div class="modal-dialog custom-modal-dialog modal-dialog-centered">
                <div class="modal-content custom-modal-content">
                    <div class="modal-header custom-small-modal-header">
                        <button type="button" class="btn-close" data-bs-dismiss="modal"
                        aria-label="Close">
                        </button>
                    </div>
                    <div class="modal-body">
                        <h4 class="mb-4">
                            {{ customTrans('property_single.share_this_place') }}
                        </h4>
                        <div class="single-share-property">
                        <!-- Image tag will be here -->
                        <img src="" id="share-property-image">
                        <div class="ss-property-content">
                                <p class="mb-0" id="share-property-title"></p>
                            </div>
                        </div>
                        <input type="hidden" id="share-property-url" value="{{ url()->current() }}">
                        <div class="ss-property-btns">
                            <div class="row">
                                <div class="col-md-6">
                                    <button class="transparent-btn" id="copy-link-btn">
                                        <i class="far fa-copy"></i>
                                        {{ customTrans('property_single.copy_link') }}
                                    </button>
                                </div>
                                <div class="col-md-6">
                                    <button class="transparent-btn" id="email-share-btn">
                                        <i class="far fa-envelope"></i>
                                        {{ customTrans('property_single.email') }}
                                    </button>
                                </div>
                                <div class="col-md-6">
                                    <button class="transparent-btn" id="whatsapp-share-btn">
                                        <i class="fab fa-whatsapp"></i>
                                        {{ customTrans('property_single.whatsApp') }}
                                    </button>
                                </div>
                                <div class="col-md-6">
                                    <button class="transparent-btn" id="messenger-share-btn">
                                        <i class="fab fa-facebook-messenger"></i>
                                        {{ customTrans('property_single.messenger') }}
                                    </button>
                                </div>
                                <div class="col-md-6">
                                    <button class="transparent-btn" id="facebook-share-btn">
                                        <i class="fab fa-facebook"></i>
                                        {{ customTrans('property_single.facebook') }}
                                    </button>
                                </div>
                                <div class="col-md-6">
                                    <button class="transparent-btn" id="twitter-share-btn">
                                    <img src="{{asset('icons/twitter.svg')}}" alt="">
                                        {{ customTrans('property_single.twitter') }}
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div id="share-tooltip" class="tooltip-container" style="position: fixed; bottom: 20px; left: 50%; transform: translateX(-50%); z-index: 9999; display: none; opacity: 0; transition: opacity 0.3s ease-in-out;">
                            <div class="tooltip-content" style="background-color: rgba(0, 0, 0, 0.8); color: white; padding: 10px 20px; border-radius: 5px; font-size: 14px; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);">
                                <i class="fas fa-check-circle" style="margin-right: 5px;"></i> <span id="tooltip-message">Link copied to clipboard successfully!</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
    </div>
    <!-- share modal end -->



    {{-- <section class="feature">
    <div class="container">
        <div class="bg">
            <div class="feature-content d-flex">
                <div>
                    <h1 class="">{{ customTrans('homepage.get_feature')}}</h1>
                    <p class="text-white">Download the App</p>
                    <div class="fea-btn">
                        <a href="https://apps.apple.com/us/app/darent/id1661536049" target="_blank">
                            <button class="ms-0">
                                <img loading="lazy" src="{{ asset('images/Group 34.png') }}" height="auto" width="auto" alt="app store icon">
                            </button>
                        </a>

                        <a href="https://play.google.com/store/apps/details?id=com.darent" target="_blank">
                            <button>
                                <img loading="lazy" src="{{ asset('images/play store.png') }}" height="auto" width="auto" alt="play store icon">
                            </button>
                        </a>
                    </div>
                </div>
            </div>
            <div class="d">
                <img src="images/Path 11.png" height="auto" width="auto" alt="...">
            </div>
        </div>
    </div>
</section> --}}





    <div class="whatsapp-icon">
        <a href="https://api.whatsapp.com/send?phone=966533313486&text=Hi">
            <img src="{{ asset('icons/whatsapp.svg') }}" height="auto" width="auto" alt="whatsapp icon">
        </a>
    </div>
@stop
<script>
    // var readmoreText = {{ customTrans('property_single.read_more') }};
</script>
@push('scripts')
    @auth
        <script rel="preload" src="{{ asset('js/sweetalert.min.js') }}" as="script"></script>
    @endauth
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const additionalProperties = document.getElementById('additional-properties');
            const loadMoreButton = document.getElementById('load-more');

            if (additionalProperties.children.length < 8) {
                loadMoreButton.style.display = 'none';
            }
            propertyCardSlider()
            popOverFunction()
        });
        let currentPage = 1;
        const loadMoreButton = document.getElementById('load-more');
        const loader = document.getElementById('property-loader');

        loadMoreButton.addEventListener('click', function() {
            currentPage++;
            let url = `{{ route('recommended.home.properties') }}?page=${currentPage}`;
            loader.style.display = 'flex'; // Show loader


            loadMoreButton.style.display = 'none';
           if(true){
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    const additionalProperties = document.getElementById('additional-properties');
                    additionalProperties.insertAdjacentHTML('beforeend', data.html);
                    $('.loadskull').removeClass('loadskull');
                    $('img.lazy').each(function() {
                        const element = $(this);
                        element.removeClass('lazy').addClass('loaded');
                        element.attr('src', element.data('src'));
                    });
                    loader.style.display = 'none'; // Hide loader

                    // Disable the button if no more pages
                    if (currentPage >= data.last_page) {
                        loadMoreButton.disabled = true;
                        loadMoreButton.style.display = 'none';
                        // loadMoreButton.textContent = 'No more properties to load';
                    }
                });}
        });
    </script>
    <script>
        AOS.init({

            duration: 1200,

        })
    </script>
    <script>
        $(document).ready(function() {
            // Capture the click event on the heart icon
            $('.fav-icon').click(function() {
                // Get the item ID from the data attribute of the heart icon
                var itemId = $(this).data('item-id');
                const elem = this
                // Set the item ID as the value of the hidden input field in the modal
                $('#item_id').val(itemId);
                $('#item_before_discount').val(elem.dataset.beforeDiscount);
                $('#item_total_price').val(elem.dataset.totalPrice);
                $('#item_property_type_name').val(elem.dataset.propertyTypeName);
                $('#item_city_name').val(elem.dataset.cityName);
                $('#item_host_id').val(elem.dataset.hostId);
                $('#item_day_price').val(elem.dataset.dayPrice);
                $('#item_number_of_days').val(elem.dataset.numberOfDays);
                $('#item_property_code').val(elem.dataset.propertyCode);
                $('#wishlist-property-id').val(itemId)

                $('#wishlist-property-id').val(itemId);
            });

            // Clear the hidden input field value when the modal is closed
            $('#create-whishlist').on('hidden.bs.modal', function() {
                $('#item_id').val('');
                $('#wishlist-property-id').val('');
            });
            $('#whishlist-listing').on('hidden.bs.modal', function() {
                $('#wishlist-property-id').val('');
            });

            if ("geolocation" in navigator) {
                navigator.geolocation.getCurrentPosition(function(position) {
                    const userLat = position.coords.latitude;
                    const userLong = position.coords.longitude;

                    // Create an object with the location data
                    const locationData = {
                        lat: userLat,
                        long: userLong
                    };

                    // Send the location data to the server using AJAX
                    $.ajax({
                        type: "GET",
                        url: "{{ route('setLatLong') }}",
                        data: locationData,
                        success: function(response) {
                            if (response.success) {
                                console.log('Location data updated in session successfully');
                            } else {
                                console.error('Failed to update location data in session');
                            }
                        },
                        error: function(error) {
                            console.error('Error:', error);
                        }
                    });
                });
            } else {
                console.error("Geolocation is not available in this browser.");
            }

            function showTooltip(message) {
                $('#tooltip-message').text(message);
                var tooltip = $('#share-tooltip');
                tooltip.css('display', 'block');

                // Use setTimeout to ensure the display property is applied before changing opacity
                setTimeout(function() {
                    tooltip.css('opacity', '1');

                    // Hide tooltip after 2 seconds
                    setTimeout(function() {
                        tooltip.css('opacity', '0');

                        // Hide element after fade out animation completes
                        setTimeout(function() {
                            tooltip.css('display', 'none');
                        }, 300);
                    }, 2000);
                }, 10);
            }

             // Copy link functionality
            $('#copy-link-btn').on('click', function() {
                var propertyUrl = $('#share-property-url').val();
                navigator.clipboard.writeText(propertyUrl).then(function() {
                    showTooltip('Link copied to clipboard successfully!');
                });
            });

            // Email share functionality
            $('#email-share-btn').on('click', function() {
                var propertyUrl = $('#share-property-url').val();
                var propertyName = $('#share-property-title').val();
                var subject = "Check out this property: " + propertyName;
                var body = "I found this amazing property on Darent: " + propertyUrl;
                showTooltip('Opening email client...');
                setTimeout(function() {
                    window.location.href = "mailto:?subject=" + encodeURIComponent(subject) + "&body=" + encodeURIComponent(body);
                }, 500);
            });

            // WhatsApp share functionality
            $('#whatsapp-share-btn').on('click', function() {
                var propertyUrl = $('#share-property-url').val();
                var propertyName = $('#share-property-title').val();
                var text = "Check out this property on Darent: " + propertyName + " " + propertyUrl;
                showTooltip('Opening WhatsApp...');
                window.open('https://wa.me/?text=' + encodeURIComponent(text), '_blank');
            });

            // Facebook share functionality
            $('#facebook-share-btn').on('click', function() {
                var propertyUrl = $('#share-property-url').val();
                showTooltip('Opening Facebook...');
                window.open('https://www.facebook.com/sharer/sharer.php?u=' + encodeURIComponent(propertyUrl), '_blank', 'width=600,height=400');
            });

            // Twitter share functionality
            $('#twitter-share-btn').on('click', function() {
                var propertyUrl = $('#share-property-url').val();
                var propertyName = $('#share-property-title').val();
                var text = "Check out this property on Darent: " + propertyName;
                showTooltip('Opening Twitter...');
                window.open('https://twitter.com/intent/tweet?text=' + encodeURIComponent(text) + '&url=' + encodeURIComponent(propertyUrl), '_blank', 'width=600,height=400');
            });

            // Facebook Messenger share functionality
            $('#messenger-share-btn').on('click', function() {
                var propertyUrl = $('#share-property-url').val();
                showTooltip('Opening Messenger...');
                window.open('https://www.facebook.com/dialog/send?link=' + encodeURIComponent(propertyUrl) + '&app_id=966920033870&redirect_uri=' + encodeURIComponent(propertyUrl), '_blank', 'width=600,height=400');
            });



        });

        $(document).on('click', "#wishlistBtn", function(e) {
            e.preventDefault();
            $(this).addClass('disabled');

            var name = $('#name').val();
            var property_id = $('#item_id').val();
            var token = $('input[name="_token"]').val();
            $.ajax({
                type: "POST",
                url: "{{ route('createWishlist') }}",
                data: {
                    "_token": token,
                    "name": name,
                    "property_id": property_id,
                },
                success: function(data) {
                    if (data == "Success") {
                        $("#create-whishlist").modal("hide");
                        setTimeout(function() {
                            $('#success').modal('show');
                        }, 1500);

                        // window.location.reload();

                        trackEvent('add_to_wishlist', {
                            value: Number.parseFloat($('#item_day_price').val()).toFixed(2),
                            currency: '{{Session::get('currency')}}',
                            discount: Number.parseFloat($('#item_before_discount').val() - $('#item_total_price').val()).toFixed(2),
                            item_type: $('#item_property_type_name').val(),
                            item_city_name: $('#item_city_name').val(),
                            item_host_id: $('#item_host_id').val(),
                            price: Number.parseFloat($('#item_day_price').val()).toFixed(2),
                            quantity: $('#item_number_of_days').val(),
                            total_price: Number.parseFloat($('#item_total_price').val()).toFixed(2),
                        }, 'ga')

                        trackEvent('AddToWishlist', {
                            content_ids: [$('#item_id').val()],
                            contents: [
                                {
                                    'content_id': $('#item_id').val(),
                                    'content_type': 'product',
                                    'content_name': name,
                                }
                            ],
                            currency: '{{Session::get('currency')}}',
                            value:  $('#item_total_price').val(),
                        }, ['tik'])

                        trackEvent('ADD_TO_WISHLIST', {
                            item_ids: [$('#item_property_code').val()],
                            item_category: 'product',
                            number_items: $('#item_number_of_days').val(),
                            price: Number.parseFloat($('#item_total_price').val()).toFixed(2),
                            currency: '{{Session::get('currency')}}',
                            user_email: '{{ auth()->user()?->email }}',
                            user_phone_number: '{{ auth()->user()?->phone }}'
                        }, ['snap'])
                    }

                }
            });
        });
        $(document).on('click', '.successmodalbtn', function(e) {
            window.location.reload(true);
        });
        $(document).on('click', '#existWishlist', function(e) {
            e.preventDefault();
            $(this).addClass('disabled');
            var property_id = $('#wishlist-property-id').val();
            var wishlist_name_id = $(this).data('wishlist-name-id');
            var token = $('input[name="_token"]').val();
            $.ajax({
                    type: "POST",
                    url: "{{ route('addRemoveWishlist') }}",
                    data: {
                        "_token": token,
                        "wishlist_name_id": wishlist_name_id,
                        "property_id": property_id,
                    },
                    success: function(data) {
                        if (data.msg == "Success") {
                            $('#whishlist-listing').modal('hide');
                            //-----------WebEngage Integration------------(Verified)
                            let user = DEFAULT_USER;
                            // let comment = DEFAULT_USER;
                            let authcheck = '{{ auth()->check() }}';
                            if (authcheck) {
                                // user_id = "{{ Auth::id() }}";
                                @auth
                                var isHost = @json(auth()->user()->is_host);
                            @endauth
                            user = isHost == true ? 'Host' : DEFAULT_USER;
                        }

                        payload = {
                            "Name": data.property.name,
                            "Unit Code": data.property.property_code,
                            "Cost Per Night": data.property.property_price.price,
                            "Category Name": data.property.property_type.name,
                            "User": user
                        };
                        webEngageTracking(MARK_AS_WISHLIST, payload);
                        //-----------WebEngage Integration------------
                        window.location.reload();

                    }

                }
            });
        });
        $(document).on('click', '#toggleWishlist', function(e) {
            let authcheck = '{{ auth()->check() }}';
            if (authcheck) {
                e.preventDefault();
                $(this).addClass('disabled');
                // var property_id = $('#wishlist-property-id').val();
                var user_id = "{{ Auth::id() }}";
                var property_id = $(this).data('item-id');
                var $icon = $(this);
                var token = $('input[name="_token"]').val();

                $.ajax({
                    type: "POST",
                    url: "{{ route('toggleWishlist') }}",
                    data: {
                        "_token": token,
                        "user_id": user_id,
                        "property_id": property_id,
                    },
                    success: function(data) {
                        if (data.msg == "Success") {
                            $icon.removeAttr('id');
                            $icon.find('.heart_icon').removeClass('active');
                            $icon.find('.heart_icon').attr('data-status', 0);
                            $icon.attr('data-bs-target',
                                "{{ isset($wishlistExist) ? '#whishlist-listing' : '#create-whishlist' }}"
                            );
                            $icon.removeClass('disabled');
                            // window.location.reload();
                        }

                    }
                });
            }


        });
        $(document).on('click', '#guestWishlist', function() {
            $('#staticBackdrop').modal('show');
        });

        $(document).on('click','.share-property-btn',function(e){
            e.preventDefault()
            var propertyTypeName = $(this).data('property-type-name');
            var cityName = $(this).data('city-name');
            var bedrooms = $(this).data('bedrooms');
            var bathrooms = $(this).data('bathrooms');
            var beds = $(this).data('beds');
            var rating = $(this).data('rating');
            var image = $(this).data('image');
            let propertyTitleShareModal = propertyTypeName + ' in ' + cityName + ' · ' + rating + bedrooms + ' bedroom · ' + beds + ' bed · ' + bathrooms + ' bath';
            // console.log(propertyTitleShareModal);
            $('#share-property-image').attr('src', image);
            $('#share-property-title').text(propertyTitleShareModal);
        })


        $(document).on('click', '.user-book', function(e) {
            var hostid = $(this).data('hostid');
            getprofiledata(hostid);
        });

        // book modal js
        // $(document).ready(function() {
        //     $('#md-profile-book').on('show.bs.modal', function() {
        //         $('.user-book').hide();
        //     });

        //     $('#md-profile-book').on('hidden.bs.modal', function() {
        //         $('.user-book').show();
        //     });
        // });

        function getprofiledata(hostid) {

            $.ajax({
                data: {
                    'hostid': hostid,
                },
                url: "{{ route('getprofiledata') }}",
                type: 'get',
                dataType: 'json',
                beforeSend: function() {
                    show_loader();
                },
                success: function(res) {
                    $('.hostName').text(res.hostName);
                    $('.hostImage').attr("src", res.hostimage);
                    $('.hostRating').text(res.hostRating);
                    $('.hostReviewsCount').text(res.hostReviewsCount);
                    $('.duration').text(res.duration);
                    $('.reviewsContent').html(res.hostreviews);
                    $('.listingdata').html(res.listings);
                    if (res.location == 'notset') {
                        $('.location-li').addClass('d-none');
                    } else {
                        $('.location-li').removeClass('d-none');
                        $('.location').html(res.location);
                    }

                    if (res.about == 'notset') {
                        $('.about').addClass('d-none');
                    } else {
                        $('.about').removeClass('d-none');
                        $('.about').html(res.about);
                    }


                    hide_loader();
                    $('#md-profile-book').modal('show');

                },
                error: function(request, error) {
                    hide_loader();
                    alert(error);

                },
                complete: function() {
                    hide_loader();
                }
            });

        }


        function show_loader() {
            $('#book-loader').removeClass('d-none');
            //    $('.property').removeClass('d-none');
        }

        function hide_loader() {
            $('#book-loader').addClass('d-none');
        }
        // let propertySwiper = new Swiper('.host-slider', {
        //     slidesPerView: 1,
        //     slidesToScroll: 1,
        //     slidesPerGroup: 1,
        //     spaceBetween: 10,
        //     navigation: {
        //         nextEl: '.swiper-button-hp-next',
        //         prevEl: '.swiper-button-hp-prev',
        //     },
        //     pagination: {
        //         el: '.swiper-pagination',
        //         clickable: true,
        //     },
        // });
    </script>

    {{-- <script type="text/javascript">
        document.addEventListener("DOMContentLoaded", function() {
            var sendHelloButton = document.getElementById("webEngageBtn");

            sendHelloButton.addEventListener("click", function() {
                // Use the WebEngage track function to send the "hello" event
                if (typeof webengage !== "undefined") {
                    webengage.track("hello", {
                        message: "Hello, WebEngage!"
                    });
                    console.log("message sent");
                }
            });
        });
    </script> --}}
@endpush
