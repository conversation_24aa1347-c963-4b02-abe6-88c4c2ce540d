@props(['property', 'url' => '', 'dis' => '#property_location' , 'wishlistExist'])

@php


use App\Models\PropertyFees;
    $propertyFees = PropertyFees::pluck('value', 'field');

    $checkin = null;
    $checkout = null;


    // Parse the URL query string
    $queryString = parse_url($url, PHP_URL_QUERY);
    if ($queryString) {
        parse_str($queryString, $params);
        $checkin = $params['checkin'] ?? null;
        $checkout = $params['checkout'] ?? null;
    }

@endphp

<div class="col-lg-4 col-md-6 col-12">
    <div class="new-property-card">
        <div class="np-image-container loadskull">
            <div class="swiper property-card-slider @if((isset($property->available) && !$property->available) || $property->max_nights < request()->searchNights) not-available-overlay @endif">
                <div class="swiper-wrapper">
                    @if($property->cover_photo)
                        <div class="swiper-slide">
                            <a href="{{ route('property.single', ['slug' => $property->slug]).$url }}"
                                aria-label="{{ $property->name }}" target="_blank" class="webEngageBtn">
                                <div class="np-property-image">
                                    <img src="{{ asset('images/loader.gif') }}"
                                        class="card-img-top lazy"
                                        data-src="{{ asset($property->cover_photo) }}"
                                        loading="lazy"
                                        alt="{{ $property->name }}">
                                </div>
                            </a>
                        </div>
                    @endif
                    @foreach($property->property_photos as $photo)
                        @if(!$photo->cover_photo)
                            <div class="swiper-slide">
                                <a href="{{ route('property.single', ['slug' => $property->slug]).$url }}"
                                    aria-label="{{ $property->name }}" target="_blank" class="webEngageBtn">
                                    <div class="np-property-image">
                                        <img src="{{ asset('images/loader.gif') }}"
                                            class="card-img-top lazy"
                                            data-src="{{ asset($photo->photo) }}"
                                            loading="lazy"
                                            alt="{{ $property->name }}">
                                    </div>
                                </a>
                            </div>
                        @endif
                    @endforeach
                </div>
                <div class="swiper-pagination"></div>
                <div class="swiper-button-hp-next"></div>
                <div class="swiper-button-hp-prev"></div>
            </div>

            <div class="np-badge-overlay">
                <span class="np-badge badge-view">
                    <img src="{{ asset('icons/white-eye.svg') }}" alt="Views Icon">
                    {{ App\Http\Helpers\Common::formatProductViewCount(($property->userPropertyView->count() + 100)) }}
                </span>
                @if(in_array($property->property_code, \App\Models\Properties::EXCLUSIVE_PROPERTIES))
                    <span class="np-badge badge-exclusive">
                        <img src="{{ asset('icons/bg-black-trophy.svg') }}" alt="Exclusive Icon">
                        {{ customTrans('property_single.exclusive') }}
                    </span>
                @elseif((isset($property->available) && !$property->available) || $property->max_nights < request()->searchNights)
                    <span class="np-badge badge-not-available">
                        <img src="{{ asset('icons/red-exclamation-circle.svg') }}" alt="Not Available Icon">
                        {{ customTrans('general.not_available') }}
                    </span>
                @endif
            </div>

            <div class="np-action-icons">
                <button class="icon-btn fav-icon"
                        data-bs-toggle="modal"
                        @auth
                            @if($property->wishlist == true)
                                id="toggleWishlist"
                            @else
                                data-bs-target="{{ isset($wishlistExist) ? '#whishlist-listing' : '#create-whishlist' }}"
                            @endif
                        @endauth
                        @guest
                            id="guestWishlist"
                        @endguest
                        data-item-id="{{ $property->id }}"
                        data-before-discount="{{ $property->before_discount }}"
                        data-total-price="{{ $property->total_price }}"
                        data-property-type-name="{{ $property->property_type_name }}"
                        data-city-name="{{ $property->city_name }}"
                        data-host-id="{{ $property->host_id }}"
                        data-day-price="{{ $property->day_price }}"
                        data-number-of-days="{{ $property->number_of_days }}"
                        data-property-code="{{ $property->property_code }}">
                    <div class="fav-in {{ $property->wishlist == true ? 'active' : '' }} heart_icon"
                        data-status="{{ $property->wishlist }}"
                        data-id="{{ $property->id }}">
                        <img src="{{ asset('icons/black-heart.svg') }}" height="auto" width="auto" alt="heart" class="fav-blank">
                        <img src="{{ asset('icons/filled-heart-icon.png') }}" height="auto" width="auto" alt="heart" class="fav-fill">
                    </div>
                </button>
                <button class="icon-btn share-property-btn" data-bs-toggle="modal"
                    data-property-type-name="{{ $property->property_type_name }}"
                    data-city-name="{{ $property->city_name }}"
                    data-bedrooms="{{ $property->bedrooms }}"
                    data-bathrooms="{{ $property->bathrooms }}"
                    data-beds="{{ $property->beds }}"
                    data-image="{{isset($property->cover_photo) ? asset($property->cover_photo) : asset('images/default-image.png')}}"
                    data-rating="{{ isset($property->rating) ? '★' . number_format($property->rating, 2) . ' · ' : '' }}"
                    data-bs-target="#single-share-modal">
                    <img src="{{ asset('icons/latest-share.svg') }}" alt="Share Icon">
                </button>
            </div>

            <div class="property-features-badges pusher_badge_{{ $property->id }}">
                    <div class="pf-badge pf-badge-high-demand {{ $property->badge == 'high_demand' ? 'd-block' : 'd-none' }}">
                        <img src="{{ asset('icons/high-demand.svg') }}" alt="High Demand Icon">
                        <span>{{ customTrans('general.high_demand') }}</span>
                    </div>
                    <div class="pf-badge pf-badge-top-feature {{ $property->badge == 'top_feature' ? 'd-block' : 'd-none' }}">
                        <img src="{{ asset('icons/top-feature.svg') }}" alt="Top Feature Icon">
                        <span>{{ customTrans('general.top_feature') }}</span>
                    </div>
            </div>
        </div>

        <div class="np-card-body">
            <div class="np-features">
                @if(isset($property->bedrooms) && $property->bedrooms > 0)
                <span class="loadskull">
                    <img src="{{ asset('icons/bedrooms.svg') }}" alt="Beds Icon">
                    {{ digitsToArabic($property->bedrooms) }}
                </span>
                @endif
                @if(isset($property->beds) && $property->beds > 0)
                <span class="loadskull">
                    <img src="{{ asset('icons/beds.svg') }}" alt="Beds Icon">
                    {{ digitsToArabic($property->beds) }}
                </span>
                @endif
                @if(isset($property->bathrooms) && $property->bathrooms > 0)
                <span class="loadskull">
                    <img src="{{ asset('icons/bathes.svg') }}" alt="Bathes Icon">
                    {{ digitsToArabic($property->bathrooms) }}
                </span>
                @endif
            </div>

            <div class="np-card-title-container loadskull">
                <h5 class="np-card-title">
                    <a href="{{ route('property.single', ['slug' => $property->slug]).$url }}"
                        aria-label="{{ $property->name }}" target="_blank"
                        class="webEngageBtn">
                        {{ propertyTitleForListing($property) }}
                    </a>
                </h5>
                <div class="np-rating">
                    <img src="{{ asset('icons/Rate.svg') }}" alt="Star Icon">
                    @if (isset($property->is_new_lable) && $property->is_new_lable)
                        <div class="np-rating">
                            <span>{{ customTrans('host_dashboard.new') }}</span>
                        </div>
                    @else
                        <div class="np-rating-inner d-flex align-items-center gap-1">
                            <span class="np-total-rating">{{ digitsToArabic($property->average_rating ?? '0') }}</span>
                        <span class="np-total-rate">({{  digitsToArabic(isset($property->reviews_count) && !empty($property->reviews_count) ? $property->reviews_count : 0) }})</span>
                        </div>
                    @endif
                </div>
            </div>

            <div class="np-location loadskull">
                <a href="{{ route('property.single', ['slug' => $property->slug]).$url.$dis }}" target="_blank">
                    <img src="{{ asset('icons/location-marker.svg') }}" alt="Location Icon">
                    <span>
                        @if(app()->getLocale() == 'ar')
                            {{ isset($property->property_address->district_ar) ? $property->property_address->district_ar . ',' : $property->property_address->district . ',' }}
                            {{ isset($property->property_address->city_ar) ? $property->property_address->city_ar : $property->property_address->city }}
                        @else
                            {{ isset($property->property_address->district) ? $property->property_address->district . ',' : '' }}
                            {{ isset($property->property_address->city) ? $property->property_address->city : '' }}
                        @endif
                    </span>
                </a>
            </div>

            <div class="np-prices loadskull">
                <div class="np-price-left">
                    <div class="np-price-per-night">
                        @if($property->has_daily_discount || $property->property_price?->weekly_discount > 0 || $property->property_price?->monthly_discount > 0)
                        <div class='np-price-discount-per'>
                            @if(($property->property_price?->weekly_discount > 0 || $property->property_price?->monthly_discount > 0) && $property->price_details['monthly_weekly_discount_percentage'] > 0)
                            <span>-{{ $property->price_details['monthly_weekly_discount_percentage'].'%' ?? '' }} </span>
                            @elseif($property->has_daily_discount && $property->price_details['daily_discount_percent'] > 0)
                            <span>-{{ $property->price_details['daily_discount_percent'] ?? '0' }}%</span>
                            @endif
                        </div>
                        @endif
                        <span></span>
                        <div>
                            <span class="np-total-price" id="property-night-price">
                                {{ $property->price_details['per_night_price'] }}
                                <img src="{{ asset('icons/black-riyal-symbol.svg') }}" alt="Riyal Icon">
                            </span>
                            <span>{{ customTrans('property_single.per_night') }}</span>
                        </div>
                    </div>
                    <div class="total-price-main-desktop">
                        <a href="#" class="total-price-link popover-trigger"
                                data-property_id = "{{ $property->id }}" data-platform_id = "{{ $property->platform_id }}"
                                data-checkin="{{ $checkin ?? '' }}"
                                data-checkout="{{ $checkout ?? '' }}"
                                data-bs-toggle="popover"
                                data-bs-custom-class="price-details-popover" data-bs-html="true" title=""
                                data-bs-content="
                                <div class='popover-header-custom'>
                                    <span>{{ customTrans('reservation.price_details') }}</span>
                                    <a type='button' class='btn-close btn-sm btn-cancel-popover' aria-label='Close'></a>
                                </div>
                                <div class='popover-body-custom'>
                                    <div class='price-row'>
                                        <span><span class='property-per-night-price'>{{ $property->price_details['per_night_price'] }}</span> <img class='riyal-symbol' src='{{ asset('icons/black-riyal-symbol.svg') }}' alt='Riyal Icon'><span class='property-number-of-days'> × {{ $property->price_details['total_nights'] }} {{ customTrans('search.nights') }}</span></span>
                                        <span><span class='property-total-night-price'>{{ $property->price_details['total_base_night_price'] }}</span> <img class='riyal-symbol' src='{{ asset('icons/black-riyal-symbol.svg') }}' alt='Riyal Icon'></span>
                                    </div>
                                    <div class='price-row'>
                                        <span>{{ customTrans('property_single.service_fee') }}</span>
                                        <span class='property-service-fee'>{{ number_format($property->price_details['service_fee_with_discount'], 2) }} <img class='riyal-symbol' src='{{ asset('icons/black-riyal-symbol.svg') }}' alt='Riyal Icon'></span>
                                    </div>
                                    @if($property->has_daily_discount || $property->property_price?->weekly_discount > 0 || $property->property_price?->monthly_discount > 0)
                                    <div class='price-row'>
                                        @if(($property->property_price?->weekly_discount > 0 || $property->property_price?->monthly_discount > 0) && $property->price_details['monthly_or_weekly_discount'] > 0)
                                        <span>Discount ({{ $property->price_details['total_nights'] >= 28 && $property->property_price?->monthly_discount > 0 ? customTrans('property_card.monthly_discount') : ($property->price_details['total_nights'] >= 7 && $property->property_price?->weekly_discount > 0 ? customTrans('property_card.weekly_discount') : '') }}) ({{ $property->price_details['monthly_weekly_discount_percentage'].'%' ?? '' }})</span>
                                        <span class='discount property-discount'>-{{ number_format($property->price_details['monthly_or_weekly_discount'], 2) }} <img class='riyal-symbol' src='{{ asset('icons/green-riyal-symbol.svg') }}' alt='Riyal Icon'></span>
                                        @elseif($property->has_daily_discount && $property->price_details['daily_discount_amount'] > 0)
                                        <span>{{ customTrans('property_card.daily_discount') }} (-{{ $property->price_details['daily_discount_percent'] ?? '0' }}%)</span>
                                        <span class='discount property-discount'>-{{ number_format($property->price_details['daily_discount_amount'], 2) }} <img class='riyal-symbol' src='{{ asset('icons/green-riyal-symbol.svg') }}' alt='Riyal Icon'></span>
                                        @endif
                                    </div>
                                    @endif
                                    <div class='price-row total-row'>
                                        <span>{{ customTrans('listing.total_price') }}</span>
                                        <span>
                                        <span class='property-total-price'>{{ number_format($property->price_details['total_with_discount'], 2) }}</span> <img class='riyal-symbol' src='{{ asset('icons/black-riyal-symbol.svg') }}' alt='Riyal Icon'></span></span>
                                    </div>
                                </div>
                            ">
                                <span>{{ customTrans('listing.total_price') }}</span>
                                <span class="tp-link-amount"><span>{{number_format($property->price_details['total_with_discount'], 2) }}</span>
                                <img src="{{ asset('icons/gray-riyal-symbol.svg') }}" alt="Riyal Icon"></span></span>
                            </a>

                    </div>
                    <div class="total-price-main-mobile">
                        <a href="#" class="total-price-link" data-bs-toggle="offcanvas"
                            data-bs-target="#priceDetailsOffcanvas_{{$property->id }}" aria-controls="priceDetailsOffcanvas_{{$property->id }}">
                            <span>{{ customTrans('listing.total_price') }}</span>
                            <span><span class="tp-link-amount property-total-price-mobile">{{ number_format($property->price_details['total_with_discount'], 2) }}</span>
                            <img src="{{ asset('icons/gray-riyal-symbol.svg') }}" alt="Riyal Icon"></span>
                        </a>

                    </div>
                </div>
                @php
                    $hasWeeklyDiscount = $property->property_price?->weekly_discount > 0 ?? false;
                    $hasMonthlyDiscount = $property->property_price?->monthly_discount > 0 ?? false;
                    $hasDailyDiscount = $property->has_daily_discount ?? false;
                    $discountText = '';

                    if ($property->price_details['total_nights'] >= 28 && $hasMonthlyDiscount) {
                        $discountText = "{{ customTrans('property_card.monthly') }}";
                        if ($hasWeeklyDiscount) {
                            $discountText .= " . {{ customTrans('property_card.weekly') }}";
                        } elseif ($hasDailyDiscount) {
                            $discountText .= " . {{ customTrans('property_card.daily') }}";
                        }
                    } elseif ($property->price_details['total_nights'] >= 7 && $hasWeeklyDiscount) {
                        $discountText = $hasDailyDiscount ? 'Weekly . Daily' : 'Weekly';
                    } elseif ($property->price_details['total_nights'] == 1 && $property->has_daily_discount) {
                        $discountText = 'Daily';
                    }
                @endphp

                <!-- @if($discountText != '')

                    <div class="price-after-discount">
                        <div class="pa-discount-icon">
                            <img src="{{ asset('icons/white-discount-tag.svg') }}" alt="Discount Icon">
                        </div>
                        <div class="pa-discount-text">
                            <p class="mb-0">{{ customTrans('host_dashboard.discounts') }}</p>
                            <p class="mb-0 pa-dt-text">{{ $discountText }}</p>
                        </div>
                    </div>
                @endif -->
            </div>
        </div>
    </div>
</div>

<!-- Price Details Offcanvas for Mobile -->
<div class="offcanvas offcanvas-bottom price-details-offcanvas" data-bs-scroll="false" tabindex="-1" id="priceDetailsOffcanvas_{{$property->id }}"
    aria-labelledby="offcanvasLabel">
    <div class="offcanvas-header">
        <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close">
            <img src="{{ asset('icons/bg-gray-cancel.svg') }}" alt="Close Icon">
        </button>
    </div>
    <div class="offcanvas-body">
        <h5 class="offcanvas-title" id="offcanvasLabel">{{ customTrans('reservation.price_details') }}</h5>
        <div class="price-row">
            <span>{{ $property->price_details['per_night_price'] }} <img src="{{ asset('icons/black-riyal-symbol.svg') }}" class="riyal-symbol"
                    alt="Riyal Icon"> x {{ $property->price_details['total_nights'] }} {{ customTrans('search.nights') }}</span>
            <span>{{ $property->price_details['total_base_night_price'] }} <img src="{{ asset('icons/black-riyal-symbol.svg') }}" class="riyal-symbol"
                    alt="Riyal Icon"></span>
        </div>
        <div class="price-row">
            <span>{{ customTrans('property_single.service_fee') }}</span>
            <span>{{ number_format($property->price_details['service_fee_with_discount'], 2) }} <img src="{{ asset('icons/black-riyal-symbol.svg') }}" class="riyal-symbol"
                    alt="Riyal Icon"></span>
        </div>


        @if($hasWeeklyDiscount || $hasMonthlyDiscount || $property->has_daily_discount)
        <div class="price-row">
            <span>{{ $discountText }}</span>
            <span class="discount">-{{ number_format($property->price_details['monthly_or_weekly_discount'], 2) }} <img src="{{ asset('icons/green-riyal-symbol.svg') }}"
                    class="riyal-symbol" alt="Riyal Icon"></span>
        </div>
        @endif
        <div class="price-row total-row">
            <span>{{ customTrans('listing.total_price') }}</span>
            <span>
                <span>{{ number_format($property->price_details['total_with_discount'], 2) }} <img src="{{ asset('icons/black-riyal-symbol.svg') }}" class="riyal-symbol"
                    alt="Riyal Icon"></span>
            </span>
        </div>
    </div>
</div>
