{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "repositories": {"ibrahim-sakr/laravel-otp": {"url": "https://github.com/ibrahim-sakr/laravel-otp.git", "type": "vcs"}, "eburonmedia/laravel-google-translate": {"url": "https://github.com/eburonmedia/laravel-google-translate", "type": "vcs"}}, "require": {"php": "^8.2", "ext-zip": "*", "barryvdh/laravel-dompdf": "*", "bensampo/laravel-enum": "^6.2", "eluceo/ical": "^0.16.1", "fingerprint/fingerprint-pro-server-api-sdk": "^6.1", "geniusts/hijri-dates": "^1.2", "google/apiclient": "^2.14", "google/cloud-bigquery": "^1.31", "google/cloud-logging": "*", "google/cloud-translate": "*", "guzzlehttp/guzzle": "^7.2", "intervention/image": "^2.4", "joggapp/laravel-google-translate": "dev-master", "kutia-software-company/larafirebase": "dev-master", "laravel/framework": "^11.0", "laravel/passport": "^12.0", "laravel/prompts": "^0.1.13", "laravel/sanctum": "^4.0", "laravel/socialite": "^5.5", "laravel/tinker": "^2.9", "maatwebsite/excel": "^3.1", "mpdf/mpdf": "^8.2", "owen-it/laravel-auditing": "^14.0", "phpmailer/phpmailer": "^6.1", "pusher/pusher-php-server": "^7.0", "sadiqsalau/laravel-otp": "dev-main", "sentry/sentry-laravel": "^4.10", "simplesoftwareio/simple-qrcode": "^4.2", "spatie/laravel-data": "*", "spatie/laravel-google-cloud-storage": "^2.3", "spatie/laravel-html": "*", "staudenmeir/laravel-cte": "^1.0", "yajra/laravel-datatables-buttons": "^11.0", "yajra/laravel-datatables-oracle": "^11.0"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.8", "fakerphp/faker": "^1.9.1", "laravel/pint": "^1.13", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^8.0", "phpunit/phpunit": "^10.0", "plannr/laravel-fast-refresh-database": "^1.2", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Modules\\": "Mo<PERSON>les/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true}}, "minimum-stability": "dev", "prefer-stable": true}